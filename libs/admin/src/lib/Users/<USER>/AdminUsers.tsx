import { Auth<PERSON>I } from '@optimize/api';
import { ContentLayout, PaginatedTable } from '@optimize/components';
import { optimizePageRoutes, tableQueryPrefixes } from '@optimize/constants';
import { MANAGE_USER_PERMISSIONS, useCheckAllPermissions } from '@optimize/hooks';
import { Button, Card, H5, Row, Space, theme } from '@rxbenefits/ui';
import { ReactNode } from 'react';
import { Link } from 'react-router-dom';

import { userColumns } from './UserColumns';

export const AdminUsers = () => {
  const isAddUserEnabled = useCheckAllPermissions(MANAGE_USER_PERMISSIONS.ADD);
  const isImportEnabled = useCheckAllPermissions(MANAGE_USER_PERMISSIONS.IMPORTDNUSER);

  const apiClient = async (queryParams) => await AuthAPI.users.list({ queryParams });

  const AdminUsersTitle: ReactNode = (
    <Row style={{ alignItems: 'center', justifyContent: 'space-between' }} gutter={1}>
      <H5 style={{ marginBottom: `${theme.spaces.s0}` }} data-testid="table-heading--addEditUsers">
        Users
      </H5>
      <Space>
        {isImportEnabled && (
          <Button ghost data-testid="button--importDNUser">
            <Link to={optimizePageRoutes.adminImport}>Import DataNet User</Link>
          </Button>
        )}
        {/* {isAddUserEnabled && (
          <Button ghost data-testid="button--addNewUser" style={{ marginRight: '1px' }}>
            <Link to={optimizePageRoutes.adminCreateUser}>Create User</Link>
          </Button>
        )} */}
      </Space>
    </Row>
  );

  return (
    <ContentLayout>
      <Card title={AdminUsersTitle} bodyStyle={{ padding: `${theme.spaces.s0}` }}>
        <PaginatedTable
          tableSearchFields={[
            {
              groupKey: 'tableSearch',
              dividerStyle: { display: 'none' },
              groupColumnSpan: 8,
              fields: [
                {
                  label: 'Search for a User',
                  toolTip: '',
                  fieldName: 'name',
                  type: 'input',
                  inputProps: {
                    placeholder: 'Name',
                    id: 'user-search-name-input',
                  },
                },
                {
                  label: 'Email',
                  fieldName: 'username',
                  type: 'input',
                  inputProps: {
                    placeholder: 'Email',
                    id: 'user-search-email',
                  },
                },
                {
                  label: 'User Status',
                  fieldName: 'active',
                  type: 'select',
                  inputProps: {
                    defaultActiveFirstOption: true,
                    defaultValue: '',
                    options: [
                      { label: 'All', value: '' },
                      { label: 'Active', value: 'true' },
                      { label: 'Deactivated', value: 'false' },
                    ],
                    id: 'user-search-active',
                    placeholder: 'All',
                    filterSort: () => 1,
                  },
                },
              ],
            },
          ]}
          verticallyAlignCells="top"
          dataService={apiClient}
          columns={userColumns()}
          rowKey="id"
          tableIdentifier={tableQueryPrefixes.user}
        />
      </Card>
    </ContentLayout>
  );
};
