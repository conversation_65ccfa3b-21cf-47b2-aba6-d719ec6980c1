import { validationRules } from '@optimize/constants';
import { DEV_PERMISSIONS, useCheckAnyPermissions, useDeveloper } from '@optimize/hooks';
import { AuthOrganizationAccess, AuthUserAccess, RolesResponse, UserType } from '@optimize/types';
import { groupArrayBy } from '@optimize/utils';
import { FieldGroupProps, FieldProps, Text, theme, TransferData } from '@rxbenefits/ui';
import { ReactNode } from 'react';

import { filterTransferOptions, RoleGroupMappings } from '../../Utils';
import { GenerateRoleProps, GroupSelection, RoleGroup } from '../Components';
const generateRoleGrouping = (roleGroup: GenerateRoleProps): FieldGroupProps => {
  return {
    groupKey: RoleGroupMappings[roleGroup.title].title,
    groupColumnSpan: 24,
    CustomComponent: (
      <RoleGroup
        handleRoleChange={roleGroup.handleRoleChange}
        roles={roleGroup.roles}
        title={RoleGroupMappings[roleGroup.title].title}
        tooltip={RoleGroupMappings[roleGroup.title].tooltip}
        extra={RoleGroupMappings[roleGroup.title].extra}
        selectedRoleIds={roleGroup.selectedRoleIds}
      />
    ),
    sectionStyle: { paddingLeft: theme.spaces.s12, paddingRight: theme.spaces.s12 },
  };
};

export const UserDetailFieldGroups = (
  userAccess: AuthUserAccess | undefined,
  userType: UserType | undefined,
  hasOrgAccess: boolean,
  organizationAccess: 'all' | 'select' | undefined,
  userOrganizations: TransferData[] | undefined,
  selectedOrgs: string[] | undefined,
  roles: RolesResponse[] | undefined,
  selectedRoles: number[],
  editUserRoles: number[],
  editUserOrgs: AuthOrganizationAccess[],
  handleBenefitGroupUpdates: (orgId: string, data: 'all' | string[]) => void,
  handleRoleUpdates: (id: number, checked: boolean) => void
) => {
  const isDeveloper = useDeveloper();

  const OrgComponent: ReactNode =
    selectedOrgs &&
    selectedOrgs.map((orgID) => (
      <GroupSelection
        preSelectedGroups={editUserOrgs
          .find((org) => String(org.id) === orgID)
          ?.benefitGroups.map((group) => String(group.id))}
        handleGroupChange={handleBenefitGroupUpdates}
        userHasRestrictedBenefitGroups={
          userAccess?.organizations.find((org) => String(org.id) === orgID)?.restrictBenefitGroups
        }
        editUserHasAllGroups={
          !editUserOrgs.find((org) => String(org.id) === orgID)?.restrictBenefitGroups
        }
        key={orgID}
        orgID={orgID}
      />
    ));

  // Group roles by group, converts to Map
  const grouped = roles ? groupArrayBy(roles, (role) => role.group) : [];

  // Init containing array for role groupings
  const roleGroupData: GenerateRoleProps[] = [];

  const isKeystoneDev = useCheckAnyPermissions(DEV_PERMISSIONS.KEYSTONE);

  // Iterate map of grouped arrays and return UI needed values
  grouped.forEach((data: RolesResponse[], title) => {
    const getRoles = () => {
      return data
        .filter((el) => {
          // THIS IS WHERE FILTERING ROLES BASED ON PERMISSIONS HAPPENS FOR UPDATE ONLY
          // Sadly can not be based on ID as this changes per env
          // potential improvement is to have name typed to be any of the possible names of a role, but thats a lot
          if (el.name === 'Keystone Dev') return isKeystoneDev;
          return true;
        })
        .map((el) => {
          return {
            label: el.name,
            id: el.id,
            isProtected: el.protected,
          };
        });
    };

    roleGroupData.push({
      title,
      handleRoleChange: handleRoleUpdates,
      roles: getRoles(),
      selectedRoleIds: editUserRoles,
    });
  });

  if (!userOrganizations) userOrganizations = [];

  const fieldGroups: FieldGroupProps[] = [
    {
      groupTitle: 'User Detail',
      sectionStyle: { paddingLeft: '12px', paddingRight: '12px' },
      dividerStyle: {
        paddingLeft: 'unset',
        paddingRight: 'unset',
      },
      fields: [
        {
          fieldName: 'firstName',
          type: 'input',
          label: 'First Name',
          isRequired: true,
          rules: validationRules.firstName,
        },
        {
          fieldName: 'lastName',
          type: 'input',
          label: 'Last Name',
          isRequired: true,
          rules: validationRules.lastName,
        },
        {
          fieldName: 'phone',
          type: 'phone',
          label: 'Phone',
          fieldColumnSpan: 8,
        },
        {
          fieldName: 'extension',
          type: 'input',
          label: 'Extension',
          fieldColumnSpan: 4,
        },
        {
          fieldName: 'username',
          type: 'input',
          label: 'Email',
          isRequired: true,
          inputProps: {
            disabled: true,
          },
        },
      ],
    },
    {
      groupTitle: (
        <>
          <Text
            style={{
              fontWeight: theme.fontWeight.semiBold,
              color: theme.colors.basePallete.darkGray,
            }}
          >
            User Permission
          </Text>
          {selectedRoles.length === 0 && <Text>&nbsp;(0/1 required roles)</Text>}
        </>
      ),
      groupKey: 'userPermission',
      fields: [],
      dividerStyle: { opacity: '0.5' },
    },
  ];

  roleGroupData
    .filter((e) => {
      if (isDeveloper || !(e.title === 'i-developer')) {
        return RoleGroupMappings[e.title];
      } else {
        return false;
      }
    })
    .map(generateRoleGrouping)
    .forEach((roleGroup) => {
      fieldGroups.push(roleGroup);
    });

  // ORIGINAL ORGANIZATIONS CODE - COMMENTED OUT
  /*
  if (userType === UserType.CLIENT) {
    fieldGroups.push({
      groupTitle: 'Organizations',
      groupColumnSpan: 24,
      fields: [
        {
          fieldName: 'organization',
          type: 'transfer',
          label: 'Choose Organizations',
          inputProps: {
            dataSource: userOrganizations,
            showSearch: true,
            pagination: { pageSize: 200 },
            listStyle: { height: '100%', width: '47%' },
            style: { height: '50vh' },
            titles: ['All Available Organizations', 'Selected Organizations'],
            filterOption: filterTransferOptions,
            sortByTitle: true,
          },
        },
      ],
    });
  } else {
    if (userType === UserType.BROKER || (userType === UserType.INTERNAL && !hasOrgAccess)) {
      fieldGroups.push({
        groupTitle: 'Organizations',
        groupColumnSpan: 24,
        fields: [
          {
            fieldName: 'organization',
            type: 'transfer',
            label: 'Choose Organizations',
            inputProps: {
              dataSource: userOrganizations,
              showSearch: true,
              pagination: { pageSize: 200 },
              listStyle: { height: '100%', width: '47%' },
              style: { height: '50vh' },
              titles: ['All Available Organizations', 'Selected Organizations'],
              filterOption: filterTransferOptions,
              sortByTitle: true,
            },
          },
        ],
      });
      // INTERNAL AND RxBAdmin
    } else if (userType === UserType.INTERNAL && hasOrgAccess) {
      const internalFields: FieldProps[] = [
        {
          fieldName: 'organizationAccess',
          type: 'radio',
          label: 'Organizations Access',
          isRequired: true,
          inputProps: {
            options: [
              { label: 'All Organizations', value: 'all' },
              { label: 'Select Organizations', value: 'select' },
            ],
          },
        },
      ];
      if (organizationAccess === 'select')
        internalFields.push({
          fieldName: 'organization',
          type: 'transfer',
          label: 'Choose Organizations',
          inputProps: {
            pagination: { pageSize: 200 },
            dataSource: userOrganizations,
            showSearch: true,
            listStyle: { height: '100%', width: '47%' },
            style: { height: '50vh' },
            titles: ['All Available Organizations', 'Selected Organizations'],
            filterOption: filterTransferOptions,
            sortByTitle: true,
          },
        });
      fieldGroups.push({
        groupTitle: 'Organizations',
        groupColumnSpan: 24,
        fields: internalFields,
      });
    }
  }
  */

  // Organizations section - feature no longer available
  fieldGroups.push({
    groupTitle: 'Organizations',
    groupColumnSpan: 24,
    fields: [
      {
        fieldName: 'organization',
        type: 'custom',
        label: '',
        CustomComponent: () => (
          <div
            style={{
              padding: '8px 0',
              color: '#666',
              fontStyle: 'italic',
              fontSize: '14px',
            }}
          >
            This feature is no longer available in this application. Please access{' '}
            <a
              href="https://admin-portal.rxbenefits.com/"
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#0066cc', textDecoration: 'underline' }}
            >
              Admin Portal
            </a>{' '}
            to make changes.
          </div>
        ),
      },
    ],
  });

  if (selectedOrgs && selectedOrgs.length > 0) {
    fieldGroups.push({
      groupKey: 'groups',
      groupTitle: 'Groups',
      groupColumnSpan: 24,
      CustomComponent: OrgComponent,
    });
  }

  return fieldGroups;
};
