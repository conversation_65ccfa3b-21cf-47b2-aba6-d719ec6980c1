#!/bin/bash

# =======================================================================
# FEATURE BRANCH MANAGER SCRIPT
# =======================================================================
# 
# PURPOSE:
# This script provides two main workflows for developers:
# 
# WORKFLOW 1: Cherry-pick into current branch
# - Skip branch creation and go directly to cherry-picking
# - Use when you're already on the correct branch
# 
# WORKFLOW 2: Create new feature branch and cherry-pick
# - Full branch creation workflow followed by cherry-picking
# - Choose between hotfix or release branch types
# - Enter project name for branch naming
# - Select from the latest 6 branches OR manually enter branch name
# - Create new feature branch with naming: feature/{version}-{project-name}
# - Cherry-pick commits with conflict resolution support
# 
# USAGE:
# ./branch-manager.sh
# 
# REQUIREMENTS:
# - Must be run from within a git repository
# - Git remote branches must be available
# - User must have permissions to create branches and cherry-pick
# 
# BRANCH NAMING CONVENTION:
# feature/{version}-{project-name}
# Examples: feature/v1.73.8-ben-admin, feature/v1.74.0-optimization
# 
# CONFLICT RESOLUTION:
# When cherry-pick conflicts occur, the script pauses and allows the user
# to resolve conflicts manually, then continue or abort the process.
# 
# HOW TO FIND GIT HASHES FROM GITHUB PULL REQUESTS:
# =======================================================================
# To cherry-pick specific commits, you'll need their git commit hashes.
# Here's how to find them in the GitHub UI:
# 
# METHOD 1 - FROM A PULL REQUEST:
# 1. Navigate to the GitHub repository
# 2. Go to "Pull requests" tab
# 3. Find and click on the PR you want to cherry-pick from
# 4. Click on the "Commits" tab within the PR
# 5. You'll see a list of all commits in that PR
# 6. Each commit shows:
#    - Commit message
#    - Author and timestamp  
#    - 7-character short hash (e.g., "a1b2c3d") on the right
#    - Click the short hash to see the full commit details
# 7. On the commit detail page, the full hash is shown in the URL and
#    at the top right (e.g., "a1b2c3d4e5f6789...")
# 8. Copy the full hash (or you can use the 7-character short version)
# 
# METHOD 2 - FROM COMMIT HISTORY:
# 1. Go to the main repository page
# 2. Click on "X commits" (where X is the number) above the file list
# 3. Browse through commits or use search/filter
# 4. Click on any commit to see its hash
# 5. The hash appears in the URL and at the top of the commit page
# 
# METHOD 3 - FROM FILES CHANGED:
# 1. In a PR, go to "Files changed" tab
# 2. Each file section shows which commit made those changes
# 3. Click on the commit hash next to the filename
# 4. This takes you to the specific commit with its full hash
# 
# TIPS:
# - You can use either the full hash (40 characters) or short hash (7+ chars)
# - Copy multiple hashes before running this script for efficiency
# - Commits are listed chronologically - older commits first
# - Use descriptive commit messages to identify the right commits
# - If cherry-picking multiple commits, do them in chronological order
# 
# ⚠️  IMPORTANT: CHERRY-PICKING BEHAVIOR
# Each commit contains only the changes made at that specific point in time.
# If a PR has multiple commits, cherry-picking ONLY the last commit will NOT
# include all the changes from that PR. You must cherry-pick ALL commits
# from the PR in chronological order to get the complete set of changes.
# 
# Example: PR with commits A → B → C
# - Cherry-picking only C = you get only C's changes
# - To get full PR = cherry-pick A, then B, then C
# 
# CHERRY-PICK METHODS AVAILABLE IN THIS SCRIPT:
# =======================================================================
# 
# 🌟 RECOMMENDED: METHOD 3 - Merge Commit (Easiest for full PRs)
# - Cherry-picks the merge commit that contains all PR changes
# - Gets the ENTIRE PR in one operation - no need for multiple hashes!
# - Only works if PR was merged with "Create a merge commit"
# - Find merge hash: Go to merged PR in GitHub, search for "merged commit",
#   the hash to the right of that text is what you need
# - Note: Requires -m 1 flag (handled automatically by script)
# 
# ALTERNATIVE METHODS FOR SPECIFIC USE CASES:
# 
# METHOD 2: Commit Range  
# - Format: first-hash..last-hash or first-hash^..last-hash
# - Gets all commits between first and last (inclusive)
# - Use when: You want a sequence of commits from a PR but no merge commit exists
# - Example: abc123^..def456 (includes abc123 through def456)
# 
# METHOD 1: Individual Commits
# - Enter each commit hash one by one
# - Use when: You only want specific commits from a PR (selective cherry-picking)
# - Most manual but gives you precise control over which commits to include
# 
# AUTHOR: Generated for optimize.ui development team
# =======================================================================

# Colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to print colored text
print_color() {
    echo -e "${1}${2}${NC}"
}

# Function to print header
print_header() {
    echo
    print_color $CYAN "=================================="
    print_color $CYAN "    Feature Branch Manager"
    print_color $CYAN "=================================="
    echo
}

# Function to get latest branches
get_latest_branches() {
    local branch_type=$1
    local count=$2
    
    git branch -r | grep "origin/${branch_type}/" | \
    sed "s/.*origin\/${branch_type}\///" | \
    grep -E "v?[0-9]+\.[0-9]+\.[0-9]+" | \
    sort -V | tail -n $count | sort -Vr
}



# Function to handle cherry-pick conflicts
handle_conflict() {
    print_color $RED "⚠️  MERGE CONFLICT DETECTED!"
    print_color $YELLOW "Please resolve the conflicts in your editor, then:"
    print_color $WHITE "  - Stage your changes: git add ."
    print_color $WHITE "  - Press 1 to continue"
    print_color $WHITE "  - Press 0 to abort"
    
    while true; do
        read -p "$(print_color $CYAN 'Choose (1/0): ')" choice
        case $choice in
            1)
                                 git cherry-pick --continue
                 if [ $? -eq 0 ]; then
                     print_color $GREEN "✅ Cherry-pick continued successfully!"
                    return 0
                else
                    handle_conflict
                fi
                ;;
            0)
                                 git cherry-pick --abort
                 print_color $YELLOW "❌ Cherry-pick aborted"
                return 1
                ;;
            *)
                print_color $RED "Invalid choice. Please enter 1 or 0."
                ;;
        esac
    done
}

# Function to perform cherry-pick
do_cherry_pick() {
    local hash=$1
    print_color $BLUE "🍒 Cherry-picking: $hash"
    
    git cherry-pick $hash
    if [ $? -ne 0 ]; then
        handle_conflict
        return $?
    else
        print_color $GREEN "✅ Cherry-pick successful!"
        return 0
    fi
}

# Function to check if branch exists
branch_exists() {
    local branch_name=$1
    git show-ref --verify --quiet "refs/remotes/origin/$branch_name" || git show-ref --verify --quiet "refs/heads/$branch_name"
}

# Main script starts here
print_header

# Step 1: Ask what they want to do
print_color $YELLOW "What would you like to do?"
print_color $WHITE "1. Cherry-pick into current branch"
print_color $WHITE "2. Create new feature branch and cherry-pick"
echo

while true; do
    read -p "$(print_color $CYAN 'Choose (1/2): ')" action_choice
    case $action_choice in
        1)
            ACTION="cherry-pick"
            break
            ;;
        2)
            ACTION="create-branch"
            break
            ;;
        *)
            print_color $RED "Invalid choice. Please enter 1 or 2."
            ;;
    esac
done

print_color $GREEN "Selected: $ACTION"
echo

if [ "$ACTION" = "cherry-pick" ]; then
    # Skip to cherry-picking section
    print_color $BLUE "Current branch: $(git branch --show-current)"
    print_color $YELLOW "Ready to cherry-pick into current branch!"
    echo
else
    # Continue with branch creation flow
    # Step 2: Ask if hotfix or release
    print_color $YELLOW "What type of branch are you working with?"
    print_color $WHITE "1. Hotfix"
    print_color $WHITE "2. Release"
    echo

    while true; do
        read -p "$(print_color $CYAN 'Choose (1/2): ')" branch_choice
        case $branch_choice in
            1)
                BRANCH_TYPE="hotfix"
                break
                ;;
            2)
                BRANCH_TYPE="release"
                break
                ;;
            *)
                print_color $RED "Invalid choice. Please enter 1 or 2."
                ;;
        esac
    done

        print_color $GREEN "Selected: $BRANCH_TYPE"
    echo

    # Step 3: Ask for project name
    print_color $YELLOW "What project are you associated with?"
    read -p "$(print_color $CYAN 'Project name: ')" PROJECT_NAME

    if [ -z "$PROJECT_NAME" ]; then
        print_color $RED "Project name cannot be empty!"
        exit 1
    fi

    print_color $GREEN "Project: $PROJECT_NAME"
    echo

    # Step 4: Get and display latest branches
    print_color $BLUE "📋 Fetching latest branches..."
    git fetch --all > /dev/null 2>&1

    # Get latest branches of selected type only
    SELECTED_BRANCHES=()
    while IFS= read -r line; do
        [[ -n "$line" ]] && SELECTED_BRANCHES+=("$line")
    done < <(get_latest_branches "$BRANCH_TYPE" 6)

    # Show how many branches found
    print_color $GREEN "✅ Found ${#SELECTED_BRANCHES[@]} $BRANCH_TYPE branches"

    # Check if we found any branches
    if [ ${#SELECTED_BRANCHES[@]} -eq 0 ]; then
        print_color $RED "❌ No $BRANCH_TYPE branches found!"
        print_color $YELLOW "Make sure you're in a git repository with remote branches."
        exit 1
    fi



    # Step 5: Let user choose branch
    while true; do
        # Display available branches
        print_color $YELLOW "Available $BRANCH_TYPE branches to branch off from:"
        for i in "${!SELECTED_BRANCHES[@]}"; do
            print_color $WHITE "  $((i+1)). $BRANCH_TYPE/${SELECTED_BRANCHES[i]}"
        done
        print_color $WHITE "  $((${#SELECTED_BRANCHES[@]}+1)). Type branch name manually"
        echo

        read -p "$(print_color $CYAN 'Choose option: ')" choice

        # Check if it's a number for branch selection
        if [[ "$choice" =~ ^[0-9]+$ ]]; then
            if [ "$choice" -ge 1 ] && [ "$choice" -le "${#SELECTED_BRANCHES[@]}" ]; then
                # Selected from list
                SELECTED_BRANCH="$BRANCH_TYPE/${SELECTED_BRANCHES[$((choice-1))]}"
                break
            elif [ "$choice" -eq "$((${#SELECTED_BRANCHES[@]}+1))" ]; then
                # Manual input option
                read -p "$(print_color $CYAN 'Enter branch name (e.g., release/v1.73.8 or hotfix/v1.73.9): ')" manual_branch
                if [ -n "$manual_branch" ]; then
                    if branch_exists "$manual_branch"; then
                        SELECTED_BRANCH="$manual_branch"
                        break
                    else
                        print_color $RED "❌ Branch '$manual_branch' not found!"
                        print_color $YELLOW "Please try again..."
                        echo
                    fi
                else
                    print_color $RED "Branch name cannot be empty!"
                    echo
                fi
            else
                print_color $RED "Invalid selection. Please enter a number between 1 and $((${#SELECTED_BRANCHES[@]}+1))."
                echo
            fi
        else
            print_color $RED "Invalid input. Please enter a number."
            echo
        fi
    done

    print_color $GREEN "Selected branch: $SELECTED_BRANCH"
    echo

    # Step 6: Create new feature branch
    # Extract version number from selected branch
    VERSION=$(echo "$SELECTED_BRANCH" | grep -oE "v?[0-9]+\.[0-9]+\.[0-9]+")
    NEW_BRANCH="feature/${VERSION}-${PROJECT_NAME}"

    print_color $BLUE "🌿 Creating new branch: $NEW_BRANCH"
    git checkout $SELECTED_BRANCH
    if [ $? -ne 0 ]; then
        print_color $RED "Failed to checkout $SELECTED_BRANCH"
        exit 1
    fi

    git checkout -b $NEW_BRANCH
    if [ $? -ne 0 ]; then
        print_color $RED "Failed to create branch $NEW_BRANCH"
        exit 1
    fi

    print_color $GREEN "✅ Successfully created and switched to: $NEW_BRANCH"
    echo
fi

# Cherry-pick section (works for both paths)
print_color $YELLOW "🍒 Time for cherry-picking!"
print_color $WHITE "Choose your cherry-pick method:"
print_color $GREEN "1. 🌟 Merge commit (RECOMMENDED - gets entire PR in one operation)"
print_color $WHITE "2. Commit range (e.g., abc123..def456)"
print_color $WHITE "3. Individual commits (enter hashes one by one)"
echo

read -p "$(print_color $CYAN 'Choose method (1/2/3): ')" method_choice

case $method_choice in
    1)
        # Merge commit method (NOW OPTION 1 - RECOMMENDED)
        print_color $WHITE "Enter the merge commit hash from the PR:"
        print_color $YELLOW "TIP: Go to your merged PR in GitHub and search for 'merged commit'."
        print_color $YELLOW "The hash shown to the right of that text is what you need."
        read -p "$(print_color $CYAN 'Merge commit hash: ')" merge_hash
        
        if [ -n "$merge_hash" ]; then
            print_color $BLUE "🍒 Cherry-picking merge commit: $merge_hash"
            git cherry-pick -m 1 $merge_hash
            if [ $? -ne 0 ]; then
                handle_conflict
            else
                print_color $GREEN "✅ Merge commit cherry-pick successful!"
            fi
        fi
        ;;
    2)
        # Commit range method
        print_color $WHITE "Enter commit range (format: first-hash..last-hash or first-hash^..last-hash):"
        read -p "$(print_color $CYAN 'Commit range: ')" commit_range
        
        if [ -n "$commit_range" ]; then
            print_color $BLUE "🍒 Cherry-picking range: $commit_range"
            git cherry-pick $commit_range
            if [ $? -ne 0 ]; then
                handle_conflict
            else
                print_color $GREEN "✅ Range cherry-pick successful!"
            fi
        fi
        ;;
    3)
        # Individual commit method (NOW OPTION 3)
        print_color $WHITE "Enter git commit hashes one at a time (press Enter with empty input to finish):"
        echo
        while true; do
            read -p "$(print_color $CYAN 'Git hash (or press Enter to finish): ')" git_hash
            
            if [ -z "$git_hash" ]; then
                break
            fi
            
            # Validate hash format (basic check)
            if [[ ! "$git_hash" =~ ^[a-fA-F0-9]{6,40}$ ]]; then
                print_color $RED "Invalid git hash format. Please enter a valid commit hash."
                continue
            fi
            
            # Perform cherry-pick
            if ! do_cherry_pick "$git_hash"; then
                print_color $YELLOW "Cherry-pick process stopped."
                break
            fi
            echo
        done
        ;;
    *)
        print_color $RED "Invalid choice. Defaulting to merge commit method."
        # Fall back to merge commit method
        print_color $WHITE "Enter the merge commit hash from the PR:"
        print_color $YELLOW "TIP: Go to your merged PR in GitHub and search for 'merged commit'."
        print_color $YELLOW "The hash shown to the right of that text is what you need."
        read -p "$(print_color $CYAN 'Merge commit hash: ')" merge_hash
        
        if [ -n "$merge_hash" ]; then
            print_color $BLUE "🍒 Cherry-picking merge commit: $merge_hash"
            git cherry-pick -m 1 $merge_hash
            if [ $? -ne 0 ]; then
                handle_conflict
            else
                print_color $GREEN "✅ Merge commit cherry-pick successful!"
            fi
        fi
        ;;
esac

if [ "$ACTION" = "create-branch" ]; then
    print_color $GREEN "🎉 Branch creation and cherry-picking complete!"
    print_color $YELLOW "Current branch: $(git branch --show-current)"
    print_color $BLUE "You're all set to continue working on your feature!"
else
    print_color $GREEN "🎉 Cherry-picking complete!"
    print_color $YELLOW "Current branch: $(git branch --show-current)"
    print_color $BLUE "Your commits have been successfully cherry-picked!"
fi 