import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  ListItem,
  Text,
  UnorderedList,
  useToast,
  VStack,
} from '@chakra-ui/react';
import React, { useRef, useState } from 'react';

import { FileUploadProps } from '../../../lib/types/uploadPriceRecord';

const FileUpload = ({ onUploadSuccess }: FileUploadProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  //   const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    setError(null);

    if (!files || files.length === 0) {
      setSelectedFile(null);
      return;
    }

    const file = files[0];
    // Validate file type
    const validTypes = [
      '.xlsx',
      '.xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
    ];
    const isValidType = validTypes.some((type) =>
      type.startsWith('.')
        ? file.name.toLowerCase().endsWith(type)
        : file.type === type
    );

    if (!isValidType) {
      setError('Please select a valid Excel file (.xlsx or .xls)');
      setSelectedFile(null);
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      setError(
        `File size of "${file.name}" is too large. Please upload a file smaller than 10MB.`
      );
      setSelectedFile(null);
      return;
    }

    setSelectedFile(file);
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    try {
      // Read file as base64
      setIsUploading(true);

      const fileReader = new FileReader();

      fileReader.readAsDataURL(selectedFile);

      fileReader.onload = async () => {
        try {
          // Call your API Gateway endpoint
          const base64File = (fileReader.result as string).split(',')[1];
          const response = await fetch('/api/pricing/upload', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              file_content: base64File,
              bucket_name: 'edpm-upload-price-records',
              file_name: selectedFile.name,
              productId: 'EDPM',
              fileSize: selectedFile.size,
            }),
          });

          if (response.ok) {
            toast({
              title: 'File uploaded successfully',
              description: `File "${selectedFile.name}" has been uploaded and is ready for processing.`,
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
            if (onUploadSuccess) {
              onUploadSuccess();
            }
            setSelectedFile(null);
            setIsUploading(false);
          } else {
            throw new Error('Upload failed');
          }
        } catch (error) {
          toast({
            title: 'Upload failed',
            description: 'Error uploading file. Please try again.',
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
          setIsUploading(false);
        }
      };
    } catch (error) {
      toast({
        title: 'File Read Error',
        description: 'Could not read the file',
        variant: 'destructive',
      });
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      // Simulate file input change with the dropped file
      if (fileInputRef.current) {
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInputRef.current.files = dataTransfer.files;
        // Manually trigger an onChange event
        const event = new Event('change', { bubbles: true });
        fileInputRef.current.dispatchEvent(event);
      }
    }
  };

  return (
    <Box p={5} borderWidth="1px" borderRadius="lg" boxShadow="sm">
      <Text fontSize="xl" fontWeight="bold" mb={4}>
        Upload Product Excel File
      </Text>

      <Alert status="info" mb={4}>
        <AlertIcon />
        <VStack align="start" spacing={2} width="full">
          <Text fontWeight="bold">Excel File Requirements:</Text>
          <Text>
            Your Excel file must include columns with the following prefixes:
          </Text>
          <UnorderedList pl={4}>
            <ListItem>
              <Text as="span" fontWeight="medium">
                Metadata_
              </Text>{' '}
              - For product metadata fields (e.g., Metadata_EffectiveDate,
              Metadata_ExpiryDate, Metadata_PriceRecordName)
              <Text fontSize="sm" color="gray.600" mt={1}>
                {`Note: ExpiryDate values can be empty/null. They will be treated as "never expires".`}
              </Text>
            </ListItem>
            <ListItem>
              <Text as="span" fontWeight="medium">
                Parameter_
              </Text>{' '}
              - For product parameter fields (e.g.,
              Parameter_PharmacyBenefitsManager)
            </ListItem>
            <ListItem>
              <Text as="span" fontWeight="medium">
                ProductValue_
              </Text>{' '}
              - For product value fields (e.g.,
              ProductValue_RetailBrandDiscount)
            </ListItem>
          </UnorderedList>
        </VStack>
      </Alert>

      <Box
        p={6}
        borderWidth="2px"
        borderRadius="md"
        borderStyle="dashed"
        borderColor="gray.300"
        bg="gray.50"
        mb={4}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        textAlign="center"
      >
        <FormControl isInvalid={!!error}>
          <FormLabel htmlFor="file-upload" cursor="pointer" m={0}>
            <Flex
              direction="column"
              align="center"
              justify="center"
              h="100%"
              py={6}
            >
              <Text mb={2} fontWeight="normal">
                Drag and drop your Excel file here, or{' '}
                <strong>click to browse</strong>
              </Text>
              <Text fontSize="sm" color="gray.500">
                Accepted formats: .xlsx, .xls
              </Text>
              {selectedFile && (
                <Text mt={4} fontWeight="medium" color="blue.400">
                  Selected: {selectedFile.name}
                </Text>
              )}
            </Flex>
          </FormLabel>
          <Input
            id="file-upload"
            type="file"
            accept=".xlsx, .xls"
            onChange={handleFileChange}
            ref={fileInputRef}
            display="none"
          />
          {error && <FormErrorMessage>{error}</FormErrorMessage>}
        </FormControl>
      </Box>

      {/* {isUploading && (
        <Progress 
          value={uploadProgress} 
          size="sm" 
          colorScheme="blue" 
          mb={4} 
          borderRadius="md"
        />
      )} */}

      <Button
        onClick={handleFileUpload}
        colorScheme="blue"
        isLoading={isUploading}
        loadingText="Uploading..."
        isDisabled={!selectedFile || isUploading}
        width="full"
      >
        Upload
      </Button>
    </Box>
  );
};

export default FileUpload;
