import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalO<PERSON>lay,
  VStack,
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

import { ParameterDependency } from '../../../lib/types/parameter';
import FormItem from '../../ui/FormItem';
import { useToast } from '../../ui/toast';

interface DependencyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (dependency: Partial<ParameterDependency>) => Promise<void>;
  dependency: ParameterDependency | null;
  isLoading: boolean;
}

interface FormState {
  controlling_parameter_id: string;
  condition_type: 'equals' | 'not_equals' | 'in' | 'not_in';
  condition_value: string;
  effect: 'show' | 'hide' | 'enable' | 'disable';
  pbm_id: string;
  effective_from: string;
  effective_to: string;
}

interface FormErrors {
  controlling_parameter_id?: string;
  condition_value?: string;
  effective_from?: string;
  effective_to?: string;
}

const initialFormState: FormState = {
  controlling_parameter_id: '',
  condition_type: 'equals',
  condition_value: '',
  effect: 'show',
  pbm_id: '',
  effective_from: new Date().toISOString().split('T')[0],
  effective_to: '',
};

export default function DependencyModal({
  isOpen,
  onClose,
  onSave,
  dependency,
  isLoading,
}: DependencyModalProps) {
  // State
  const [formState, setFormState] = useState<FormState>(initialFormState);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [parametersFetched, setParametersFetched] = useState(false);
  const [availableParameters, setAvailableParameters] = useState<
    Array<{
      parameter_id: string;
      name: string;
    }>
  >([]);

  const { toast } = useToast();
  // Load available parameters
  useEffect(() => {
    const fetchParameters = async () => {
      try {
        const response = await fetch(`/api/pricing/parameter`);
        if (!response.ok) throw new Error('Failed to fetch parameters');
        const data = await response.json();
        setAvailableParameters(data);
        setParametersFetched(true);
      } catch (error) {
        console.error('Error fetching parameters:', error);
        toast({
          title: 'Error',
          description: 'Failed to load available parameters',
          variant: 'error',
        });
      }
    };

    if (isOpen && !parametersFetched) {
      fetchParameters();
    }
  }, [isOpen, parametersFetched, toast]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (dependency) {
        setFormState({
          controlling_parameter_id: dependency.controlling_parameter_id,
          condition_type: dependency.condition_type,
          condition_value: dependency.condition_value,
          effect: dependency.effect,
          pbm_id: dependency.pbm_id || '',
          effective_from: dependency.effective_from.split('T')[0],
          effective_to: dependency.effective_to
            ? dependency.effective_to.split('T')[0]
            : '',
        });
      } else {
        setFormState(initialFormState);
      }
      setErrors({});
      setTouched({});
    }
  }, [isOpen, dependency]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formState.controlling_parameter_id) {
      newErrors.controlling_parameter_id = 'Controlling parameter is required';
    }

    if (!formState.condition_value.trim()) {
      newErrors.condition_value = 'Condition value is required';
    }

    if (!formState.effective_from) {
      newErrors.effective_from = 'Effective from date is required';
    }

    if (
      formState.effective_to &&
      formState.effective_from &&
      new Date(formState.effective_to) <= new Date(formState.effective_from)
    ) {
      newErrors.effective_to =
        'Effective to date must be after effective from date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormState((prev) => ({
      ...prev,
      [name]: value,
    }));
    setTouched((prev) => ({
      ...prev,
      [name]: true,
    }));
  };

  const handleSubmit = async () => {
    const allTouched = Object.keys(formState).reduce(
      (acc, key) => ({ ...acc, [key]: true }),
      {}
    );
    setTouched(allTouched);

    if (!validateForm()) {
      return;
    }

    const saveData = {
      ...formState,
      id: dependency?.id,
      effective_to: formState.effective_to || null,
    };

    await onSave(saveData);
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {dependency ? 'Edit Dependency' : 'Add Dependency'}
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={4}>
            <FormItem
              isRequired
              isInvalid={
                touched.controlling_parameter_id &&
                !!errors.controlling_parameter_id
              }
              label="Controlling Parameter"
              name="controlling_parameter_id"
              value={formState.controlling_parameter_id}
              onChange={handleChange}
              placeholder="Select parameter"
              fieldType="select"
              errorMessage={errors.controlling_parameter_id}
              options={availableParameters.map((param) => (
                <option key={param.parameter_id} value={param.parameter_id}>
                  {param.name}
                </option>
              ))}
            />

            <HStack width="100%" spacing={4}>
              <FormItem
                label="Condition Type"
                name="condition_type"
                value={formState.condition_type}
                onChange={handleChange}
                fieldType="select"
                options={[
                  <option key="equals" value="equals">
                    Equals (=)
                  </option>,
                  <option key="not_equals" value="not_equals">
                    Not Equals (≠)
                  </option>,
                  <option key="in" value="in">
                    In List (∈)
                  </option>,
                  <option key="not_in" value="not_in">
                    Not In List (∉)
                  </option>,
                ]}
                formControlProps={{ flex: 1 }}
              />

              <FormItem
                isRequired
                isInvalid={touched.condition_value && !!errors.condition_value}
                label="Condition Value"
                name="condition_value"
                value={formState.condition_value}
                onChange={handleChange}
                placeholder="Enter value"
                fieldType="input"
                errorMessage={errors.condition_value}
                formControlProps={{ flex: 1 }}
              />
            </HStack>

            <HStack width="100%" spacing={4}>
              <FormItem
                label="Effect"
                name="effect"
                value={formState.effect}
                onChange={handleChange}
                fieldType="select"
                options={[
                  <option key="show" value="show">
                    Show
                  </option>,
                  <option key="hide" value="hide">
                    Hide
                  </option>,
                  <option key="enable" value="enable">
                    Enable
                  </option>,
                  <option key="disable" value="disable">
                    Disable
                  </option>,
                ]}
                formControlProps={{ flex: 1 }}
              />

              <FormItem
                label="PBM ID (Optional)"
                name="pbm_id"
                value={formState.pbm_id}
                onChange={handleChange}
                placeholder="Enter PBM ID"
                fieldType="input"
                formControlProps={{ flex: 1 }}
              />
            </HStack>

            <HStack width="100%" spacing={4}>
              <FormItem
                isRequired
                isInvalid={touched.effective_from && !!errors.effective_from}
                label="Effective From"
                name="effective_from"
                fieldFormat="date"
                value={formState.effective_from}
                onChange={handleChange}
                errorMessage={errors.effective_from}
                fieldType="input"
              />

              <FormItem
                isInvalid={touched.effective_to && !!errors.effective_to}
                label="Effective To"
                name="effective_to"
                fieldFormat="date"
                value={formState.effective_to}
                onChange={handleChange}
                min={formState.effective_from}
                errorMessage={errors.effective_to}
                fieldType="input"
              />
            </HStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={isLoading}
            size="sm"
          >
            {dependency ? 'Save Changes' : 'Add Dependency'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
