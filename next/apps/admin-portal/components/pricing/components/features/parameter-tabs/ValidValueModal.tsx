import { useUser } from '@auth0/nextjs-auth0/client';
import {
  <PERSON>ton,
  HStack,
  Modal,
  ModalBody,
  ModalClose<PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay,
  VStack,
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

import { ParameterValidValue } from '../../../lib/types/parameter';
import FormItem from '../../ui/FormItem';
import { useToast } from '../../ui/toast';

interface ValidValueModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (value: Partial<ParameterValidValue>) => Promise<void>;
  value: ParameterValidValue | null;
  parameterId: string; // Add parameterId prop
  isLoading?: boolean;
  availablePbms?: string[];
}

interface FormState {
  value: string;
  label: string;
  description: string;
  pbm_id: string;
  effective_from: string;
  effective_to: string;
}

interface FormErrors {
  value?: string;
  label?: string;
  effective_from?: string;
  effective_to?: string;
}

const initialFormState: FormState = {
  value: '',
  label: '',
  description: '',
  pbm_id: '',
  effective_from: new Date().toISOString().split('T')[0],
  effective_to: '',
};

export default function ValidValueModal({
  isOpen,
  onClose,
  onSave,
  value,
  parameterId,
  isLoading = false,
  availablePbms = [],
}: ValidValueModalProps) {
  // Form state
  const [formState, setFormState] = useState<FormState>(initialFormState);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSaving, setIsSaving] = useState(false);

  const { user } = useUser();
  const { toast } = useToast();

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (value) {
        setFormState({
          value: value.value,
          label: value.label,
          description: value.description || '',
          pbm_id: value.pbm_id || '',
          effective_from: value.effective_from.split('T')[0],
          effective_to: value.effective_to
            ? value.effective_to.split('T')[0]
            : '',
        });
      } else {
        setFormState(initialFormState);
      }
      setErrors({});
      setTouched({});
    }
  }, [isOpen, value]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formState.value.trim()) {
      newErrors.value = 'Value is required';
    }

    if (!formState.label.trim()) {
      newErrors.label = 'Label is required';
    }

    if (!formState.effective_from) {
      newErrors.effective_from = 'Effective from date is required';
    }

    if (
      formState.effective_to &&
      formState.effective_from &&
      new Date(formState.effective_to) <= new Date(formState.effective_from)
    ) {
      newErrors.effective_to =
        'Effective to date must be after effective from date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value: inputValue } = e.target;
    setFormState((prev) => ({
      ...prev,
      [name]: inputValue,
    }));
    setTouched((prev) => ({
      ...prev,
      [name]: true,
    }));
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      const allTouched = Object.keys(formState).reduce(
        (acc, key) => ({ ...acc, [key]: true }),
        {}
      );
      setTouched(allTouched);

      if (!validateForm()) {
        return;
      }

      const saveData: Partial<ParameterValidValue> = {
        parameter_id: parameterId,
        value: formState.value,
        label: formState.label,
        description: formState.description,
        pbm_id: formState.pbm_id || null,
        effective_from: formState.effective_from,
        effective_to: formState.effective_to || null,
        id: value?.id,
        created_by: user?.email,
      };

      // Call the onSave callback with the save data
      await onSave(saveData);

      onClose();
    } catch (error) {
      console.error('Error saving valid value:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to save valid value',
        variant: 'error',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {value ? 'Edit Valid Value' : 'Add Valid Value'}
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={4}>
            <FormItem
              isRequired
              isInvalid={touched.value && !!errors.value}
              label="Value"
              name="value"
              value={formState.value}
              onChange={handleChange}
              placeholder="Enter value"
              errorMessage={errors.value}
              fieldType="input"
            />

            <FormItem
              isRequired
              isInvalid={touched.label && !!errors.label}
              label="Label"
              name="label"
              value={formState.label}
              onChange={handleChange}
              placeholder="Enter label"
              errorMessage={errors.label}
              fieldType="input"
            />

            <FormItem
              label="Description"
              name="description"
              value={formState.description}
              onChange={handleChange}
              placeholder="Enter description"
              rows={3}
              fieldType="textarea"
            />

            <FormItem
              label="PBM ID"
              name="pbm_id"
              value={formState.pbm_id}
              onChange={handleChange}
              placeholder="Select PBM (optional)"
              fieldType="select"
              options={availablePbms?.map((pbmId) => (
                <option key={pbmId} value={pbmId}>
                  {pbmId}
                </option>
              ))}
            />

            <HStack width="100%" spacing={4}>
              <FormItem
                isRequired
                isInvalid={touched.effective_from && !!errors.effective_from}
                label="Effective From"
                name="effective_from"
                fieldFormat="date"
                value={formState.effective_from}
                onChange={handleChange}
                errorMessage={errors.effective_from}
                fieldType="input"
              />

              <FormItem
                isInvalid={touched.effective_to && !!errors.effective_to}
                label="Effective To"
                name="effective_to"
                fieldFormat="date"
                value={formState.effective_to}
                onChange={handleChange}
                min={formState.effective_from}
                errorMessage={errors.effective_to}
                fieldType="input"
              />
            </HStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button
            variant="ghost"
            mr={3}
            onClick={onClose}
            isDisabled={isSaving || isLoading}
          >
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={isSaving || isLoading}
          >
            {value ? 'Save Changes' : 'Add Value'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
