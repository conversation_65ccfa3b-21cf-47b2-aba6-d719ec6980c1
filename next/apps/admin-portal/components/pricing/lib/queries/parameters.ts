import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { Parameter } from '../types/parameter';
import getResponse from './reponses';

export const QUERY_KEYS = {
  parameters: ['parameters'] as const,
  parameterList: (filters?: Record<string, any>) =>
    [...QUERY_KEYS.parameters, 'list', filters] as const,
  parameterDetails: (id: string) =>
    [...QUERY_KEYS.parameters, 'details', id] as const,
};

interface UpdateParameterVariables {
  parameterId: string;
  data: Partial<Parameter>;
}

export function useParameters(filters: Record<string, any> = {}) {
  return useQuery({
    queryKey: QUERY_KEYS.parameterList(filters),
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.set(key, String(value));
        }
      });
      const response = await fetch(`/api/pricing/parameter`);

      if (!response.ok) {
        throw new Error('Failed to fetch parameters');
      }
      const data = await response.json();

      return getResponse(queryParams, data);
    },
    staleTime: Infinity,
    retry: false,
    refetchOnWindowFocus: false,
  });
}

export function useUpdateParameter() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ parameterId, data }: UpdateParameterVariables) => {
      const response = await fetch(`/api/pricing/parameter/${parameterId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update parameter');
      }

      return response.json();
    },
    onSuccess: (updatedParameter) => {
      // Invalidate the parameter list query to force a refresh
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.parameters });

      // Update the parameter details in the cache
      queryClient.setQueryData(
        QUERY_KEYS.parameterDetails(updatedParameter.parameter_id),
        updatedParameter
      );
    },
  });
}
