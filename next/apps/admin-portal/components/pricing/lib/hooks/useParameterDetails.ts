import { useQuery } from '@tanstack/react-query';

import {
  Parameter,
  ParameterDependency,
  ParameterUIConfig,
  ParameterValidValue,
} from '../../lib/types/parameter';

interface ParameterDetails {
  parameter: Parameter;
  validValues: ParameterValidValue[];
  uiConfig: ParameterUIConfig | null;
  dependencies: ParameterDependency[];
}

const fetcher = async (url: string): Promise<ParameterDetails> => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch parameter details');
  }
  return response.json();
};

export function useParameterDetails(parameterId: string) {
  const { data, error, isLoading, refetch } = useQuery<ParameterDetails>({
    queryKey: ['parameterDetails', parameterId],
    queryFn: () => fetcher(`/api/pricing/parameter/${parameterId}`),
    staleTime: Infinity,
    retry: false,
    refetchOnWindowFocus: false,
  });

  return {
    data: data
      ? {
          parameter: data.parameter,
          validValues: data.validValues || [],
          uiConfig: data.uiConfig || null,
          dependencies: data.dependencies || [],
        }
      : null,
    isLoading,
    error,
    refetch,
  };
}
