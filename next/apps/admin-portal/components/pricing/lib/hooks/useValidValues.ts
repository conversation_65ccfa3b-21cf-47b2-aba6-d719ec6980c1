import { useMutation, useQueryClient } from '@tanstack/react-query';

import { QUERY_KEYS } from '../queries/parameters';
import { ParameterValidValue } from '../types/parameter';

interface ValidValueVariables {
  parameterId: string;
  data: Partial<ParameterValidValue>;
}

export function useValidValues() {
  const queryClient = useQueryClient();

  const createValidValue = useMutation({
    mutationFn: async ({ parameterId, data }: ValidValueVariables) => {
      const response = await fetch(`/api/pricing/valid-values`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create valid value');
      }

      return response.json();
    },
    onSuccess: (_, { parameterId }) => {
      // Invalidate parameter details query to trigger a refresh
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.parameterDetails(parameterId),
      });
    },
  });

  const updateValidValue = useMutation({
    mutationFn: async ({ parameterId, data }: ValidValueVariables) => {
      const response = await fetch(`/api/pricing/valid-values/${data.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update valid value');
      }

      return response.json();
    },
    onSuccess: (_, { parameterId }) => {
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.parameterDetails(parameterId),
      });
    },
  });

  const deleteValidValue = useMutation({
    mutationFn: async ({ parameterId, data }: ValidValueVariables) => {
      const response = await fetch(`/api/pricing/valid-values/${data.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete valid value');
      }

      return response.json();
    },
    onSuccess: (_, { parameterId }) => {
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.parameterDetails(parameterId),
      });
    },
  });

  return {
    createValidValue,
    updateValidValue,
    deleteValidValue,
  };
}
