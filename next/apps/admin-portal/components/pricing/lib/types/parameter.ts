export interface Parameter {
  parameter_id: string;
  name: string;
  description: string;
  group_name: string;
  is_pbm_specific: boolean;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
}

export interface ParameterValidValue {
  id: number;
  parameter_id: string;
  pbm_id: string | null;
  value: string;
  label: string;
  description: string;
  effective_from: string;
  effective_to: string | null;
  created_at: string;
  created_by: string | null;
  updated_at: string | null;
  updated_by: string | null;
}

export interface ParameterUIConfig {
  parameter_id: string;
  component_type: 'select' | 'input' | 'radio' | 'toggle' | 'checkbox';
  label: string;
  placeholder: string;
  help_text: string;
  validation_rules: Record<string, any> | null;
  dependencies: Record<string, any> | null;
  is_required: boolean;
  is_visible: boolean;
  config_data: any;
}

export interface ParameterDependency {
  id: number;
  parameter_id: string;
  controlling_parameter_id: string;
  controlling_parameter_name: string;
  pbm_id: string | null;
  condition_type: 'equals' | 'in' | 'not_in' | 'not_equals';
  condition_value: string;
  effect: 'show' | 'hide' | 'enable' | 'disable';
  effective_from: string;
  effective_to: string | null;
}

export interface ParameterResponse {
  parameters: Parameter[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ParameterFilters {
  search?: string;
  group?: string;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
}
