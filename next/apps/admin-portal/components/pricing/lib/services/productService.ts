export const getProducts = async () => {
  const response = await fetch(`/api/pricing/products`);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.body || 'Failed to fetch products');
  }
  return response.json();
};

export const getProductById = async (productId: string) => {
  const response = await fetch(`/api/pricing/products/${productId}`);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.body || 'Failed to fetch the product details');
  }
  return response.json();
};

export const updateProduct = async (productId: string, productData: any) => {
  const response = await fetch(`/api/pricing/products/${productId}`, {
    body: JSON.stringify(productData),
    method: 'PUT',
  });
  if (!response.ok) {
    const error = await response.json();
    throw error.details;
  }
  return response.json();
};

export const exportProducts = async (productData: any) => {
  const response = await fetch(`/api/pricing/products`, {
    body: JSON.stringify(productData),
    method: 'POST',
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.body || 'Failed to export product');
  }
  return response.json();
};
