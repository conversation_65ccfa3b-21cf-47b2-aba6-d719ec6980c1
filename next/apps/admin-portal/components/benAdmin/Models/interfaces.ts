export interface OrganizationDetails {
  organization: Organization;
  plan: Plan;
  picklists: Picklist[];
  features: Feature[];
  percentages?: Percentages;
  emptyState?: boolean;
}

export interface CostShareTierList {
  CostShareTier: CostShareTierItem[];
}

export interface CostShareTierItem {
  value: string;
  label: string;
  checked?: boolean;
}

export interface Percentages {
  [key: string]: number;
}

export interface DrugList {
  value: string;
}

export interface PlanContainer {
  plan: Plan;
  picklists: Picklist[];
  features: Feature[];
}

export interface ContactDetails {
  organization_id: string;
  name: string;
  internal_contacts: InternalContacts[];
  external_contacts: ExternalContacts[];
}

export interface InternalContacts {
  id: string;
  full_name: string;
  phone: string | null;
  email: string | null;
  role: string;
}

export interface ExternalContacts {
  id: string;
  first_name: string | null;
  last_name: string;
  email: string | null;
  title: string | null;
  phone: string | null;
  mobile: string | null;
  job_role: string | null;
  address: AddressLine;
  other_address: AddressLine;
  contact_owner: string | null;
  contact_type: string | null;
  contact_status: string | null;
  influence: Influence;
  prior_notice: string | null;
  roles: string[] | null;
  is_direct: boolean;
  is_active: boolean;
  email_blast: string | null;
}

export interface Influence {
  web: string | null;
  level: string | null;
}

export interface Organizations {
  organization_id: number;
  datanet_id: number;
  salesforce_id: string;
  name: string;
  legal_entity_id: number;
  effective_date: string;
  expiration_date: string;
  active_ind: boolean;
  created_date: string;
  created_by: number;
  updated_date: string;
  updated_by: number;
  version_number: number;
}

export interface Vendors {
  chosen_pbm: string;
  eligvendorname: string;
}

export interface ChangeRequest {
  change_request_id: number;
  organization_id: number;
  plan_id: number;
  target_effective_date: string;
  type: number;
  status: number;
  change_content: OrganizationDetails;
  published_date: string | null;
  publisher: BenAdminUser | null;
  created_date: string;
  creator: BenAdminUser;
  updated_date: string;
  updater: BenAdminUser;
  version_number: number | null;
  status_value: string;
}

export interface CostShareDesignDetails {
  name: string;
  effective_date: Date;
  expiration_date: Date;
  tier_ind: number;
  drug_list_ind: number;
  pharmacy_channel_ind: number;
  pharmacy_list: string;
  cost_share_type_ind: number;
  pre_packaged_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface CostShareTier {
  pharmacy_list?: string;
  cost_share_tier_id?: number;
  cap_ind?: number | null;
  cost_share_design_id?: number;
  co_insurance_min_amount?: number | null;
  co_insurance_max_amount?: number | null;
  days_supply?: string;
  co_pay_amount?: number | null;
  co_insurance_pct?: number | null;
  co_insurance_ltgt_ind?: number | null;
  co_insurance_cap_amount?: number | null;
  created_date?: string;
  created_by?: number;
  updated_date?: string;
  updated_by?: number;
  version_number?: number;
}

export interface Organization {
  organization_id: number;
  legal_entity_name: string;
  eligibility_vendor_name: string;

  status: string; // Please remove this when status is defined
  pbm: string; // Please remove this when pbm is defined

  datanet_id: number;
  salesforce_id: string;
  name: string;
  effective_date: Date;
  expiration_date: Date | null;
  active_ind: number;
  covered_members: number;
  covered_employees: number;
  brokerage_firm: string | null;
  line_of_business: string | null;
  industry: string | null;
  industry_vertical: string | null;
  primary_termination_reason: string | null;
  secondary_termination_reason: string | null;
  termination_notes: string | null;
  company_hq_state_ind: string | null;
  comm_broker_no: number | null;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  address: Address;
  vendors: Vendors;
}

export interface AddressLine {
  street: string | null;
  city: string | null;
  state: string | null;
  postal_code: string | null;
  country: string;
}

export interface Address {
  address_id: number;
  owner_id: number;
  address_line_1: string;
  address_line_2: string;
  address_type: string;
  owner_type: string;
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country_code: string;
  country: string | null;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface LegalEntity {
  legal_entity_id: number;
  name: string;
  registration_number: string | null;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  person_assignments?: PersonAssignment[];
  addresses: Address[];
  phones: Phone[];
}

export interface LegalEntityAssignments {
  legal_entity_assignment_id: number;
  legal_entity_id: number;
  pa_reviewer: string;
  role_id: number;
  target_legal_entity_id: number;
  target_plan_id: number;
  target_plan_design_id: number;
  effective_date: Date;
  expiration_date: Date | null;
  created_date: Date;
  created_by: number;
  updated_date: Date | null;
  updated_by: number;
  version_number: number;
  role: Role;
  source_legal_entity: SourceLegalEntity;
}

export interface Plan {
  pa_reviewer: string;
  plan_id: number;
  organization_id: number;
  product_id: number;
  datanet_id: number;
  salesforce_id: string;
  name: string;
  effective_date: Date;
  expiration_date: Date;
  expiration_reason: string;
  carrier_number: string;
  account_number: string;
  contract_name: string;
  paid_contract_number: string;
  group_umbrella_number: string;
  plan_year_renewal: number;
  previous_pbm: string;
  renewal_month: number;
  annual_reporting_start_month: number;
  erisa_ind: string;
  implementation_start_date: Date;
  implementation_timeline: string;
  open_enrollment_start_date: Date;
  open_enrollment_end_date: Date;
  open_enrollment_support: string | null;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  product: Product;
  plan_pdx: PlanPDX;
  plan_material: PlanMaterial;
  plan_transition: PlanTransition;
  plan_eligibility: PlanEligibility;
  plan_designs: PlanDesign[];
  pharmacies: Pharmacy[];
  legal_entity_assignment: LegalEntityAssignments[];
  is_340b_ind: string;
  overview_notes: string;
}

export interface PlanPDX {
  plan_pdx_id: number;
  plan_id: number;
  covered_employees: number;
  covered_members: number;
  open_enrollment_start_date: Date;
  open_enrollment_end_date: Date;
  open_enrollment_support: string | null;
  notes: string | null;
  pdx_updates_after_signature: string | null;
  retirees_trust_ind: number | null;
  retail_claims_covered_ind: number | null;
  mail_claims_covered_ind: number | null;
  paper_claims_covered_ind: number | null;
  out_of_network_claims_covered_ind: number | null;
  cob_allowed_ind: number | null;
  medicaid_subrogation_ind: number | null;
  foreign_claims_covered: string | null;
  pre_packaged_copay_ind: number | null;
  diabetic_meds_supplies_description: string | null;
  paper_claims_pricing_ind: number | null;
  specialty_arrangement_ind: number | null;
}

export interface PlanDesignAncillary {
  plan_design_ancillary_id: number | null;
  plan_design_id: number | null;
  effective_date: string | null;
  expiration_date: string | null;
  pharmacy_claim_export_ind: string | null;
  eligibility_export_ind: string | null;
  product_id: number | null;
  notes: string | null;
  product: Product | null;
  product_type: string | null;
  product_options: string | null;
}

export interface AddOnProduct {
  product_id: number;
  product_class_name: string;
  name: string;
  product_options?: string[];
}

export interface AddOnProductResponse {
  plan_id: number;
  products: AddOnProduct[];
}

export interface Feature {
  product_feature_id: number;
  product_class_feature_id: number;
  name: string;
  label: string;
  feature_items: FeatureItem[];
}

export interface FeatureItem {
  feature_item_id: number;
  feature_item_id_level: string;
  name: string;
  label: string;
  field_type: number;
  field_type_label: string;
  picklist_id: number;
  product_class_feature_item_id?: number;
  product_feature_item_id?: number;
}

export interface Product {
  product_id: number | null;
  vendor_id: number | null;
  product_class_id: number | null;
  name: string;
  core_ind: number;
  product_class: ProductClass;
  created_date?: Date;
  created_by?: number;
  updated_date?: Date;
  updated_by?: number;
  version_number?: number;
  vendor?: Vendor;
}

export interface Phone {
  phone_id: number;
  owner_id: number;
  owner_type: string;
  phone_number: string;
  phone_type: string;
  is_primary: boolean;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface Vendor {
  vendor_id: number;
  vendor_type: number;
  pbm_id: number;
  prior_auth_provider_id: number | null;
  legal_entity_id: number;
  name: string;
  elig_vendor_name: string;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  pbm: PBM;
}

export interface PlanMaterial {
  plan_material_id: number;
  plan_id: number;
  id_card_responsible_ind: number;
  id_card_type_ind: number;
  id_card_logo_ind: number;
  employee_id_source_ind: number;
  id_card_mailing_ind: number;
  pcn: string;
  rx_bin: number;
  member_materials_name: string;
  member_packets_ind: number;
  disruption_letters_ind: number;
  specialty_letters_ind: number;
  mail_order_letters_ind: number;
  allow_customization_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  marketing_outreach_home_delivery_ind: number;
  marketing_outreach_specialty_service_ind: number;
  notes: string | null;
}

export interface PlanTransition {
  plan_transition_id: number;
  plan_id: number;
  carrier_to_carrier_ind: number;
  fsa_ind: number;
  fsa_substantiation_needed: string;
  historical_claims_ind: number;
  hra_ind: number;
  hra_substantiation_needed: string;
  hsa_ind: number;
  hsa_substantiation_needed: string;
  mail_order_percentage: number;
  pa_file_ind: number;
  ort_transition_file_ind: string;
  outbound_claims_file: string;
  patient_transfer_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  accum_transfer_ind: number;
  follow_me_ind: number;
  notes: string | null;
}

export interface PlanEligibility {
  plan_eligibility_id: number;
  plan_id: number;
  active_employees_ind: number;
  cobra_ind: number;
  created_date: Date;
  created_by: number;
  dependent_age: number;
  dependent_age_ind: number;
  dependent_alt_groups_ind: number;
  retiree_subsidy_ind: string;
  retirees_ind: number;
  student_age: number;
  student_age_ind: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface PlanDesign {
  plan_design_id: number;
  plan_id: number;
  sort_seq: number;
  type_ind: number;
  name: string;
  active_ind: number;
  description: string;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  plan_design_details: PlanDesignDetail[];
  plan_features: PlanFeature[];
  plan_design_ancillaries: PlanDesignAncillary[];
}

export interface PlanPdx {
  plan_pdx_id: number;
  plan_id: number;
  covered_employees: number;
  covered_members: number;
  open_enrollment_start_date: string;
  open_enrollment_end_date: string;
  open_enrollment_support: string;
  notes: string;
  pdx_updates_after_signature: string | null;
  retirees_trust_ind: boolean | null;
  retail_claims_covered_ind: boolean | null;
  mail_claims_covered_ind: boolean | null;
  paper_claims_covered_ind: boolean | null;
  out_of_network_claims_covered_ind: boolean | null;
  cob_allowed_ind: boolean | null;
  medicaid_subrogation_ind: boolean | null;
  foreign_claims_covered: boolean | null;
  pre_packaged_copay_ind: boolean | null;
  diabetic_meds_supplies_description: string | null;
  paper_claims_pricing_ind: boolean | null;
  specialty_arrangement_ind: boolean | null;
}

export interface PlanFeature {
  plan_feature_id: number;
  product_feature_id: number;
  product_class_feature_id: number;
  plan_feature_items: PlanFeatureItem[];
}

export interface PlanFeatureItem {
  plan_feature_item_id: number;
  product_feature_item_id: number;
  product_class_feature_item_id: number;
  value: string | null;
  value_differs?: boolean | null;
}

export interface PlanDesignDetail {
  plan_design_detail_id: number;
  plan_design_id: number;
  benefit_period_ind: number;
  esi_bplid: null | string;
  dispense_as_written_ind: number | null;
  dispense_as_written_description: string | null;
  qualified_hdhp_ind: null | number;
  union_benefit_ind: null | number;
  healthcare_reform_status: number;
  non_grandfathered_date: null | string;
  compounds_ind: number | null;
  compound_copay_ind: number | null;
  overview_notes: null | string;
  effective_date: string;
  expiration_date: null | string;
  created_date: string;
  created_by: number;
  updated_date: string;
  updated_by: number;
  version_number: number;
  pa_reviewer: null | string;
  accum_integrated_ind: number | null;
  accum_transfer_ind: number | null;
  max_coverage_amount: number | null;
  diabetic_supplies_copay_setup_ind: number;
  pre_packaged_copays_ind: number;
  diabetic_supplies_description: string;
  medical_integration_tier_ind: number | null;
  plan_design_detail_esi: PlanDesignDetailESI[];
  cost_share_design: CostShareDesign[];
  accumulation_deductible: AccumulationDeductible[];
  accumulation_moop: AccumulationMoop[];
  accumulation_other: AccumulationOther[];
}

export interface AccumulationMoop {
  accums_tier_name: string;
  accumulation_moop_id: number;
  accum_period_ind: number;
  apply_ind: number;
  apply_retail_mail_paper_ind: number;
  benefit_period_length_ind: number;
  benefit_period_length_other: number;
  carryover_phase_ind: number;
  describe_carryover_phase: string;
  drug_type_status_ind: number;
  effective_date: Date;
  embedded_ind: number;
  employee_1_dep_amount: number;
  exclude_drug_list_ind: string;
  expiration_date: Date;
  family_plan_amount: number;
  formulary_status_ind: number;
  include_drug_list_ind: string;
  individual_plan_amount: number;
  individual_within_family_amount: number;
  integrated_ind: number;
  network_status_ind: number;
  notes: string;
  pbc_order: number;
  penalties_apply_after_ind: number;
  penalties_apply_ind: number;
  pharmacy_channel_ind: number;
  plan_design_detail_id: number;
  priming_balances_ind: number;
  shared_ind: number;
  specialty_moop_amount: number;
  specify_accum_period_ind: number;
}

export interface AccumulationDeductible {
  accums_tier_name: string;
  accumulation_deductible_id: number;
  accum_period_ind: number;
  apply_brand_only_ind: number;
  apply_ind: number;
  apply_moop_max_ind: number;
  apply_retail_mail_paper_ind: number;
  apply_specialty_only_ind: number;
  apply_to_moop_ind: number;
  benefit_period_length_ind: number;
  benefit_period_length_other: number;
  carryover_phase_ind: number;
  copay_apply_during_deductible_phase_ind: number;
  describe_carryover_phase: string;
  drug_type_status_ind: number;
  effective_date: Date;
  embedded_ind: number;
  employee_1_dep_amount: number;
  exclude_drug_list_ind: string;
  expiration_date: Date;
  family_plan_amount: number;
  formulary_status_ind: number;
  include_drug_list_ind: string;
  individual_plan_amount: number;
  individual_within_family_amount: number;
  integrated_ind: number;
  network_status_ind: number;
  notes: string;
  pbc_order: number;
  penalties_apply_ind: number;
  pharmacy_channel_ind: number;
  plan_design_detail_id: number;
  priming_balances_ind: number;
  shared_ind: number;
  specialty_deductible_amount: number;
  specify_accum_period_ind: number;
}

export interface AccumulationOther {
  accums_tier_name: string;
  accumulation_other_id: number;
  accum_period_ind: number;
  benefit_period_length_ind: number;
  benefit_period_length_other: number;
  carryover_phase_ind: number;
  cdh_class_code_ind: number;
  describe_carryover_phase: string;
  drug_type_status_ind: number;
  effective_date: Date;
  employee_1_dep_amount: number;
  exclude_drug_list_ind: string;
  expiration_date: Date;
  family_plan_amount: number;
  formulary_status_ind: number;
  include_drug_list_ind: string;
  individual_plan_amount: number;
  individual_within_family_amount: number;
  integrated_ind: number;
  max_allowable_cap: number;
  max_coverage_amount: number;
  network_status_ind: number;
  notes: string;
  other_accum_type_ind: number;
  pbc_order: number;
  pharmacy_channel_ind: number;
  plan_design_detail_id: number;
  priming_balances_ind: number;
  shared_ind: number;
  specify_accum_period_ind: number;
}
export interface CostShareDesign {
  cost_share_design_id?: number | null;
  name: string | null;
  compound_ind?: number | null;
  plan_design_detail_id?: number;
  effective_date: string | null;
  expiration_date: string | null;
  tier_ind: string;
  cost_share_type_ind: number | null;
  pharmacy_channel_ind: number | null;
  pharmacy_list: string | null;
  pre_packaged_ind: number | null;
  patient_pay_type?: number | null;
  created_date?: string;
  created_by?: number;
  updated_date?: string;
  updated_by?: number;
  version_number?: number;
  drug_list_ind: string | null;
  network_status_ind: number | null;
  include_pbc_ind?: number | null;
  cost_share_tiers: CostShareTier[];
}

export interface PlanDesignDetailESI {
  plan_design_detail_esi_id: number | null;
  plan_design_detail_id: number | null;
  shared_vendor_id: number | null;
  network_applicability_ind: number | null;
  vendor_policy_number: string | null;
  spending_account_type_ind: number | null;
  xml_accums_complete_ind: number | null;
  hra_members_access_ind: number | null;
  hsa_admin_phone: string | null;
  hsa_medical_phone: string | null;
  created_date: string;
  created_by: number;
  updated_date: string;
  updated_by: number;
  version_number: number;
}

export interface Pharmacy {
  pharmacy_id: number;
  name: string;
  legal_entity_id: number;
  admin_340b_fee: number;
  billing_ind: number;
  created_date: Date;
  created_by: number;
  custom_rate_notes: string;
  dispensing_fee_value: number;
  effective_date: Date;
  expiration_date: Date | null;
  financial_guarantee_ind: number;
  hardcoded_dispense_fee_ind: number;
  hardcoded_dispense_fee_notes: string;
  ihp_software: string;
  ihp_switch: string;
  is_340b: number;
  multi_ingredient_compounds_ind: number;
  nabp_number: string;
  npi_number: string;
  notes: string;
  pharmacy_ownership_ind: number;
  pricing_setup_ind: number;
  rebate_protection_ind: number;
  status_ind: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  legal_entity: LegalEntity;
}

export interface PBM {
  pbm_id: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  legal_entity: LegalEntity;
}

export interface Person {
  person_id: number;
  first_name: string;
  last_name: string;
  birth_date: Date;
  gender: string;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface PersonAssignment {
  person_assignment_id: number;
  person_id: number;
  role_id: number;
  target_legal_entity_id: number;
  target_plan_id: number;
  target_plan_design_id: number;
  effective_date: Date;
  expiration_date: Date | null;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
  person: Person;
}

export interface Request {
  request_id: number;
  request_type_ind: number;
  status_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  // The change_content will be of type OrganizationDetails.
  change_content: OrganizationDetails;
  initial_state: any;
  note: string;
  organization_id: number;
  pbm_date: Date;
  plan_id: number;
  published_by: number;
  published_date: Date;
  type: number;
  version_number: number;
}

export interface RequestLog {
  request_log_id: number;
  request_id: number;
  log_ts: Date;
  message_severity: number;
  message_text: string;
}

export interface Role {
  role_id: number;
  entity_scope: number;
  role_type: number;
  name: string;
  created_date: Date;
  created_by: number;
  updated_date: Date | null;
  updated_by: number;
  version_number: number;
  allow_multiple_ind: number;
}

export interface SourceLegalEntity {
  legal_entity_id: number;
  name: string;
  registration_number: number | null;
  created_date: Date;
  created_by: number;
  updated_date: Date | null;
  updated_by: number;
  version_number: number;
  phones: Phone;
}

export interface Document {
  document_id: number;
  document_type_id: number;
  document_version: number;
  file_name: string;
  file_type: string;
  legal_entity_id: number;
  plan_id: number;
  request_id: number;
  s3_bucket_name: string;
  s3_object_key: string;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface DocumentType {
  document_type_id: number;
  name: string;
  description: string;
  entity_scope_ind: number;
  allow_multiple_ind: number;
  require_versioning_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface Email {
  email_id: number;
  email_address: string;
  email_type: string;
  is_primary: number;
  owner_id: number;
  owner_type: string;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface Picklist {
  picklist_id: number;
  picklist_values: PicklistValue[];
  name: string;
  sort_values_ind: number;
  system_managed_ind: number;
  value_type_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface PicklistValue {
  picklist_value_id: number;
  picklist_id: number;
  label: string;
  value: string;
  sort_seq: number;
  default_ind: number;
  created_date: Date;
  created_by: number;
  updated_date: Date;
  updated_by: number;
  version_number: number;
}

export interface UserSync {
  user_id: number;
  email: string;
  name: string;
  sync_date: Date;
}

export interface PlanDocument {
  document_id: number;
  legal_entity_id: number;
  plan_id: number;
  document_type_id: number;
  request_id: number;
  document_version: number;
  file_name: string;
  file_type: string;
  s3_bucket_name: string;
  s3_object_key: string;
  created_date: string;
  creator: BenAdminUser;
  updated_date: number;
  updater: BenAdminUser;
  version_number: number;
  s3_file_link: string;
  internal_ind: number;
  assign_group: string;
}

export interface BenAdminUser {
  user_no: number;
  name: string;
  email: string;
  sync_date: string;
}

export interface CostShareComponentProps {
  formMethods: any;
  onUpdateActiveItem?: (id: string) => void;
  isUnbreakable?: boolean;
  title?: string;
  description?: string;
  modalTitle?: string;
  basePath?: string;
  sourceLabel?: string;
}

export interface SelectedTier {
  tier_ind: string;
  unbreakable?: boolean;
}

export interface ModalSaveData {
  selected_tiers: SelectedTier[];
  unbreakable?: boolean;
}

export interface CostShareState {
  isMirroring: boolean;
  showItems: boolean;
  isUpdatingForm: boolean;
  formUpdateCount: number;
}

export type CostShareAction =
  | { type: 'SET_MIRRORING'; payload: boolean }
  | { type: 'SET_SHOW_ITEMS'; payload: boolean }
  | { type: 'UPDATE_FORM_START' }
  | { type: 'UPDATE_FORM_END' }
  | { type: 'UPDATE_STATE'; payload: Partial<CostShareState> };

export interface ProductClass {
  product_class_id: number;
  parent_id: number;
  name: string;
  requires_pbm: number;
  requires_pa_provider: number;
}

// Validation Response Interfaces
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationRule {
  plan_validation_rule_id: number;
  rule_name: string;
  validation_message: string;
  plan_design_id?: number;
}

export interface ValidationResults {
  errors: ValidationRule[];
  warnings: ValidationRule[];
}

export interface ValidationResultItem {
  ui_context_ind: number;
  status: string;
  errors: ValidationError[];
  validation_results: ValidationResults;
}

export interface ValidationResponse {
  message: string;
  results: {
    [key: string]: ValidationResultItem;
  };
}

export interface ValidateByPageReturn {
  validationData: ValidationResponse | null;
  isError: boolean;
  isLoading: boolean;
  refetch: () => Promise<any>;
}
