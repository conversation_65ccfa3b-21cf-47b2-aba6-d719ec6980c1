import {
  Button,
  Checkbox,
  FormControl,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  VStack,
} from '@chakra-ui/react';
import { FC, useState } from 'react';

interface Mapper {
  [key: string]: string;
}

interface PbcPlanDesignGroups {
  [groupName: string]: number[];
}

interface PlanDesignSelectProps {
  planId: number;
  isOpen: boolean;
  onClose: () => void;
  planDesignsList: Mapper;
  pbcPlanDesignGroups: PbcPlanDesignGroups;
  generatePBCMutation: any;
}

export const PlanDesignSelect: FC<PlanDesignSelectProps> = ({
  planId,
  isOpen,
  onClose,
  planDesignsList,
  pbcPlanDesignGroups,
  generatePBCMutation,
}) => {
  const [selectedValues, setSelectedValues] = useState<Set<string>>(new Set());
  const [disabledPlans, setDisabledPlans] = useState<number[]>([]);

  // Helper function to find which group a plan design belongs to
  const findGroupForPlan = (planId: number): string | null => {
    for (const [groupName, planIds] of Object.entries(pbcPlanDesignGroups)) {
      if (planIds.includes(planId)) {
        return groupName;
      }
    }
    return null;
  };

  // Helper function to get all plan IDs from all groups except the specified one
  const getPlanIdsFromOtherGroups = (excludeGroupName: string): number[] => {
    const allOtherPlanIds: number[] = [];
    for (const [groupName, planIds] of Object.entries(pbcPlanDesignGroups)) {
      if (groupName !== excludeGroupName) {
        allOtherPlanIds.push(...planIds);
      }
    }
    return allOtherPlanIds;
  };

  const handleCheckboxChange = (tierValue: string, isChecked: boolean) => {
    setSelectedValues((prev) => {
      const newSelected = new Set(prev);
      isChecked ? newSelected.add(tierValue) : newSelected.delete(tierValue);
      return newSelected;
    });

    // Update disabled plans based on group logic
    if (isChecked) {
      const planId = parseInt(tierValue);
      const selectedGroup = findGroupForPlan(planId);

      if (selectedGroup) {
        // Disable all plans from other groups
        const plansToDisable = getPlanIdsFromOtherGroups(selectedGroup);
        setDisabledPlans(plansToDisable);
      }
    } else {
      // If unchecking, check if there are any other selected plans
      const remainingSelected = Array.from(selectedValues).filter(
        (val) => val !== tierValue
      );

      if (remainingSelected.length === 0) {
        // No plans selected, enable all
        setDisabledPlans([]);
      } else {
        // Check if remaining selected plans are from the same group
        const firstRemainingPlanId = parseInt(remainingSelected[0]);
        const firstGroup = findGroupForPlan(firstRemainingPlanId);

        if (firstGroup) {
          // Check if all remaining selected plans are from the same group
          const allFromSameGroup = remainingSelected.every((val) => {
            const planId = parseInt(val);
            return findGroupForPlan(planId) === firstGroup;
          });

          if (allFromSameGroup) {
            // Keep other groups disabled
            const plansToDisable = getPlanIdsFromOtherGroups(firstGroup);
            setDisabledPlans(plansToDisable);
          } else {
            // Plans from different groups selected, this shouldn't happen with our logic
            // but handle it gracefully by enabling all
            setDisabledPlans([]);
          }
        }
      }
    }
  };

  const handleConfirm = () => {
    const selectedIds = Array.from(selectedValues).map(Number);
    if (generatePBCMutation)
      generatePBCMutation.mutate({
        plan_id: planId,
        plan_design_ids: selectedIds,
      });

    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent position="relative" maxWidth="sm">
        <ModalHeader
          color="#69696A"
          fontSize="lg"
          paddingTop={5}
          fontWeight="700"
        >
          Select Plan designs for PBC
        </ModalHeader>
        <ModalCloseButton
          paddingTop={5}
          _hover={{ bg: 'transparent', boxShadow: 'none' }}
        />
        <ModalBody paddingTop={0}>
          <FormControl flex="1">
            <VStack align="start" spacing={2} maxH="400px" overflowY="auto">
              {Object.entries(planDesignsList).map(([tierValue, tierLabel]) => (
                <Checkbox
                  key={tierValue}
                  colorScheme="green"
                  isChecked={selectedValues.has(tierValue)}
                  isDisabled={disabledPlans.includes(+tierValue)}
                  onChange={(e) =>
                    handleCheckboxChange(tierValue, e.target.checked)
                  }
                >
                  {tierLabel}
                </Checkbox>
              ))}
            </VStack>
          </FormControl>
        </ModalBody>
        <ModalFooter paddingTop={1}>
          <Button
            onClick={onClose}
            mr={3}
            variant="outline"
            colorScheme="green"
          >
            Cancel
          </Button>
          <Button
            colorScheme={selectedValues.size === 0 ? 'gray' : 'green'}
            onClick={handleConfirm}
            isDisabled={selectedValues.size === 0}
            variant="solid"
          >
            Confirm
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
