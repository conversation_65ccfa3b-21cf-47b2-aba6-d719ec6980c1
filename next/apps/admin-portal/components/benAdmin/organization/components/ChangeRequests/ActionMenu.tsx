import {
  <PERSON>lex,
  <PERSON>u,
  <PERSON>u<PERSON>utton,
  Menu<PERSON>tem,
  MenuList,
  Portal,
  Spinner,
  Text,
} from '@chakra-ui/react';
import { ChangeRequest } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { BsThreeDotsVertical } from 'react-icons/bs';

import { useOrganizationModals } from '../../hooks/useOrganizationModals';
import { ConfirmationPopup } from '../ConfirmationPopup';
import useActionHandlers from './actionHandlers';

interface ActionMenuProps {
  menuItemClickedRef: {
    current: string | null;
  };
  menuKey: number;
  rowData: ChangeRequest;
}

const ActionMenu = ({
  menuItemClickedRef,
  menuKey,
  rowData,
}: ActionMenuProps) => {
  const {
    isDeleteOpen: isCancelOpen,
    onDeleteOpen: onCancelOpen,
    onDeleteClose: onCancelClose,
  } = useOrganizationModals();

  const { cancelChangeRequest, isCancelLoading } = useActionHandlers(
    rowData,
    onCancelClose
  );

  const handleCancelRequest = async () => {
    await cancelChangeRequest();
  };

  const actionList = [
    {
      action: 'Cancel',
      handler: onCancelOpen,
    },
  ];

  return (
    <>
      <Flex justify="flex-end">
        <Menu key={menuKey} placement="bottom-end">
          <MenuButton
            key={menuKey}
            disabled={isCancelLoading}
            onClick={() => (menuItemClickedRef.current = `${menuKey}-button`)}
          >
            {isCancelLoading ? <Spinner size="xs" /> : <BsThreeDotsVertical />}
          </MenuButton>
          <Portal>
            <MenuList>
              {actionList.map((menu) => (
                <MenuItem
                  key={menuKey}
                  onClick={(e) => {
                    menuItemClickedRef.current = `${menuKey}-item`;
                    menu.handler?.();
                  }}
                >
                  <Text color="#69696A" fontWeight="400" textStyle="sm">
                    {menu.action}
                  </Text>
                </MenuItem>
              ))}
            </MenuList>
          </Portal>
        </Menu>
      </Flex>

      <ConfirmationPopup
        isOpen={isCancelOpen}
        onClose={onCancelClose}
        onConfirm={handleCancelRequest}
        alertHeader="Cancel Change Request"
        alertBody="Canceled change requests cannot be edited or published. Do you wish to proceed?"
        confirmButtonText="Yes"
        cancelButtonText="No"
        isConfirmLoading={isCancelLoading}
      />
    </>
  );
};

export default ActionMenu;
