import { CheckIcon } from '@chakra-ui/icons';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Tag,
  TagLabel,
  TagLeftIcon,
  useDisclosure,
} from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';

import { ChangeRequest, OrganizationDetails } from '../../../Models/interfaces';
import AutoTable from '../../../ReusableComponents/Components/Table';
import { AutoTableColumn } from '../../../ReusableComponents/Models/types';
import { useIsChangeRequestPage } from '../../hooks/useOrganizationHooks';
import { NewChangeRequestModal } from './NewChangeRequestModal';

export const ChangeRequestTable = ({
  orgId,
  changeRequests,
  organizationDetails,
}: {
  orgId: number | null;
  changeRequests: ChangeRequest[];
  organizationDetails: OrganizationDetails;
}) => {
  const router = useRouter();
  const { isOpen, onOpen, onClose } = useDisclosure();

  /**
   * Check if we are in draft mode by checking route.
   * When in draft mode, certain actions (like row clicks) are disabled.
   */
  const isChangeRequestPage = useIsChangeRequestPage();

  const getStatusLabel = useMemo(
    () => (statusCode: number) => {
      switch (statusCode) {
        case 0:
          return 'Pending';
        case 1:
          return 'Partially Published';
        case 2:
          return 'Published';
        case 9:
          return 'Canceled';
        default:
          return 'Unknown';
      }
    },
    []
  );

  /**
   * Returns the color scheme for a given status.
   */
  const getTagColor = useMemo(
    () => (statusCode: number) => {
      switch (statusCode) {
        case 0: // Pending
          return 'yellow';
        case 1: // Partially Published
          return 'orange';
        case 2: // Published
          return 'green';
        case 9: // Canceled
          return 'red';
        default:
          return 'gray';
      }
    },
    []
  );

  /**
   * When a row is clicked, it indicates the user wants to edit an existing change request.
   * In that case, we store the clicked change request data in sessionStorage and route
   * to the ChangeRequestIntake page so that the form can be pre-populated with this data.
   */
  const handleRowClick = (data: ChangeRequest) => {
    if (data.status_value !== 'Pending') return;
    const changeRequestId = data?.change_request_id;
    router.push(
      `/ben-admin/organization/${orgId}/change-request-intake/${changeRequestId}`
    );
  };

  /**
   * Define table columns using memoization to prevent unnecessary re-renders.
   * Each column can include custom rendering logic, such as formatting dates
   * or displaying status tags with specific colors.
   */
  const columns: AutoTableColumn<ChangeRequest>[] = useMemo(
    () => [
      {
        key: 'status',
        override: {
          headerName: 'Status',
          data: (row) => ({
            display: (
              <Tag colorScheme={getTagColor(row.status)}>
                {row.status === 2 && !isChangeRequestPage && (
                  <TagLeftIcon as={CheckIcon} />
                )}
                <TagLabel>{getStatusLabel(row.status)}</TagLabel>
              </Tag>
            ),
            value: row.status,
          }),
          // Disable row clicks if the status is not pending or if we're in draft mode.
          disableRow: (row: any) => row.status !== 0 || isChangeRequestPage,
        },
      },
      {
        key: 'target_effective_date',
        override: { headerName: 'Effective Date', datatype: 'date' },
      },
      {
        key: 'creator.name',
        override: { headerName: 'Created By' },
      },
      {
        key: 'updated_date',
        override: { headerName: 'Last Updated', datatype: 'date' },
      },
      {
        key: 'updater.name',
        override: { headerName: 'Updated By' },
      },
    ],
    [getTagColor, isChangeRequestPage, getStatusLabel]
  );

  return (
    <>
      {!isChangeRequestPage && (
        <Card>
          <CardHeader>
            <Heading size="md">Change Requests</Heading>
          </CardHeader>
          <CardBody>
            <AutoTable
              data={changeRequests}
              columns={columns}
              onRowClick={handleRowClick}
            />
            <Flex justify="end" mt={4}>
              <Button
                colorScheme="green"
                onClick={() => {
                  onOpen();
                }}
              >
                Create New
              </Button>
            </Flex>
          </CardBody>
        </Card>
      )}

      <NewChangeRequestModal
        isOpen={isOpen}
        onClose={onClose}
        orgId={orgId}
        organizationDetails={organizationDetails}
        changeRequests={changeRequests}
      />
    </>
  );
};
