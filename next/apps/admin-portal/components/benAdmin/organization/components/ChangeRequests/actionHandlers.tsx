import { useQueryClient } from '@tanstack/react-query';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { ChangeRequest } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import Toast from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/TabSections/Toast';

const useActionHandlers = (
  rowData: ChangeRequest,
  onCancelClose: () => void
) => {
  const queryClient = useQueryClient();
  const { useApiMutation } = useBenAdmin();

  const {
    mutateAsync: cancelChangeRequestMutation,
    isPending: isCancelLoading,
  } = useApiMutation('changerequest', 'PUT', {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['changerequest'] });
      onCancelClose();
      return Toast({
        title: 'Change Request Cancelled',
        description: 'This change request has been cancelled.',
        position: 'top-right',
      });
    },
    onError: () => {
      onCancelClose();
      return Toast({
        title: 'Failed to Cancel Change Request',
        description: 'Please try again later.',
        position: 'top-right',
        status: 'error',
      });
    },
  });

  const body: Partial<ChangeRequest> = {
    organization_id: rowData.organization_id,
    type: rowData.type,
    status: 9,
  };

  const cancelChangeRequest = async () => {
    cancelChangeRequestMutation({
      pathParams: { id: rowData.change_request_id },
      ...body,
    });
  };

  return {
    cancelChangeRequest,
    isCancelLoading,
  };
};

export default useActionHandlers;
