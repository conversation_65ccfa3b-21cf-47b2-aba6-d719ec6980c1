'use client';

import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import {
  Box,
  FormControl,
  FormErrorMessage,
  IconButton,
  Input,
  InputGroup,
  InputLeftAddon,
  Select,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useTheme,
} from '@chakra-ui/react';
import { CostShareTier } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import React, { useState } from 'react';

import { DaySupplyOption } from './StepTwoTemplate';

interface StepThreeTemplateProps {
  tiers: CostShareTier[];
  onTierChange: (
    tierIndex: number,
    field: keyof CostShareTier,
    value: any
  ) => void;
  patientPayType: string;
  selectedDaySupplyOptions: DaySupplyOption[];
  title?: string;
}

const ROWS_PER_PAGE = 3;

const StepThreeTemplate: React.FC<StepThreeTemplateProps> = ({
  tiers,
  onTierChange,
  patientPayType,
  selectedDaySupplyOptions,
  title = 'Configure Cost Share',
}) => {
  const theme = useTheme();
  //   const {
  //     formState: { errors, dirtyFields },
  //   } = useFormContext();
  const [currentPage, setCurrentPage] = useState(0);
  const [dirtyTiers, setDirtyTiers] = useState<Record<number, Set<string>>>({});

  const formStyles = {
    field: {
      _focus: {
        borderColor: 'green.500',
        boxShadow: `0 0 0 1px ${theme.colors.green[500]}`,
      },
    },
    customSelect: {
      appearance: 'none',
      WebkitAppearance: 'none',
      MozAppearance: 'none',
      bg: 'gray.100',
      width: '35px',
      height: '32px',
      borderRightRadius: '0',
      borderRight: '0',
      cursor: 'pointer',
      paddingInlineStart: '8px',
      paddingInlineEnd: '8px',
      textAlign: 'center',
      marginRight: '-5px',
      borderLeft: '3px solid #48BB78',
      outline: 'none',
      '&::-ms-expand': {
        display: 'none',
      },
      '& > option': {
        textAlign: 'center',
      },
      _hover: {
        borderLeft: '3px solid #38A169',
      },
      _focus: {
        outline: 'none',
        boxShadow: 'none',
        border: 'none',
        borderLeft: '3px solid  #48BB78',
      },
      _focusVisible: {
        outline: 'none',
        boxShadow: 'none',
      },
    },
    capAmountInput: {
      minWidth: '200px',
      marginLeft: '0',
      borderLeftRadius: '0',
    },
  };

  const filteredOptions = selectedDaySupplyOptions.filter(
    (option) => option.checked
  );
  const totalPages = Math.ceil(filteredOptions.length / ROWS_PER_PAGE);
  const startIndex = currentPage * ROWS_PER_PAGE;
  const currentPageOptions = filteredOptions.slice(
    startIndex,
    startIndex + ROWS_PER_PAGE
  );

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages - 1));
  };

  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 0));
  };

  // Handle input change with validation
  const handleInputChange = (
    tierIndex: number,
    field: keyof CostShareTier,
    value: string,
    isNumeric = false
  ) => {
    // Only allow numeric input if specified
    if (isNumeric && value && !/^\d*\.?\d*$/.test(value)) {
      return;
    }

    // Mark the field as dirty
    setDirtyTiers((prev) => {
      const newDirtyTiers = { ...prev };
      if (!newDirtyTiers[tierIndex]) {
        newDirtyTiers[tierIndex] = new Set();
      }
      newDirtyTiers[tierIndex].add(field);
      return newDirtyTiers;
    });

    // If changing co_insurance_ltgt_ind to greater or lesser (values 0 or 1),
    // also mark co_pay_amount as dirty to enforce validation
    if (field === 'co_insurance_ltgt_ind' && (value === '0' || value === '1')) {
      setDirtyTiers((prev) => {
        const newDirtyTiers = { ...prev };
        if (!newDirtyTiers[tierIndex]) {
          newDirtyTiers[tierIndex] = new Set();
        }
        newDirtyTiers[tierIndex].add('co_pay_amount');
        return newDirtyTiers;
      });
    }

    onTierChange(tierIndex, field, value);
  };

  // Check if a field should show error
  const shouldShowError = (
    tierIndex: number,
    field: string,
    tier: CostShareTier
  ) => {
    // If field is co_pay_amount and co_insurance_ltgt_ind is set (greater or lesser),
    // also validate co_pay_amount is provided
    if (
      field === 'co_pay_amount' &&
      (String(tier.co_insurance_ltgt_ind) === '0' ||
        String(tier.co_insurance_ltgt_ind) === '1')
    ) {
      return !tier.co_pay_amount && dirtyTiers[tierIndex]?.has(field);
    }

    return dirtyTiers[tierIndex]?.has(field);
  };

  // Check if co-pay amount is required based on selected lesser/greater option
  const isCoPayAmountRequired = (tier: CostShareTier) => {
    return (
      String(tier.co_insurance_ltgt_ind) === '0' ||
      String(tier.co_insurance_ltgt_ind) === '1'
    );
  };

  return (
    <Box width="100%">
      <Text fontSize="lg" fontWeight="semibold" mb={4}>
        {title}
      </Text>
      <Text fontSize="sm" color="gray.600" mb={4}>
        Set up the cost share details for each selected days supply option.
        {totalPages > 1 && ` (Page ${currentPage + 1} of ${totalPages})`}
      </Text>

      <Box overflowX="auto">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Tier Name</Th>
              <Th>Day Supply</Th>
              {patientPayType === '1' ? (
                <Th>Co-Pay/Amount</Th>
              ) : patientPayType === '2' ? (
                <>
                  <Th>Co-Insurance Percentage</Th>
                  <Th>Minimum</Th>
                  <Th>Maximum</Th>
                </>
              ) : (
                <>
                  <Th>Co-Insurance Percentage</Th>
                  <Th>Lesser or Greater</Th>
                  <Th>Co-Pay/Amount</Th>
                </>
              )}
            </Tr>
          </Thead>
          <Tbody>
            {currentPageOptions.map((option, pageIndex) => {
              const index = startIndex + pageIndex;
              const tier = tiers[index] || {
                cost_share_tier_id: null,
                days_supply: option.label,
                co_pay_amount: null,
                co_insurance_pct: null,
                co_insurance_ltgt_ind: null,
                co_insurance_min_amount: null,
                co_insurance_max_amount: null,
              };

              return (
                <Tr key={option.id}>
                  <Td>
                    {tier?.cost_share_tier_id
                      ? `CT${tier.cost_share_tier_id
                          ?.toString()
                          ?.padStart(8, '0')}`
                      : ''}
                  </Td>
                  <Td>{option.label}</Td>
                  {patientPayType === '1' ? (
                    // Co-Pay View
                    <Td>
                      <FormControl
                        isRequired
                        isInvalid={
                          shouldShowError(index, 'co_pay_amount', tier) &&
                          !tier.co_pay_amount
                        }
                      >
                        <InputGroup size="sm">
                          <InputLeftAddon>$</InputLeftAddon>
                          <Input
                            type="text"
                            inputMode="numeric"
                            value={tier.co_pay_amount || ''}
                            onChange={(e) =>
                              handleInputChange(
                                index,
                                'co_pay_amount',
                                e.target.value,
                                true
                              )
                            }
                            placeholder="Enter amount"
                            sx={formStyles.field}
                          />
                        </InputGroup>
                        {shouldShowError(index, 'co_pay_amount', tier) &&
                          !tier.co_pay_amount && (
                            <FormErrorMessage>
                              Co-pay amount is required
                            </FormErrorMessage>
                          )}
                      </FormControl>
                    </Td>
                  ) : patientPayType === '2' ? (
                    // Co-Insurance View
                    <>
                      <Td>
                        <FormControl
                          isRequired
                          isInvalid={
                            shouldShowError(index, 'co_insurance_pct', tier) &&
                            !tier.co_insurance_pct
                          }
                        >
                          <InputGroup size="sm">
                            <Input
                              type="text"
                              inputMode="numeric"
                              value={tier.co_insurance_pct || ''}
                              onChange={(e) =>
                                handleInputChange(
                                  index,
                                  'co_insurance_pct',
                                  e.target.value,
                                  true
                                )
                              }
                              placeholder="Enter percentage"
                              sx={formStyles.field}
                            />
                            <InputLeftAddon>%</InputLeftAddon>
                          </InputGroup>
                          {shouldShowError(index, 'co_insurance_pct', tier) &&
                            !tier.co_insurance_pct && (
                              <FormErrorMessage>
                                Co-insurance percentage is required
                              </FormErrorMessage>
                            )}
                        </FormControl>
                      </Td>
                      <Td>
                        <InputGroup size="sm">
                          <InputLeftAddon>$</InputLeftAddon>
                          <Input
                            type="text"
                            inputMode="numeric"
                            value={tier.co_insurance_min_amount || ''}
                            onChange={(e) =>
                              handleInputChange(
                                index,
                                'co_insurance_min_amount',
                                e.target.value,
                                true
                              )
                            }
                            placeholder="Enter min"
                            sx={formStyles.field}
                          />
                        </InputGroup>
                      </Td>
                      <Td>
                        <InputGroup size="sm">
                          <InputLeftAddon>$</InputLeftAddon>
                          <Input
                            type="text"
                            inputMode="numeric"
                            value={tier.co_insurance_max_amount || ''}
                            onChange={(e) =>
                              handleInputChange(
                                index,
                                'co_insurance_max_amount',
                                e.target.value,
                                true
                              )
                            }
                            placeholder="Enter max"
                            sx={formStyles.field}
                          />
                        </InputGroup>
                      </Td>
                    </>
                  ) : (
                    // Blended View
                    <>
                      <Td>
                        <FormControl
                          isRequired
                          isInvalid={
                            shouldShowError(index, 'co_insurance_pct', tier) &&
                            !tier.co_insurance_pct
                          }
                        >
                          <InputGroup size="sm">
                            <Input
                              type="text"
                              inputMode="numeric"
                              value={tier.co_insurance_pct || ''}
                              onChange={(e) =>
                                handleInputChange(
                                  index,
                                  'co_insurance_pct',
                                  e.target.value,
                                  true
                                )
                              }
                              placeholder="Enter percentage"
                              sx={formStyles.field}
                            />
                            <InputLeftAddon>%</InputLeftAddon>
                          </InputGroup>
                          {shouldShowError(index, 'co_insurance_pct', tier) &&
                            !tier.co_insurance_pct && (
                              <FormErrorMessage>
                                Co-insurance percentage is required
                              </FormErrorMessage>
                            )}
                        </FormControl>
                      </Td>
                      <Td>
                        <FormControl>
                          <Select
                            size="sm"
                            value={tier.co_insurance_ltgt_ind || ''}
                            onChange={(e) =>
                              handleInputChange(
                                index,
                                'co_insurance_ltgt_ind',
                                e.target.value
                              )
                            }
                            sx={formStyles.field}
                          >
                            <option value="">Select</option>
                            <option value="0">Lesser</option>
                            <option value="1">Greater</option>
                          </Select>
                        </FormControl>
                      </Td>
                      <Td>
                        <FormControl
                          isRequired={isCoPayAmountRequired(tier)}
                          isInvalid={
                            isCoPayAmountRequired(tier) &&
                            !tier.co_pay_amount &&
                            dirtyTiers[index]?.has('co_pay_amount')
                          }
                        >
                          <InputGroup size="sm">
                            <InputLeftAddon>$</InputLeftAddon>
                            <Input
                              type="text"
                              inputMode="numeric"
                              value={tier.co_pay_amount || ''}
                              onChange={(e) =>
                                handleInputChange(
                                  index,
                                  'co_pay_amount',
                                  e.target.value,
                                  true
                                )
                              }
                              placeholder="Enter amount"
                              sx={formStyles.field}
                            />
                          </InputGroup>
                        </FormControl>
                      </Td>
                    </>
                  )}
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </Box>

      {totalPages > 1 && (
        <Box display="flex" justifyContent="center" mt={4} gap={4}>
          <IconButton
            aria-label="Previous page"
            icon={<ChevronLeftIcon boxSize={6} />}
            onClick={handlePrevPage}
            isDisabled={currentPage === 0}
            color="green.500"
            variant="unstyled"
            size="lg"
            _hover={{ color: 'green.600' }}
            _disabled={{ color: 'gray.400', cursor: 'not-allowed' }}
          />
          <IconButton
            aria-label="Next page"
            icon={<ChevronRightIcon boxSize={6} />}
            onClick={handleNextPage}
            isDisabled={currentPage === totalPages - 1}
            color="green.500"
            variant="unstyled"
            size="lg"
            _hover={{ color: 'green.600' }}
            _disabled={{ color: 'gray.400', cursor: 'not-allowed' }}
          />
        </Box>
      )}
    </Box>
  );
};

export default StepThreeTemplate;
