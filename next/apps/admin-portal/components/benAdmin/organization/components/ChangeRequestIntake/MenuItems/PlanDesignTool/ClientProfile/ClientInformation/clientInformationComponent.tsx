import { Box } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import { useClientInformationForm } from './clientInformationForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ClientInformationComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useClientInformationForm(
    currentDetails as Partial<OrganizationDetails>
  );
  const submitHandler = useSaveChangeRequestHandler(formMethods);

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = () => {
    // Seamlessly update the active menu item to "pharmacy-benefits-manager" via the callback.
    if (onUpdateActiveItem) {
      onUpdateActiveItem('pharmacy-benefits-manager');
    }
  };

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  return (
    <Box display="flex" flexDirection="row" justifyContent="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Client Information"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        onSaveExit={handleSaveAndExit}
      />
      <Box
        flex="1"
        minW="320px"
        maxW="400px"
        position="sticky"
        top={1}
        alignSelf="flex-start"
        maxH="60vh"
        overflowY="scroll"
        sx={{
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'transparent',
            borderRadius: '4px',
          },
          '&:hover::-webkit-scrollbar-thumb': {
            background: 'gray.300',
          },
          '&:hover::-webkit-scrollbar-track': {
            background: 'gray.100',
          },
        }}
      >
        <HelpCenter
          // ! helpContent and validationResults are mocked up for right now, but model the expected JSON output
          helpContent={{
            fields: [
              {
                field_name: 'Legal Name',
                help_text: 'Legal Name description here',
              },
              {
                field_name: 'Employer Address',
                help_text: 'Employer Address here',
              },
              {
                field_name: 'Employer Address 2',
                help_text: 'Employer Address 2 goes here',
              },
            ],
          }}
          validationResults={[
            {
              field_name: 'Legal Name',
              validation_message: 'Legal Name is required',
              severity: 'Error',
            },
            {
              field_name: 'Employer Address',
              validation_message: 'Employer Address is required',
              severity: 'Warning',
            },
          ]}
          subtitle={'Help Topics'}
          description={'Topics will change as you navigate through each page'}
        />
      </Box>
    </Box>
  );
};

export default ClientInformationComponent;
