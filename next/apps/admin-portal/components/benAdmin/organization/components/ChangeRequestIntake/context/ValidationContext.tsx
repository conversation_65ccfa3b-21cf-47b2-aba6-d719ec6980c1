import React, { createContext, ReactNode, useContext, useMemo } from 'react';

import {
  ValidateByPageReturn,
  ValidationResponse,
} from '../../../../Models/interfaces';
import { getUIContextFromNavigationConstant } from '../Navigation/uiContextEnum';

interface ValidationContextType {
  validationData: ValidationResponse | null;
  isLoading: boolean;
  isError: boolean;
  refetch: () => Promise<any>;
}

const ValidationContext = createContext<ValidationContextType | undefined>(
  undefined
);

interface ValidationProviderProps {
  children: ReactNode;
  validationData: ValidationResponse | null;
  isLoading: boolean;
  isError: boolean;
  refetch: () => Promise<any>;
}

export const ValidationProvider: React.FC<ValidationProviderProps> = ({
  children,
  validationData,
  isLoading,
  isError,
  refetch,
}) => {
  const contextValue: ValidationContextType = {
    validationData,
    isLoading,
    isError,
    refetch,
  };

  return (
    <ValidationContext.Provider value={contextValue}>
      {children}
    </ValidationContext.Provider>
  );
};

export const useValidationContext = (): ValidationContextType => {
  const context = useContext(ValidationContext);
  if (context === undefined) {
    throw new Error(
      'useValidationContext must be used within a ValidationProvider'
    );
  }
  return context;
};

// Hook that returns same format as useValidateByPage but filtered from context (for initial load)
export const useValidateByPageFromContext = (
  navigationConstant: string
): ValidateByPageReturn => {
  const { validationData, isError, isLoading, refetch } =
    useValidationContext();

  const uiContextInd = useMemo(
    () => getUIContextFromNavigationConstant(navigationConstant),
    [navigationConstant]
  );

  const filteredValidationData = useMemo((): ValidationResponse | null => {
    if (!validationData || !uiContextInd) return null;

    const pageData = validationData.results[uiContextInd.toString()];
    if (!pageData) return null;

    return {
      message: validationData.message,
      results: { [uiContextInd.toString()]: pageData },
    };
  }, [validationData, uiContextInd]);

  return {
    validationData: filteredValidationData,
    isError,
    isLoading,
    refetch,
  };
};
