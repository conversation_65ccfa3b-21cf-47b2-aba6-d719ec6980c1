import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  DEDUCTIBLE_ITEM,
  OTHER_CAP_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { MoopFormModal } from './moopForm';

const MoopComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const submitHandler = useSaveChangeRequestHandler(formMethods);
  const urlIndex = getIndexFromURL() || 0;

  // Watch product name to pass into form
  const productName = formMethods.watch('plan.product.name');

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };
  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  return (
    <DynamicCollectionSection
      basePath={getAccumsGeneralPaths(urlIndex).accumulation_moop}
      formMethods={formMethods}
      generalForm={<MoopFormModal productName={productName} />}
      title="Pharmacy Accumulators - Maximum Out of Pocket"
      description=""
      emptyMessage="You haven't added anything."
      skipMessage="If this Plan does not have any Maximum Out of Pocket tiers, skip to the next step."
      isOptional={true}
      modalTitle="Pharmacy Accumulators - Maximum Out of Pocket"
      onBack={() => handleNavigation(DEDUCTIBLE_ITEM)}
      onContinue={() => handleNavigation(OTHER_CAP_ITEM)}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default MoopComponent;
