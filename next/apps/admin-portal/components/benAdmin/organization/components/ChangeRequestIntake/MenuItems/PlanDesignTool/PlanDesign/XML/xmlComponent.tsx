import {
  useSaveChangeRequestHandler,
  useXMLForm,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  ACCUMULATORS_GENERAL_ITEM,
  GENERAL_ITEM,
  STANDARD_ITEM,
} from '../../../../Navigation/navigationConstants';
import { productNames } from '../../productNameConstants';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const XMLComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch, getValues } = formMethods;
  const currentDetails = watch();
  const productName = currentDetails?.plan?.product?.name;

  const baseSaveHandler = useSaveChangeRequestHandler(formMethods);

  // Create an enhanced submit handler that handles multi-plan updates
  const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Handle save and exit action
  const handleSaveAndExit = useCallback(() => {
    const currentValues = getValues();
    submitHandler(currentValues);
  }, [getValues, submitHandler]);
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useXMLForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(GENERAL_ITEM);
    }
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(
        productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
          ? STANDARD_ITEM
          : ACCUMULATORS_GENERAL_ITEM
      );
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="XML Leave Behind - Info Provided by ESI"
      formDescription=""
      subCategories={subCategories}
      onSaveExit={handleSaveAndExit}
      onContinue={handleContinue}
      onBack={handleBack}
    />
  );
};

export default XMLComponent;
