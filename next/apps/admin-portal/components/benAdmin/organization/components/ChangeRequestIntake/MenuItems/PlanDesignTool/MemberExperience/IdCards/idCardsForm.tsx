import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { idCardConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/idCardConfig';

/**
 * Remove after demo BADMIN-1494: 2025-07-03
 * Get default RX BIN value based on PBM product name
 */
const getDefaultRxBin = (productName?: string): string => {
  if (!productName) return '';

  const product = productName.toUpperCase();

  if (product.includes('CAREMARK') || product.includes('CMK')) {
    return '004336'; // CMK-360
  } else if (product.includes('EXPRESS SCRIPTS') || product.includes('ESI')) {
    return '610014'; // ESI-360
  } else if (product.includes('OPTUM') || product.includes('OPT')) {
    return '610011'; // OPT-360
  } else if (product.includes('IRX')) {
    return '028314'; // IRX-360
  }

  return '';
};

/**
 * Remove after demo BADMIN-1494: 2025-07-03
 * Get default PCN value based on PBM vendor
 */
const getDefaultPcn = (vendorName?: string): string => {
  if (!vendorName) return '';

  const vendor = vendorName.toUpperCase();

  if (vendor.includes('CAREMARK') || vendor.includes('CMK')) {
    return 'ADV'; // CMK-360
  } else if (vendor.includes('OPTUM') || vendor.includes('OPT')) {
    return 'IRX'; // OPT-360
  } else if (vendor.includes('IRX')) {
    return 'LUMN8'; // IRX-360
  }

  return '';
};

export const useIdCardsForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const {
    idCardsResponsibleMap,
    idCardTypeMap,
    yesNoNaMap,
    employeeIdSourceMap,
    idCardMailingMap,
  } = usePicklistMaps();
  const planMaterial = currentDetails?.plan?.plan_material;
  const planId = currentDetails?.plan?.plan_id;
  const productName = currentDetails?.plan?.product?.name;

  // Debug: Log product name to see what values we're getting
  console.log('🔍 PRODUCT NAME DEBUG:', {
    productName,
    planProductObject: currentDetails?.plan?.product
  });

  // Get default values based on PBM product name
  const defaultRxBin = getDefaultRxBin(productName);
  const defaultPcn = getDefaultPcn(productName);

  if (!planMaterial?.plan_id && planId) {
    formMethods?.setValue('plan.plan_material.plan_id', planId);
  }
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Responsible for ID Cards',
          'dropdownSelect',
          idCardConfig.id_card_responsible_ind,
          planMaterial?.id_card_responsible_ind,
          {
            isRequired: true,
            optionsMap: idCardsResponsibleMap,
            placeholder: 'Select Responsible for ID Cards',
            infoText:
              'Designates which entity will print member ID cards.',
          }
        ),
        defineFormField(
          'ID Card Type',
          'dropdownSelect',
          idCardConfig.id_card_type_ind,
          planMaterial?.id_card_type_ind,
          {
            isRequired: true,
            optionsMap: idCardTypeMap,
            placeholder: 'Select ID Card Type',
            infoText:
              'Specifies whether ID card has Rx information only (separate), or both Rx and Medical (combined).',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Logo on ID Cards',
          'dropdownSelect',
          idCardConfig.id_card_logo_ind,
          planMaterial?.id_card_logo_ind,
          {
            optionsMap: yesNoNaMap,
            placeholder: 'Select Logo Applicability',
            infoText:
              "Whether the client's logo will be printed on the ID card. Used only when ID Card Type is 'Separate'.",
          }
        ),
        defineFormField(
          'Extract Alternate ID',
          'dropdownSelect',
          idCardConfig.employee_id_source_ind,
          planMaterial?.employee_id_source_ind,
          {
            optionsMap: employeeIdSourceMap,
            placeholder: 'Select Alternate Id',
            infoText:
              "Generated ID cannot be used for combined card type.",
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'ID Card Mailing',
          'dropdownSelect',
          idCardConfig.id_card_mailing_ind,
          planMaterial?.id_card_mailing_ind,
          {
            isRequired: true,
            optionsMap: idCardMailingMap,
            placeholder: 'Select ID Card Mailing',
            infoText:
              "Whether ID cards will be mailed to members' homes directly, or sent to the client's HR office.",
          }
        ),
        defineFormField('PCN', 'input', idCardConfig.pcn, planMaterial?.pcn || defaultPcn, {
          isRequired: true,
          placeholder: 'Enter PCN',
          validations: z.string().max(10, 'PCN must not exceed 10 characters'),
        }),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Rx BIN',
          'input',
          idCardConfig.rx_bin,
          planMaterial?.rx_bin || defaultRxBin,
          {
            isRequired: true,
            placeholder: 'Enter Rx BIN',
            validations: z
              .string()
              .regex(/^\d+$/, 'Value must be a number')
              .max(10, 'Rx BIN must not exceed 10 digits'),
          }
        ),
      ]),
    ]),
  ];
  return { subCategories };
};
