// Remove the 'use' prefix since it's no longer a Hook
export interface CategoryMapEntry {
  title: string;
  picklistFields: (string | null)[];
}

export const EsiCategoryMap: CategoryMapEntry[] = [
  {
    title: 'Maintenance & Home Delivery',
    picklistFields: [
      'ESI-MaintenancePharmacyNetwork',
      'ESI-HomeDeliveryProgram',
      'MandatoryMailGraceFills',
    ],
  },
  {
    title: 'Specialty Network',
    picklistFields: [
      'ESI-SpecialtyPharmacyNetwork',
      'ESI-SpecialtyGraceFillsRetail',
    ],
  },
  {
    title: 'Retail Network',
    picklistFields: ['ESI-RetailPharmacyNetwork'],
  },
  {
    title: 'ESI XML Fields',
    picklistFields: [
      'EsiRetailNetwork',
      'Direct Custom ESI Network for XML File',
    ],
  },
];

export const OptumCategoryMap: CategoryMapEntry[] = [
  {
    title: 'Maintenance & Home Delivery',
    picklistFields: [
      'OPT-MaintenancePharmacyNetwork',
      'OPT-HomeDeliveryProgram',
      'MandatoryMailGraceFills',
      'Mandatory Mail Member Penalty',
    ],
  },
  {
    title: 'Specialty Network',
    picklistFields: ['OPT-SpecialtyPharmacyNetwork'],
  },
  {
    title: 'Retail Network',
    picklistFields: ['OPT-RetailPharmacyNetwork'],
  },
];

export const CareMarkCategoryMap: CategoryMapEntry[] = [
  {
    title: 'Maintenance',
    picklistFields: [
      'CMK-MaintenancePharmacyNetwork',
      'MandatoryMailGraceFills',
    ],
  },
  {
    title: 'Specialty Network',
    picklistFields: ['CMK-SpecialtyPharmacyNetwork'],
  },
  {
    title: 'Retail Network',
    picklistFields: ['CMK-RetailPharmacyNetwork'],
  },
];

export const EmptyCategoryMap: CategoryMapEntry[] = [];

/**
 * Utility function to get the appropriate category map based on the input type.
 * @param type The type of category map ('IsESI', 'IsOptum', 'IsCMK', or undefined)
 * @returns The corresponding category map
 */
export function getCategoryMap(type?: string): CategoryMapEntry[] {
  switch (type) {
    case 'IsESI':
      return EsiCategoryMap;
    case 'IsOptum':
      return OptumCategoryMap;
    case 'IsCMK':
      return CareMarkCategoryMap;
    default:
      return EmptyCategoryMap;
  }
}
