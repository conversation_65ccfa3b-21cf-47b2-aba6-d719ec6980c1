import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  ACCUMULATORS_GENERAL_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { DeductibleFormModal } from './deductibleForm';

const DeductibleComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const submitHandler = useSaveChangeRequestHandler(formMethods);
  const urlIndex = getIndexFromURL() || 0;

  const handleSaveAndExit = () => {
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleNavigation = (destination: string) => {
    onUpdateActiveItem?.(destination);
  };

  const productName = formMethods.watch('plan.product.name');

  return (
    <DynamicCollectionSection
      basePath={getAccumsGeneralPaths(urlIndex).accumulation_deductible}
      formMethods={formMethods}
      generalForm={<DeductibleFormModal productName={productName} />}
      title="Pharmacy Accumulators - Deductible"
      description=""
      emptyMessage="You haven't added anything."
      skipMessage="If this Plan does not have any In-House Pharmacies, skip to the next step."
      isOptional={true}
      modalTitle="Pharmacy Accumulators - Deductible"
      onBack={() => handleNavigation(ACCUMULATORS_GENERAL_ITEM)}
      onContinue={() => handleNavigation(MAXIMUM_OUT_OF_POCKET_ITEM)}
      onSaveExit={handleSaveAndExit}
    />
  );
};
export default DeductibleComponent;
