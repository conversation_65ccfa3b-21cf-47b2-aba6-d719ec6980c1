import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { productNames } from '../../../productNameConstants';
import { currencyValidation } from '../../../validations';
/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useAccumsGeneralForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const accumsGeneralConfig = getAccumsGeneralPaths(indexToUse);
  const planDesignDetail =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  const productName = currentDetails?.plan?.product?.name;

  const { accumTransferMap, yesNoMap, medicalIntegrationTierMap } =
    usePicklistMaps();

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Accum Transfer',
          'dropdownSelect',
          accumsGeneralConfig.accum_transfer_ind,
          planDesignDetail?.accum_transfer_ind,
          {
            infoText: 'Accum Transfer',
            optionsMap: accumTransferMap,
          }
        ),
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360
          ? [
              defineFormField(
                'Accumulators Integrated with Medical',
                'dropdownSelect',
                accumsGeneralConfig.accum_integrated_ind,
                planDesignDetail?.accum_integrated_ind,
                {
                  infoText: 'Accumulators Integrated with Medical',
                  optionsMap: yesNoMap,
                }
              ),
            ]
          : []),
      ]),
      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'Plan Max Coverage Amount',
                'input',
                accumsGeneralConfig.max_coverage_amount,
                planDesignDetail?.max_coverage_amount,
                {
                  infoText: 'Plan Max Coverage Amount',
                  placeholder: 'Enter Plan Max Coverage Amount',
                  validations: currencyValidation.optional(),
                }
              ),
              defineFormField(
                'Medical Integration Tier',
                'dropdownSelect',
                accumsGeneralConfig.medical_integration_tier_ind,
                planDesignDetail?.medical_integration_tier_ind,
                {
                  infoText: 'Medical Integration Tier',
                  optionsMap: medicalIntegrationTierMap,
                }
              ),
            ]),
          ]
        : []),
    ]),
  ];

  return {
    subCategories,
  };
}
