import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { yesNoMap } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/FieldData/maps';
import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { currencyValidation } from '../../../validations';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useAccumsGeneralForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const { accumTransferMap, medicalIntegrationTierMap } = usePicklistMaps();
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const accumsGeneralConfig = getAccumsGeneralPaths(indexToUse);
  const planDesignDetail =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Accum Transfer',
          'dropdownSelect',
          accumsGeneralConfig.accum_transfer_ind,
          planDesignDetail?.accum_transfer_ind,
          {
            isRequired: true,
            infoText: 'Accum Transfer',
            optionsMap: accumTransferMap,
          }
        ),
        defineFormField(
          'Accumulators Integrated with Medical',
          'dropdownSelect',
          accumsGeneralConfig.accum_integrated_ind,
          planDesignDetail?.accum_integrated_ind,
          {
            isRequired: true,
            infoText: 'Accumulators Integrated with Medical',
            optionsMap: yesNoMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Plan Max Coverage Amount',
          'input',
          accumsGeneralConfig.max_coverage_amount,
          planDesignDetail?.max_coverage_amount,
          {
            infoText: 'Plan Max Coverage Amount',
            placeholder: 'Enter Plan Max Coverage Amount',
            validations: currencyValidation.optional(),
          }
        ),
        defineFormField(
          'Medical Integration Tier',
          'dropdownSelect',
          accumsGeneralConfig.medical_integration_tier_ind,
          planDesignDetail?.medical_integration_tier_ind,
          {
            isRequired: true,
            infoText: 'Medical Integration Tier',
            optionsMap: medicalIntegrationTierMap,
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
