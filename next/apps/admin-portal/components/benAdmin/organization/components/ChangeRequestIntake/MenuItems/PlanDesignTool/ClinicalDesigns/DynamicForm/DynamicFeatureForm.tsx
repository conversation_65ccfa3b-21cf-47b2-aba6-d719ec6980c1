import {
  CLINICAL_DESIGN_MODE,
  useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import { Feature } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { createMultiPlanSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { usePharmacyNetworkFormFields } from '../../ProductsAndServices/CoreProducts/PharmacyNetwork/usePharmacyNetworkFormFields';
import { useDynamicFeatureFields } from './useDynamicFeatureFields';

interface DynamicFeatureFormProps {
  feature: Feature;
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  nextItemId?: string; // ID of the next item to navigate to
  previousItemId?: string;
}

/**
 * A dynamic form component that renders a form for any feature
 * Uses optimized hooks and utilities for form generation and submission
 */
const DynamicFeatureForm: React.FC<DynamicFeatureFormProps> = ({
  feature,
  formMethods,
  onUpdateActiveItem,
  nextItemId,
  previousItemId,
}) => {
  const { watch, getValues } = formMethods;
  const currentDetails = watch();

  // Get the base save handler from the form system
  const baseSaveHandler = useSaveChangeRequestHandler(formMethods);
  const applyChangesToAllPlans = !getValues(`toggles.${CLINICAL_DESIGN_MODE}`);

  // Handle save and exit action
  const handleSaveAndExit = useCallback(() => {
    const currentValues = getValues();

    if (!applyChangesToAllPlans) {
      // When toggle is active (alternate view), don't apply to all plans
      baseSaveHandler(currentValues);
    } else {
      // When toggle is not active (default view), apply to all plans
      const submitHandler = createMultiPlanSubmitHandler(
        baseSaveHandler,
        feature
      );
      submitHandler(currentValues);
    }
  }, [getValues, baseSaveHandler, feature, applyChangesToAllPlans]);

  // Handle continue action - navigate to next item if specified
  // This parmacy network item is hardcoded here since it's also a feature item but
  // doesnt belong to clinical design...we need to filter this out...making it
  // not actually dynamic. We need a way of distinguishing what belongs to clinical
  // and what belongs to products and services. Currently hard coding is the only way...
  const handleContinue = useCallback(() => {
    if (onUpdateActiveItem) {
      if (nextItemId === 'ESI_PHARMACYNETWORK_ITEM') {
        onUpdateActiveItem('ESI_PREVENTATIVELISTVACCINE_ITEM');
      } else if (nextItemId === 'OPT_PHARMACYNETWORK_ITEM') {
        onUpdateActiveItem('OPT_PREVENTATIVELISTVACCINE_ITEM');
      } else if (nextItemId === 'CMK_PHARMACYNETWORK_ITEM') {
        onUpdateActiveItem('CMK_PREVENTATIVELISTVACCINE_ITEM');
      } else if (nextItemId) {
        onUpdateActiveItem(nextItemId);
      } else {
        onUpdateActiveItem('CLINICAL_DESIGN_REVIEW');
      }
    }
  }, [onUpdateActiveItem, nextItemId]);

  // Call both hooks unconditionally
  const pharmacyNetworkFields = usePharmacyNetworkFormFields(
    feature,
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );
  const dynamicFields = useDynamicFeatureFields(
    feature,
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );
  const isPharmacyNetwork = /^Pharmacy Network$|^.*Pharmacy Network$/.test(
    feature.label || feature.name || ''
  );

  // Get form subcategories using our optimized hook
  const { subCategories } = isPharmacyNetwork
    ? pharmacyNetworkFields
    : dynamicFields;

  const handleBack = useCallback(() => {
    if (onUpdateActiveItem && previousItemId) {
      if (previousItemId === 'ESI_PHARMACYNETWORK_ITEM') {
        onUpdateActiveItem('ESI_FORMULARYINCLUSION_ITEM');
      } else if (previousItemId === 'OPT_PHARMACYNETWORK_ITEM') {
        onUpdateActiveItem('OPT_FORMULARYINCLUSION_ITEM');
      } else if (previousItemId === 'CMK_PHARMACYNETWORK_ITEM') {
        onUpdateActiveItem('CMK_FORMULARYINCLUSION_ITEM');
      } else {
        onUpdateActiveItem(previousItemId);
      }
    }
  }, [onUpdateActiveItem, previousItemId]);

  return (
    <GenericForm
      formMethods={formMethods}
      formName={feature.label || feature.name || ''}
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      {...(previousItemId && { onBack: handleBack })} // Disable Back for Clinical Design Note
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default React.memo(DynamicFeatureForm);
