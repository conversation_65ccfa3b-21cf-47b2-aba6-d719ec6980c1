import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin';
import {
  Feature,
  OrganizationDetails,
  Plan,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  buildPicklistsMap,
  getOptionsMap,
  mapFieldType,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/Configurable/fieldUtils';
import {
  buildFieldPath,
  findMatchingPlanFeatureItem,
  findMatchingPlanFeatures,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import {
  defineDropdownField,
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { useURLIndex } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/useUrlParameters';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { getIdMap, syncAcrossPlanDesigns } from './paramsUtils';

/**
 * Hook to dynamically generate form fields for a feature
 * Uses URL parameter detection and optimized field generation
 *
 * @param feature The feature object to generate fields for
 * @param currentDetails The current organization details
 * @returns An object containing the generated subCategories
 */
export function useDynamicFeatureFields(
  feature: Feature,
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<any>
) {
  // Get the index from URL with polling - this is now provided by our reusable hook
  const urlIndex = useURLIndex(undefined, { debug: false });
  const esiConditionSpecificManagementFeature =
    'ESI-ConditionSpecificManagement';
  const defaultYesESIConditionSpecificManagementFields = [
    'Cardiovascular Care',
    'Oncology Care',
    'HIV Care Value Program',
    'Multiple Sclerosis Care Value',
    'Rare Conditions Care Value',
    'React with Market Event',
  ];

  // Use memoization to prevent regenerating fields unnecessarily
  const subCategories = useMemo(() => {
    // Early return if feature is invalid
    if (!feature || !feature.feature_items) {
      return [];
    }

    // Get picklists map for quick lookup
    const picklistsMap = buildPicklistsMap(currentDetails.picklists || []);

    // Find the matching plan features
    const matchingPlanFeatures = findMatchingPlanFeatures(
      currentDetails.plan as Plan,
      feature,
      urlIndex || 0
    );

    // Create fields for all feature items
    const fields = feature.feature_items.map((item) => {
      // Find the matching plan feature item
      const matchingItem = findMatchingPlanFeatureItem(
        matchingPlanFeatures,
        item
      );

      // Determine field type based on field_type_label
      const fieldType = mapFieldType(item.field_type_label) as any;

      // Get options map for selection fields
      const optionsMap = getOptionsMap(picklistsMap, item);

      // Build the field path with explicit URL index
      const fieldPath = buildFieldPath(matchingItem, urlIndex);

      const idMap = getIdMap(fieldPath, formMethods);

      // Get the current value from the plan structure
      const currentValue = matchingItem ? matchingItem.value : '';

      if (
        feature.name === esiConditionSpecificManagementFeature &&
        defaultYesESIConditionSpecificManagementFields.includes(
          item.name || item.label
        )
      ) {
        const currentDefaultValue = formMethods?.getValues(fieldPath);
        if (!currentDefaultValue) {
          formMethods?.setValue(fieldPath, '1');
        }
      }

      // For dropdown fields, handle toggle features
      if (fieldType === 'dropdownSelect' && optionsMap) {
        // Create a toggleName based on field path for the checkbox state
        const toggleName = `${fieldPath}_differs`;

        // Use defineDropdownField for enhanced dropdown functionality
        const fieldConfig = defineDropdownField(
          item.label || '',
          fieldPath,
          currentValue,
          optionsMap,
          {
            infoText: '',
            placeholder: `Select ${item.label}`,
            isRequired: false,
            showToggle: true,
            toggleLabel: 'Differs from Plan Design',
            toggleName: toggleName,
            toggleDefaultValue: false,
            formPath: PLAN_DESIGNS_BASE_PATH,
            sync: true,
            idMap,
            syncFunction: syncAcrossPlanDesigns,
          }
        );
        return fieldConfig;
      }
      // For other field types, use the standard defineFormField
      return defineFormField(
        item.label || '',
        fieldType,
        fieldPath,
        currentValue,
        {
          infoText: '',
          placeholder: `Enter ${item.label}`,
          isRequired: false,
          ...(optionsMap &&
          ['checkboxGroup', 'radioGroup', 'dropdownSelect'].includes(fieldType)
            ? { optionsMap }
            : {}),
          // Add toggle and sync properties for input fields
          ...(fieldType === 'input'
            ? {
                showToggle: true,
                toggleLabel: 'Differs from Plan Design',
                toggleName: `${fieldPath}_differs`,
                toggleDefaultValue: false,
                formPath: PLAN_DESIGNS_BASE_PATH,
                sync: true,
                idMap,
                syncFunction: syncAcrossPlanDesigns,
              }
            : {}),
        }
      );
    });

    // Group fields into inline groups of 2
    const inlineGroups = [];
    for (let i = 0; i < fields.length; i += 2) {
      inlineGroups.push(defineInlineFieldGroup(fields.slice(i, i + 2)));
    }

    // Create a single subcategory with all fields
    return [
      defineSubCategory(feature.label || feature.name || '', '', inlineGroups),
    ];
  }, [
    feature,
    currentDetails.picklists,
    currentDetails.plan,
    urlIndex,
    formMethods,
  ]);

  return { subCategories };
}
