// useProductsAndServicesSections.ts
import {
  PLAN_DESIGN_BASE_PATH,
  useGeneralForm,
  useXMLForm,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  FormSections,
  SectionConfig,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/Completion/types';

import {
  GENERAL_ITEM,
  PATIENT_PAY_ITEM,
  PHARMACY_ACCUMULATORS_ITEM,
  PLAN_DESIGN_LIST_ITEM,
  PLAN_DESIGN_MODE,
  XML_ITEM,
} from '../../../../Navigation/navigationConstants';
import { useCompoundForm } from '../PatientPay/Compound/compoundForm';
import { useDispenseAsWrittenForm } from '../PatientPay/Dispense/dispenseForm';
import { useAccumsGeneralForm } from '../PharmacyAccumulators/General/accumsGeneralForm';
// Other imports...

export function usePlanDesignSections(
  currentDetails?: Partial<OrganizationDetails>
): FormSections {
  const { subCategories: generalCategorys } = useGeneralForm(
    currentDetails || {}
  );

  const { subCategories: xmlCategorys } = useXMLForm(currentDetails || {});

  const { subCategories: generalAccumulatorsCategorys } = useAccumsGeneralForm(
    currentDetails || {}
  );

  const { subCategories: compoundsCategorys } = useCompoundForm(
    currentDetails || {}
  );

  const { subCategories: dispenseCategorys } = useDispenseAsWrittenForm(
    currentDetails || {}
  );

  const sections: SectionConfig[] = [
    {
      id: PLAN_DESIGN_LIST_ITEM,
      title: 'Plan Design List',
      path: PLAN_DESIGN_BASE_PATH,
      isCollection: true,
    },
    {
      id: GENERAL_ITEM,
      title: 'General',
      relatedSections: [...generalCategorys],
      isCollection: false,
    },
    {
      id: XML_ITEM,
      title: 'XML Leave Behind - Info Provided',
      relatedSections: [...xmlCategorys],
      isCollection: false,
    },
    {
      id: PATIENT_PAY_ITEM,
      title: 'Patient Pay',
      relatedSections: [...compoundsCategorys, ...dispenseCategorys],
      isCollection: false,
    },
    {
      id: PHARMACY_ACCUMULATORS_ITEM,
      title: 'Pharmacy Accumulators',
      relatedSections: [...generalAccumulatorsCategorys],
      isCollection: false,
    },
  ];

  return { sections, id: PLAN_DESIGN_MODE };
}
