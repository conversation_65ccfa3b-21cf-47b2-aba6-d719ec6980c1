import { dateStyles } from 'apps/admin-portal/components/benAdmin';
import {
  CostShareDesign,
  CostShareTier,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, UseFormReturn } from 'react-hook-form';

import StepModal from '../StepModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import StepOneTemplate from './StepOneTemplate';
import StepThreeTemplate from './StepThreeTemplate';
import StepTwoTemplate, { DaySupplyOption } from './StepTwoTemplate';

// Scenario type
export enum Scenario {
  Retail = 1,
  Mail = 2,
  Custom = 3,
}

// Update form data interface
export interface CostShareFormData {
  pharmacyChannel: string;
  networkStatus: string;
  customPharmacyName?: string;
  patientPayType: string;
  documentGeneration: string[];
  daySupplyOptions: DaySupplyOption[];
  costShareTiers: CostShareTier[];
  drugListInd?: string | null;
}

interface CostShareModalControllerProps {
  isOpen: boolean;
  onClose: () => void;
  formMethods: UseFormReturn<any>;
  basePath: string;
  selectedIndexes: number[];
  daySupply: any;
  pharmacyChannel?: any;
  networkStatus?: any;
  costShareType?: any;
  dateStyles?: any;
  drugList?: any;
}

// Helper function to sort days supply options
const sortDaySupplyOptions = (
  a: DaySupplyOption,
  b: DaySupplyOption
): number => {
  const aIsMultiplier = a.label.toLowerCase().includes('multiplier');
  const bIsMultiplier = b.label.toLowerCase().includes('multiplier');

  if (aIsMultiplier && !bIsMultiplier) return 1;
  if (!aIsMultiplier && bIsMultiplier) return -1;

  if (aIsMultiplier && bIsMultiplier) {
    const aMultValue = parseFloat(a.label.match(/(\d+\.?\d*)x/)?.[1] || '0');
    const bMultValue = parseFloat(b.label.match(/(\d+\.?\d*)x/)?.[1] || '0');
    return bMultValue - aMultValue;
  }

  const aStart = parseInt(a.value.split('-')[0]);
  const bStart = parseInt(b.value.split('-')[0]);
  if (aStart !== bStart) return aStart - bStart;

  const aEnd = parseInt(a.value.split('-')[1]);
  const bEnd = parseInt(b.value.split('-')[1]);
  return aEnd - bEnd;
};

// Helper function to convert string date to Date object
const parseDate = (dateString: string | null): Date | undefined => {
  if (!dateString) return undefined;

  try {
    const date = new Date(dateString);
    return Number.isNaN(date.getTime()) ? undefined : date;
  } catch (error) {
    return undefined;
  }
};

// Helper function to format Date to string (YYYY-MM-DD)
const formatDate = (date: Date | undefined): string | null => {
  if (!date) return null;

  try {
    return date.toISOString().split('T')[0];
  } catch (error) {
    return null;
  }
};

/**
 * Controller component for the Cost Share Modal
 * Manages state and flow between the different steps
 * Updates form data at specific indexes
 */
export const CostShareModalController: React.FC<
  CostShareModalControllerProps
> = ({
  isOpen,
  onClose,
  formMethods,
  basePath,
  selectedIndexes,
  daySupply,
  pharmacyChannel,
  networkStatus,
  costShareType,
  drugList,
}) => {
  // Core state
  const [currentStep, setCurrentStep] = useState(1);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [tierNameError, setTierNameError] = useState<string | null>(null);

  // Watch the original cost share designs
  const originalDesigns = formMethods.watch(basePath) as CostShareDesign[];

  // Derived state
  const [tempDesigns, setTempDesigns] = useState<CostShareDesign[]>([]);
  const [daySupplyOptions, setDaySupplyOptions] = useState<DaySupplyOption[]>(
    []
  );
  const [tierName, setTierName] = useState('');
  const [originalTierName, setOriginalTierName] = useState('');

  // New date state variables
  const [effectiveDate, setEffectiveDate] = useState<Date | undefined>(
    undefined
  );
  const [expirationDate, setExpirationDate] = useState<Date | undefined>(
    undefined
  );

  // Get values from the first selected design to use as reference for UI
  const primaryDesign = useMemo(
    () => (selectedIndexes.length > 0 ? tempDesigns[selectedIndexes[0]] : null),
    [selectedIndexes, tempDesigns]
  );

  // Derive UI state from primary design
  const pharmacyChannelValue = useMemo(
    () => primaryDesign?.pharmacy_channel_ind?.toString() || '',
    [primaryDesign]
  );
  const networkStatusValue = useMemo(
    () => primaryDesign?.network_status_ind?.toString() || '1',
    [primaryDesign]
  );
  const customPharmacyName = useMemo(
    () => primaryDesign?.pharmacy_list || '',
    [primaryDesign]
  );
  const patientPayType = useMemo(
    () => primaryDesign?.cost_share_type_ind?.toString() || '',
    [primaryDesign]
  );
  const drugListInd = useMemo(() => {
    const value = primaryDesign?.drug_list_ind || null;
    return value;
  }, [primaryDesign]);

  // Check if we're in edit mode by looking at initial form data
  const isEditMode = useMemo(() => {
    if (!selectedIndexes || !selectedIndexes.length || !originalDesigns)
      return false;

    const firstSelectedDesign = originalDesigns[selectedIndexes[0]];
    if (!firstSelectedDesign) return false;

    return !!(
      firstSelectedDesign.name &&
      firstSelectedDesign.pharmacy_channel_ind &&
      firstSelectedDesign.cost_share_type_ind
    );
  }, [selectedIndexes, originalDesigns]);

  // Check for duplicate tier names
  const isDuplicateName = useCallback(
    (name: string): boolean => {
      if (name === originalTierName) return false;
      return originalDesigns.some((design) => design.name === name);
    },
    [originalDesigns, originalTierName]
  );

  const isTierDataValid = (
    tiers: CostShareTier[],
    patientPayType: string
  ): boolean => {
    if (!tiers || tiers.length === 0) return false;

    return tiers.every((tier) => {
      // For co-pay type, we need co_pay_amount
      if (patientPayType === '1') {
        return !!tier.co_pay_amount;
      }

      // For co-insurance type, we need co_insurance_pct
      if (patientPayType === '2') {
        return !!tier.co_insurance_pct;
      }

      // For blended type, we need co_insurance_pct
      // and co_pay_amount if lesser/greater is selected
      if (patientPayType === '3') {
        // Basic validation: co_insurance_pct is required
        if (!tier.co_insurance_pct) return false;

        // If lesser/greater is selected, co_pay_amount is required
        if (
          (String(tier.co_insurance_ltgt_ind) === '0' ||
            String(tier.co_insurance_ltgt_ind) === '1') &&
          !tier.co_pay_amount
        ) {
          return false;
        }

        return true;
      }

      return false;
    });
  };

  // Initialize temp state when modal opens
  useEffect(() => {
    if (!isOpen) return;

    setCurrentStep(1);
    setTierNameError(null);

    const existingName =
      selectedIndexes?.length > 0
        ? originalDesigns[selectedIndexes[0]]?.name || ''
        : '';

    setTierName(existingName);
    setOriginalTierName(existingName);

    const clonedDesigns = JSON.parse(JSON.stringify(originalDesigns));
    const referenceDesign =
      selectedIndexes?.length > 0 ? originalDesigns[selectedIndexes[0]] : null;

    if (selectedIndexes) {
      selectedIndexes.forEach((index) => {
        if (!clonedDesigns[index] && referenceDesign) {
          clonedDesigns[index] = JSON.parse(JSON.stringify(referenceDesign));
        }
      });
    }

    setTempDesigns(clonedDesigns);

    // Initialize date fields from the primary design
    if (selectedIndexes?.length > 0 && clonedDesigns[selectedIndexes[0]]) {
      const design = clonedDesigns[selectedIndexes[0]];
      setEffectiveDate(
        parseDate(dayjs(design.effective_date).format('YYYY-MM-DD'))
      );
      setExpirationDate(parseDate(design.expiration_date));
    }

    if (daySupply && selectedIndexes?.length > 0) {
      const initialOptions = Object.entries(daySupply as Record<string, string>)
        .map(([value, label]) => ({
          id: value,
          checked:
            clonedDesigns[selectedIndexes[0]]?.cost_share_tiers?.some(
              (tier: CostShareTier) => tier.days_supply === label
            ) ?? false,
          value: value,
          label: label,
        }))
        .sort(sortDaySupplyOptions);
      setDaySupplyOptions(initialOptions);
    }
  }, [isOpen, originalDesigns, selectedIndexes, daySupply]);

  // Sync handler - updates all selected designs with the same values
  const syncSelectedDesigns = useCallback(
    (updater: (design: CostShareDesign) => CostShareDesign) => {
      setTempDesigns((prev) => {
        const updated = [...prev];
        selectedIndexes.forEach((index) => {
          if (index >= 0 && index < updated.length) {
            updated[index] = updater(updated[index]);
          }
        });
        return updated;
      });
    },
    [selectedIndexes]
  );

  // Date handlers
  const handleEffectiveDateChange = useCallback(
    (date: Date | undefined) => {
      setEffectiveDate(date);
      syncSelectedDesigns((design) => ({
        ...design,
        effective_date: formatDate(date),
      }));
    },
    [syncSelectedDesigns]
  );

  const handleExpirationDateChange = useCallback(
    (date: Date | undefined) => {
      setExpirationDate(date);
      syncSelectedDesigns((design) => ({
        ...design,
        expiration_date: formatDate(date),
      }));
    },
    [syncSelectedDesigns]
  );

  // Handlers
  const handlePharmacyChannelChange = useCallback(
    (value: string) => {
      syncSelectedDesigns((design) => ({
        ...design,
        pharmacy_channel_ind: parseInt(value, 10),
        pharmacy_list: value === '5' ? design.pharmacy_list : null,
      }));
    },
    [syncSelectedDesigns]
  );

  const handlePatientPayTypeChange = useCallback(
    (value: string) => {
      syncSelectedDesigns((design) => {
        const updatedTiers = (design.cost_share_tiers || []).map((tier) => ({
          ...tier,
          co_pay_amount: ['1', '3'].includes(value) ? null : null,
          co_insurance_pct: ['2', '3'].includes(value) ? null : null,
          co_insurance_ltgt_ind: value === '3' ? null : null,
          co_insurance_min_amount: value === '2' ? null : null,
          co_insurance_max_amount: value === '2' ? null : null,
        }));

        return {
          ...design,
          cost_share_type_ind: parseInt(value, 10),
          cost_share_tiers: updatedTiers,
        };
      });
    },
    [syncSelectedDesigns]
  );

  const handleNetworkStatusChange = useCallback(
    (value: string) => {
      syncSelectedDesigns((design) => ({
        ...design,
        network_status_ind: parseInt(value, 10),
      }));
    },
    [syncSelectedDesigns]
  );

  const handleCustomPharmacyNameChange = useCallback(
    (value: string) => {
      syncSelectedDesigns((design) => ({
        ...design,
        pharmacy_list: value,
      }));
    },
    [syncSelectedDesigns]
  );

  // New handler for Drug List changes
  const handleDrugListChange = useCallback(
    (value: string) => {
      // First update the temp designs
      const updatedDesigns = [...tempDesigns];
      selectedIndexes.forEach((index) => {
        if (updatedDesigns[index]) {
          updatedDesigns[index] = {
            ...updatedDesigns[index],
            drug_list_ind: value === '' ? null : value,
          };
        }
      });

      // Update state immediately
      setTempDesigns(updatedDesigns);

      // Then sync to form
      formMethods.setValue(basePath, updatedDesigns, {
        shouldDirty: true,
        shouldValidate: true,
      });
    },
    [tempDesigns, selectedIndexes, formMethods, basePath]
  );

  const handleDaySupplyChange = useCallback(
    (option: DaySupplyOption) => {
      const updatedOptions = daySupplyOptions.map((opt) =>
        opt.id === option.id ? { ...opt, checked: !opt.checked } : opt
      );

      syncSelectedDesigns((design) => {
        const currentTiers = [...(design.cost_share_tiers || [])];
        if (!option.checked) {
          const existingNullTier = currentTiers.find(
            (tier) =>
              tier.days_supply === null &&
              tier.co_pay_amount === null &&
              tier.co_insurance_pct === null &&
              tier.co_insurance_ltgt_ind === null &&
              tier.co_insurance_max_amount === null &&
              tier.co_insurance_min_amount === null
          );

          if (existingNullTier) {
            existingNullTier.days_supply = option.label;
          } else {
            currentTiers.push({
              days_supply: option.label,
              co_pay_amount: null,
              co_insurance_pct: null,
              co_insurance_ltgt_ind: null,
              co_insurance_max_amount: null,
              co_insurance_min_amount: null,
            });
          }
        } else {
          const tierIndex = currentTiers.findIndex(
            (tier) => tier.days_supply === option.label
          );
          if (tierIndex !== -1) {
            currentTiers.splice(tierIndex, 1);
          }
        }
        return {
          ...design,
          cost_share_tiers: currentTiers,
        };
      });

      setDaySupplyOptions(updatedOptions);
    },
    [daySupplyOptions, syncSelectedDesigns]
  );

  const handleTierChange = useCallback(
    (tierIndex: number, field: keyof CostShareTier, value: any) => {
      syncSelectedDesigns((design) => {
        const currentTiers = [...(design.cost_share_tiers || [])];
        if (currentTiers[tierIndex]) {
          currentTiers[tierIndex] = {
            ...currentTiers[tierIndex],
            [field]: value,
          };
        }
        return {
          ...design,
          cost_share_tiers: currentTiers,
        };
      });
    },
    [syncSelectedDesigns]
  );

  const handleTierNameChange = useCallback(
    (value: string) => {
      setTierName(value);

      if (!value.trim()) {
        setTierNameError('Tier Name is required');
      } else if (isDuplicateName(value.trim())) {
        setTierNameError('This Tier Name already exists');
      } else if (value.trim().length > 255) {
        setTierNameError('Value cannot exceed 255 characters');
      } else {
        setTierNameError(null);
      }

      syncSelectedDesigns((design) => ({
        ...design,
        name: value,
      }));
    },
    [syncSelectedDesigns, isDuplicateName]
  );

  const handleDelete = useCallback(() => {
    const currentDesigns = formMethods.getValues(basePath) || [];
    const updatedDesigns = currentDesigns.filter(
      (_: CostShareDesign, index: number) => !selectedIndexes.includes(index)
    );

    formMethods.setValue(basePath, updatedDesigns, {
      shouldDirty: true,
      shouldValidate: true,
    });

    setIsDeleteModalOpen(false);
    onClose();
  }, [formMethods, basePath, selectedIndexes, onClose]);

  const handleFinish = useCallback(() => {
    const effective_date = formMethods.getValues('plan.effective_date');
    const currentDesigns = formMethods.getValues(basePath) || [];
    const nullIndexes = currentDesigns.reduce(
      (acc: number[], design: CostShareDesign, index: number) => {
        if (!design) return acc;

        const isNullDesign = Object.entries(design).every(([key, value]) => {
          if (key === 'cost_share_tiers') {
            return (
              !value ||
              value.length === 0 ||
              value.every((tier: any) =>
                Object.values(tier).every(
                  (v) => v === null || v === undefined || v === ''
                )
              )
            );
          }
          return value === null || value === undefined || value === '';
        });

        if (isNullDesign) {
          acc.push(index);
        }
        return acc;
      },
      []
    );

    const updatedDesigns = [...currentDesigns];
    selectedIndexes.forEach((selectedIndex, i) => {
      if (selectedIndex >= 0 && selectedIndex < tempDesigns.length) {
        // Use the effective_date from the tempDesigns if it exists,
        // otherwise fall back to the plan effective_date
        const designEffectiveDate =
          tempDesigns[selectedIndex].effective_date || effective_date;

        const updatedDesign = {
          ...tempDesigns[selectedIndex],
          effective_date: designEffectiveDate,
        };

        if (nullIndexes[i] !== undefined) {
          updatedDesigns[nullIndexes[i]] = updatedDesign;
        } else {
          updatedDesigns[selectedIndex] = updatedDesign;
        }
      }
    });

    formMethods.setValue(basePath, updatedDesigns, {
      shouldDirty: true,
      shouldValidate: true,
    });

    onClose();
  }, [formMethods, basePath, tempDesigns, selectedIndexes, onClose]);

  const handleContinue = useCallback(() => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      handleFinish();
    }
  }, [currentStep, handleFinish]);

  const isContinueDisabled = useCallback(() => {
    if (currentStep === 1) {
      if (!tierName.trim() || tierNameError || !pharmacyChannelValue)
        return true;
      if (pharmacyChannelValue === '5' && !customPharmacyName.trim())
        return true;
      if (!patientPayType) return true;
      // Require effective date
      if (!effectiveDate) return true;
    } else if (currentStep === 2) {
      return !daySupplyOptions.some((option) => option.checked);
    } else if (currentStep === 3) {
      const selectedOptions = daySupplyOptions.filter((opt) => opt.checked);
      if (selectedOptions.length === 0) return true;

      // Validate tiers based on patient pay type
      if (!primaryDesign?.cost_share_tiers) return true;

      return !isTierDataValid(primaryDesign.cost_share_tiers, patientPayType);
    }
    return false;
  }, [
    currentStep,
    tierName,
    tierNameError,
    pharmacyChannelValue,
    customPharmacyName,
    patientPayType,
    daySupplyOptions,
    primaryDesign?.cost_share_tiers,
    effectiveDate,
  ]);

  const getPatientPayTypeLabel = useCallback(() => {
    const selectedType = costShareType?.CostShareType?.find(
      (type: any) => type.value === patientPayType
    );
    return selectedType?.label || 'Configure Cost Share';
  }, [costShareType, patientPayType]);

  const documentGeneration = useMemo(
    () => (primaryDesign?.include_pbc_ind === 0 ? ['Include in PBC'] : []),
    [primaryDesign?.include_pbc_ind]
  );

  const renderStepContent = useCallback(() => {
    switch (currentStep) {
      case 1:
        return (
          <StepOneTemplate
            pharmacyChannel={pharmacyChannelValue}
            onPharmacyChannelChange={handlePharmacyChannelChange}
            patientPayType={patientPayType}
            onPatientPayTypeChange={handlePatientPayTypeChange}
            networkStatus={networkStatusValue}
            onNetworkStatusChange={handleNetworkStatusChange}
            customPharmacyName={customPharmacyName}
            onCustomPharmacyNameChange={handleCustomPharmacyNameChange}
            documentGeneration={documentGeneration}
            onDocumentGenerationChange={(values) => {
              syncSelectedDesigns((design) => ({
                ...design,
                include_pbc_ind: values.includes('Include in PBC') ? 0 : 1,
              }));
            }}
            tierName={tierName}
            onTierNameChange={handleTierNameChange}
            tierNameError={tierNameError}
            onTierNameErrorChange={setTierNameError}
            effectiveDate={effectiveDate}
            onEffectiveDateChange={handleEffectiveDateChange}
            expirationDate={expirationDate}
            onExpirationDateChange={handleExpirationDateChange}
            dateStyles={dateStyles}
            pharmacyChannelOptions={pharmacyChannel}
            networkStatusOptions={networkStatus}
            costShareTypeOptions={costShareType}
            drugList={drugList}
            drugListInd={drugListInd}
            onDrugListChange={handleDrugListChange}
            existingTierNames={
              originalDesigns
                ?.filter((item) => item.name)
                .map((item) => item.name)
                .filter((name): name is string => name !== null) || []
            }
            currentId={primaryDesign?.cost_share_design_id || null}
            originalName={originalTierName}
          />
        );
      case 2:
        return (
          <StepTwoTemplate
            daySupplyOptions={daySupplyOptions}
            onDaySupplyChange={handleDaySupplyChange}
            title="Select days supply (all that apply)"
          />
        );
      case 3:
        return (
          <StepThreeTemplate
            tiers={primaryDesign?.cost_share_tiers || []}
            onTierChange={handleTierChange}
            patientPayType={patientPayType}
            selectedDaySupplyOptions={daySupplyOptions}
            title={getPatientPayTypeLabel()}
          />
        );
      default:
        return null;
    }
  }, [
    currentStep,
    pharmacyChannelValue,
    handlePharmacyChannelChange,
    patientPayType,
    handlePatientPayTypeChange,
    networkStatusValue,
    handleNetworkStatusChange,
    customPharmacyName,
    handleCustomPharmacyNameChange,
    documentGeneration,
    tierName,
    handleTierNameChange,
    tierNameError,
    effectiveDate,
    handleEffectiveDateChange,
    expirationDate,
    handleExpirationDateChange,
    pharmacyChannel,
    networkStatus,
    costShareType,
    drugList,
    drugListInd,
    handleDrugListChange,
    originalDesigns,
    primaryDesign?.cost_share_design_id,
    primaryDesign?.cost_share_tiers,
    originalTierName,
    daySupplyOptions,
    handleDaySupplyChange,
    handleTierChange,
    getPatientPayTypeLabel,
    syncSelectedDesigns,
  ]);

  return (
    <FormProvider {...formMethods}>
      <StepModal
        isOpen={isOpen}
        onClose={onClose}
        title={`${primaryDesign?.tier_ind || ''}${
          currentStep > 1 ? ` - Step ${currentStep}` : ''
        }`}
        currentStep={currentStep}
        totalSteps={3}
        onBack={
          currentStep > 1 ? () => setCurrentStep((prev) => prev - 1) : undefined
        }
        onCancel={onClose}
        onContinue={handleContinue}
        onDelete={() => setIsDeleteModalOpen(true)}
        continueButtonLabel={currentStep === 3 ? 'Finish' : 'Continue'}
        isBackDisabled={currentStep === 1}
        isContinueDisabled={isContinueDisabled()}
        customHeight="auto"
        customWidth="900px"
        isEdit={isEditMode}
      >
        {renderStepContent()}
      </StepModal>

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
      />
    </FormProvider>
  );
};

export default CostShareModalController;
