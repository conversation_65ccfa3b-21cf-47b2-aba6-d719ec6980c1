import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { transitionConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/transitionConfig';

const percentValidation = z
  .string()
  .regex(/^(\d{1,3})(\.\d{1,2})?$/, {
    message: 'Must be a valid percentage with up to 2 decimal places',
  })
  .refine((value) => parseFloat(value) <= 100, {
    message: 'Percentage cannot exceed 100',
  })
  .transform((val) => parseFloat(val));

export const useTransitionFilesForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const { accumTransferMap, yesNoMap, yesNoEsiMap } = usePicklistMaps();

  const plan_transition = currentDetails?.plan?.plan_transition;
  const currentPBM = currentDetails?.plan?.product?.vendor?.legal_entity_id;

  const planId = currentDetails?.plan?.plan_id;
  if (!plan_transition?.plan_id && planId) {
    formMethods?.setValue('plan.plan_transition.plan_id', planId);
  }
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          '6 Months Claims Files',
          'dropdownSelect',
          transitionConfig.historical_claims_ind,
          currentDetails?.plan?.plan_transition?.historical_claims_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText:
              'Specifies if a historical claims file will be obtained for this client.',
          }
        ),
        defineFormField(
          'ORT Transition File',
          'dropdownSelect',
          transitionConfig.ort_transition_file_ind,
          currentDetails?.plan?.plan_transition?.ort_transition_file_ind,
          {
            isRequired: true,
            optionsMap: yesNoEsiMap,
            placeholder: 'Select ORT Transition File',
            infoText:
              'Specifies if an Open Refill Transfer file will be obtatined (transfers mail order refills to the new PBM mail order pharmacy).',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Mail Order Percentage',
          'input',
          transitionConfig.mail_order_percentage,
          currentDetails?.plan?.plan_transition?.mail_order_percentage,
          {
            placeholder: 'Enter Mail Order Percentage',
            infoText:
              'Estimated percentage of members currently using mail order/home delivery.',
            validations: percentValidation.optional(),
          }
        ),
        defineFormField(
          'PA File',
          'dropdownSelect',
          transitionConfig.pa_file_ind,
          currentDetails?.plan?.plan_transition?.pa_file_ind,
          {
            isRequired: true,
            optionsMap: yesNoEsiMap,
            placeholder: 'Select PA File',
            infoText:
              'Whether or not a Prior Authorization file will be obtained for this client.',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Outbound Claims Files',
          'input',
          transitionConfig.outbound_claims_file,
          currentDetails?.plan?.plan_transition?.outbound_claims_file,
          {
            placeholder: 'Enter Outbound Claims Files',
            infoText:
              'Do claims files need to be sent to another vendor, and if so, what vendor?',
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
          }
        ),
        defineFormField(
          'Carrier to Carrier Process',
          'dropdownSelect',
          transitionConfig.carrier_to_carrier_ind,
          currentDetails?.plan?.plan_transition?.carrier_to_carrier_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText:
              'Moves existing Caremark clients to the RxB/CMK arrangement .',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Patient Transfer Process',
          'dropdownSelect',
          transitionConfig.patient_transfer_ind,
          currentDetails?.plan?.plan_transition?.patient_transfer_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            placeholder: 'Select Yes/No',
            infoText:
              'When an ESI client moves their plan to RxB/ESI, a Patient Profile Transfer (PPT) file is requested to move all active Prior Authorizations, Mail, and Specialty Open Refills with the member.',
          }
        ),
        defineFormField(
          'Transition Notes',
          'input',
          transitionConfig.notes,
          currentDetails?.plan?.plan_transition?.notes,
          {
            placeholder: 'Enter your notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
          }
        ),
      ]),
    ]),
  ];
  if (currentPBM === 2)
    // Express Scripts
    subCategories.push(
      defineSubCategory('ESI Only', '', [
        defineInlineFieldGroup([
          defineFormField(
            'Accum Transfer',
            'dropdownSelect',
            transitionConfig.accum_transfer_ind,
            currentDetails?.plan?.plan_transition?.accum_transfer_ind,
            {
              isRequired: true,
              optionsMap: accumTransferMap,
              placeholder: 'Select Accum Transfer',
              infoText:
                'Specifies whether accums transfer happens at ESI at the individual contract level, or the whole carrier level (rarely used).',
            }
          ),
        ]),
      ])
    );
  else if (currentPBM === 1)
    // Caremark
    subCategories.push(
      defineSubCategory('CMK', '', [
        defineInlineFieldGroup([
          defineFormField(
            'Follow Me Logic Process',
            'dropdownSelect',
            transitionConfig.follow_me_ind,
            currentDetails?.plan?.plan_transition?.follow_me_ind,
            {
              isRequired: true,
              optionsMap: yesNoMap,
              placeholder: 'Select Yes/No',
              infoText:
                'For CMK to CMK transitions. Historical claims, PAs, and other member history remains visible after transition.',
            }
          ),
        ]),
      ])
    );
  return { subCategories };
};
