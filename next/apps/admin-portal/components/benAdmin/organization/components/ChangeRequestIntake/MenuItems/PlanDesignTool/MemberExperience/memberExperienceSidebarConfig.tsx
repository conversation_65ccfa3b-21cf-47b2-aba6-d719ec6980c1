import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';

import {
  ID_CARDS_ITEM,
  MEMBER_EXPERIENCE_MODE,
  MEMBER_SERVICES_ITEM,
  MEMBER_SERVICES_REVIEW_ITEM,
  TRANSITION_FILES_AND_DETAILS_ITEM,
  WELCOME_KIT_AND_LETTERS_ITEM,
} from '../../../Navigation/navigationConstants';
import IdCardsComponent from './IdCards/idCardsComponent';
import MemberServicesComponent from './MemberServices/memberServicesComponent';
import ReviewMemberServices from './MemberServicesReview/memberServicesReview';
import TransitionFilesComponent from './TransitionFilesAndDetails/transitionFilesComponent';
import WelcomeKitComponent from './WelcomeKitAndLetters/welcomeKitComponent';

export const getMemberServicesSidebarConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<OrganizationDetails>,
  navigateFn?: (section: string, tab: string) => void,
  activeItemId?: string // Add optional activeItemId parameter for consistency
): SidebarConfig => {
  const navigateToMemberExperienceItem = (id: string) =>
    navigateFn ? navigateFn(MEMBER_EXPERIENCE_MODE, id) : undefined;

  const result: SidebarConfig = {
    sections: [
      {
        title: 'MEMBER EXPERIENCE',
        items: [
          {
            id: ID_CARDS_ITEM,
            label: 'ID cards',
            component: (
              <IdCardsComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToMemberExperienceItem}
              />
            ),
          },
          {
            id: TRANSITION_FILES_AND_DETAILS_ITEM,
            label: 'Transition Files and Details',
            component: (
              <TransitionFilesComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToMemberExperienceItem}
              />
            ),
          },
          {
            id: WELCOME_KIT_AND_LETTERS_ITEM,
            label: 'Welcome Kit & Letters',
            component: (
              <WelcomeKitComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToMemberExperienceItem}
              />
            ),
          },
          {
            id: MEMBER_SERVICES_ITEM,
            label: 'Member Services Providers',
            component: (
              <MemberServicesComponent
                formMethods={formMethods}
                onUpdateActiveItem={navigateToMemberExperienceItem}
              />
            ),
          },
          {
            id: MEMBER_SERVICES_REVIEW_ITEM,
            label: 'Review Member Experience',
            component: (
              <ReviewMemberServices
                formMethods={formMethods}
                onUpdateActiveItem={navigateToMemberExperienceItem}
              />
            ),
          },
        ],
      },
    ],
  };

  return result;
};
