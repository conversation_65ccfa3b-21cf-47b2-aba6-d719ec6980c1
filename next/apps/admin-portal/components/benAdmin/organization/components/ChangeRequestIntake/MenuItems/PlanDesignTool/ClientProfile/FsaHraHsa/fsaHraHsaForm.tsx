import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { clientConfig } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/clientConfig';

export const useFsaHraHsaForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const { yesNoMap } = usePicklistMaps();
  const plan_transition = currentDetails?.plan?.plan_transition;
  const planId = currentDetails?.plan?.plan_id;
  if (!plan_transition?.plan_id && planId) {
    formMethods?.setValue('plan.plan_transition.plan_id', planId);
  }
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'FSA',
          'dropdownSelect',
          clientConfig.fsa_ind,
          plan_transition?.fsa_ind,
          {
            optionsMap: yesNoMap,
            infoText: 'Does the group have a Flexible Spending Account?',
          }
        ),
        defineFormField(
          'FSA Substantiation File Needed',
          'input',
          clientConfig.fsa_substantiation_needed,
          plan_transition?.fsa_substantiation_needed,
          {
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
            infoText:
              'If FSA is not managed through Debit Card, will a file to substantiate claims be needed? If yes, who is the vendor?',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'HRA',
          'dropdownSelect',
          clientConfig.hra_ind,
          plan_transition?.hra_ind,
          {
            optionsMap: yesNoMap,
            infoText: 'Does the group have a Health Reimbursement Account?',
          }
        ),
        defineFormField(
          'HRA Substantiation File Needed',
          'input',
          clientConfig.hra_substantiation_needed,
          plan_transition?.hra_substantiation_needed,
          {
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
            infoText:
              'If HRA is not managed through Debit Card, will a file to substantiate claims be needed? If yes, who is the vendor?',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'HSA',
          'dropdownSelect',
          clientConfig.hsa_ind,
          plan_transition?.hsa_ind,
          {
            optionsMap: yesNoMap,
            infoText: 'Does the group have a Health Savings Account?',
          }
        ),
        defineFormField(
          'HSA Substantiation File Needed',
          'input',
          clientConfig.hsa_substantiation_needed,
          plan_transition?.hsa_substantiation_needed,
          {
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
            infoText:
              'If HSA is not managed through Debit Card, will a file to substantiate claims be needed? If yes, who is the vendor?',
          }
        ),
      ]),
    ]),
  ];
  return { subCategories };
};
