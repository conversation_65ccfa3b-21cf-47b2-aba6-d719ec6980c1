import {
  planYearRenewalMap,
  yesNoMap,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineForm<PERSON>ield,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { getGeneralPaths } from '../../../../../Tabs/PlanDesign/PlanDesign/Config/generalConfig';

/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useGeneralForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const { healthcareReformStatusMap, planDesignTypeMap } = usePicklistMaps();
  const urlIndex = getIndexFromURL();
  const { diabeticSuppliesCopayMap, prepackagedCopaysMap } = usePicklistMaps();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const designDetails =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  // Use existing config with the determined index
  const generalConfig = getGeneralPaths(indexToUse);

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Name',
          'input',
          generalConfig.name,
          currentDetails?.plan?.plan_designs?.[indexToUse]?.name,
          {
            isRequired: true,
            placeholder: 'Benefit Name',
            validations: z
              .string()
              .max(255, 'Benefit Name must be less than 255 characters'),
          }
        ),
        defineFormField(
          'Benefit Order',
          'input',
          generalConfig.sort_seq,
          currentDetails?.plan?.plan_designs?.[indexToUse]?.sort_seq,
          {
            isRequired: true,
            infoText:
              'The order in which benefits will appear on the PDX (1,2,3, etc).',
            placeholder: 'Benefit Order',
            validations: z
              .number()
              .int('Must be an integer')
              .min(-32768, 'Value must be at least -32,768')
              .max(32767, 'Value must be at most 32,767'),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Plan Type',
          'dropdownSelect',
          generalConfig.type_ind,
          currentDetails?.plan?.plan_designs?.[indexToUse]?.type_ind,
          {
            optionsMap: planDesignTypeMap,
            isRequired: true,
            infoText: 'Denotes whether the benefit is a copay plan or HDHP.',
          }
        ),
        defineFormField(
          'Plan Design Benefit Period',
          'dropdownSelect',
          generalConfig.benefit_period_ind,
          designDetails?.benefit_period_ind,
          {
            optionsMap: planYearRenewalMap,
            isRequired: true,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Effective Date',
          'datepicker',
          generalConfig.effective_date,
          designDetails?.effective_date,
          {
            isRequired: true,
            validations: z.date(),
          }
        ),
        defineFormField(
          'End Date',
          'datepicker',
          generalConfig.expiration_date,
          designDetails?.expiration_date,
          {
            validations: z.date().optional(),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Union Benefit',
          'dropdownSelect',
          generalConfig.union_benefit_ind,
          designDetails?.union_benefit_ind,
          {
            optionsMap: yesNoMap,
            isRequired: true,
          }
        ),
        defineFormField(
          'Healthcare Reform Status',
          'dropdownSelect',
          generalConfig.healthcare_reform_status,
          designDetails?.healthcare_reform_status,
          {
            optionsMap: healthcareReformStatusMap,
            isRequired: true,
            infoText:
              'Indicates whether or not the plan is subject to ACA requirements.',
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Overview Notes',
          'input',
          generalConfig.overview_notes,
          designDetails?.overview_notes,
          {
            placeholder: 'Overview Notes',
            validations: z
              .string()
              .max(2000, 'Overview Notes must be less than 2000 characters')
              .optional(),
          }
        ),
      ]),
    ]),
    defineSubCategory('If Healthcare Reform Status = Non-Grandfathered', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Non-Grandfathered Date',
          'datepicker',
          generalConfig.non_grandfathered_date,
          designDetails?.non_grandfathered_date,
          {
            isRequired: true,
            placeholder: 'Non-Grandfathered Date',
            validations: z.date(),
          }
        ),
      ]),
    ]),
    defineSubCategory('If Benefit Plan Type = HDHP, show', '', [
      defineFormField(
        'Qualified HDHP',
        'dropdownSelect',
        generalConfig.qualified_hdhp_ind,
        designDetails?.qualified_hdhp_ind,
        {
          optionsMap: yesNoMap,
          isRequired: true,
          infoText:
            'Does the plan meet current IRS guidelines to be a qualified HDHP?',
        }
      ),
    ]),
    defineSubCategory('ESI Only', '', [
      defineFormField(
        'ESI BPLID',
        'input',
        generalConfig.esi_bplid,
        designDetails?.esi_bplid,
        {
          isRequired: true,
          placeholder: 'ESI BPLID',
          validations: z.string(),
        }
      ),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Diabetic Meds and Supplies Copays',
          'dropdownSelect',
          generalConfig.diabetic_supplies_copay_setup_ind,
          designDetails?.diabetic_supplies_copay_setup_ind,
          {
            optionsMap: diabeticSuppliesCopayMap,
            isRequired: true,
          }
        ),
        defineFormField(
          '90 Day Prepackaged Co-pays',
          'dropdownSelect',
          generalConfig.pre_packaged_copays_ind,
          designDetails?.pre_packaged_copays_ind,
          {
            optionsMap: prepackagedCopaysMap,
            isRequired: true,
            infoText:
              'Copay amount for drugs dispensed in 90 Day, unbreakable packaging.',
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
