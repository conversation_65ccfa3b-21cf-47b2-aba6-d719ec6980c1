import { QuestionOutlineIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Divider,
  Flex,
  Heading,
  Text,
} from '@chakra-ui/react';
import { theme } from '@next/admin/constants';
import { FC } from 'react';

import { ValidationSection } from './ValidationSection';

export const HelpCenter: FC<{
  validationResults: any;
  helpContent: any;
  subtitle: string;
  description: string;
}> = ({ subtitle, description, validationResults, helpContent }) => {
  return (
    <Flex
      flexDirection="column"
      mx={'auto'}
      p={8}
      background={'white'}
      borderWidth={'1px'}
      borderRadius={'lg'}
      shadow={'md'}
    >
      <Flex mb={4} gap={2} color={theme.colors.brand['700']} align="center">
        <QuestionOutlineIcon boxSize={5} />
        <Text fontSize="2xl">Help Center</Text>
      </Flex>

      <ValidationSection validatedFields={validationResults} />

      <Divider />
      <Text fontSize="lg" fontWeight="bold" my={2} color="#69696A">
        {subtitle}
      </Text>

      <Box mb={5} fontSize="md" color="#69696A">
        {description}
      </Box>

      {helpContent.fields.map((field: any) => (
        <Accordion key={field.field_name} allowMultiple color="#69696A">
          <AccordionItem mb={8} boxShadow="md">
            <Flex bg="gray.50" alignItems="center">
              <AccordionButton flex="1">
                <Flex align="center" flex="1" textAlign="left">
                  <Heading fontSize={16} m={4}>
                    {field.field_name}
                  </Heading>
                </Flex>
                <AccordionIcon />
              </AccordionButton>
            </Flex>
            <AccordionPanel>{field.help_text}</AccordionPanel>
          </AccordionItem>
        </Accordion>
      ))}
    </Flex>
  );
};
