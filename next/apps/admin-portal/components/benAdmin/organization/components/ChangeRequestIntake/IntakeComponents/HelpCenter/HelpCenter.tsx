import { QuestionOutlineIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Divider,
  Flex,
  Heading,
  Text,
} from '@chakra-ui/react';
import { theme } from '@next/admin/constants';
import { ValidationResults } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { FC } from 'react';

import { ValidationSection } from './ValidationSection';

export const HelpCenter: FC<{
  validationResults?: ValidationResults;
  helpContent?: any;
}> = ({ validationResults, helpContent }) => {
  return (
    <Box
      flex="1"
      minW="320px"
      maxW="400px"
      position="sticky"
      top={1}
      alignSelf="flex-start"
      maxH="60vh"
      overflowY="scroll"
      sx={{
        '&::-webkit-scrollbar': {
          width: '8px',
        },
        '&::-webkit-scrollbar-track': {
          background: 'transparent',
        },
        '&::-webkit-scrollbar-thumb': {
          background: 'transparent',
          borderRadius: '4px',
        },
        '&:hover::-webkit-scrollbar-thumb': {
          background: 'gray.300',
        },
        '&:hover::-webkit-scrollbar-track': {
          background: 'gray.100',
        },
      }}
    >
      <Flex
        flexDirection="column"
        mx={'auto'}
        p={8}
        background={'white'}
        borderWidth={'1px'}
        borderRadius={'lg'}
        shadow={'md'}
      >
        <Flex mb={4} gap={2} color={theme.colors.brand['700']} align="center">
          <QuestionOutlineIcon boxSize={5} />
          <Text fontSize="2xl">Help Center</Text>
        </Flex>

        {validationResults && (
          <ValidationSection validatedFields={validationResults} />
        )}

        <Divider />
        {helpContent?.length > 0 ? (
          <>
            <Text fontSize="lg" fontWeight="bold" my={2} color="#69696A">
              {'Help Topics'}
            </Text>

            <Box mb={5} fontSize="md" color="#69696A">
              {'Topics will change as you navigate through each page.'}
            </Box>

            {helpContent.fields.map((field: any) => (
              <Accordion key={field.field_name} allowMultiple color="#69696A">
                <AccordionItem mb={8} boxShadow="md">
                  <Flex bg="gray.50" alignItems="center">
                    <AccordionButton flex="1">
                      <Flex align="center" flex="1" textAlign="left">
                        <Heading fontSize={16} m={4}>
                          {field.field_name}
                        </Heading>
                      </Flex>
                      <AccordionIcon />
                    </AccordionButton>
                  </Flex>
                  <AccordionPanel>{field.help_text}</AccordionPanel>
                </AccordionItem>
              </Accordion>
            ))}
          </>
        ) : (
          <>
            <Text fontSize="lg" fontWeight="bold" my={2} color="#69696A">
              No Help Topics Found
            </Text>

            <Box mb={5} fontSize="md" color="#69696A">
              There are no Help Topics available at this time. We&apos;ll be
              adding new topics over time, and making updates as we receive more
              information
            </Box>
          </>
        )}
      </Flex>
    </Box>
  );
};
