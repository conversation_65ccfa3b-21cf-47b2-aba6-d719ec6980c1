import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Flex,
  Heading,
  Icon,
  Text,
} from '@chakra-ui/react';
import {
  ValidationResults,
  ValidationRule,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PiWarningFill, PiWarningOctagonFill } from 'react-icons/pi';

const ValidatedFieldsAccordion = ({
  type,
  fields,
}: {
  type: string;
  fields: ValidationRule[];
}) => {
  return (
    <Accordion allowMultiple color="#69696A">
      <AccordionItem
        bg={type === 'Error' ? '#FEF0EF' : '#FFF8E8'}
        mb={8}
        boxShadow="md"
        borderTop="4px"
        borderTopColor={type === 'Error' ? '#DF5D53' : '#ED8936'}
        borderRadius="8px"
      >
        <Flex alignItems="center">
          <AccordionButton>
            <Flex align="center" flex="1" textAlign="left">
              {type === 'Error' ? (
                <Icon as={PiWarningOctagonFill} color="#DF5D53" boxSize={6} />
              ) : (
                <Icon as={PiWarningFill} color="#ED8936" boxSize={6} />
              )}

              <Heading fontSize={16} m={4}>
                {type === 'Error' ? 'Errors' : 'Warnings'} on Page
              </Heading>
            </Flex>
            <AccordionIcon />
          </AccordionButton>
        </Flex>
        <AccordionPanel color="#873832">
          {fields?.map((field: ValidationRule) => (
            <Flex
              direction="column"
              gap={4}
              key={field.plan_validation_rule_id}
            >
              <Heading size="sm">{field.rule_name}</Heading>
              <Text>{field.validation_message}</Text>
            </Flex>
          ))}
        </AccordionPanel>
      </AccordionItem>
    </Accordion>
  );
};

export const ValidationSection = ({
  validatedFields,
}: {
  validatedFields: ValidationResults;
}) => {
  const errors = validatedFields.errors;
  const warnings = validatedFields.warnings;
  return (
    <>
      {errors?.length && (
        <ValidatedFieldsAccordion type="Error" fields={errors} />
      )}

      {warnings?.length && (
        <ValidatedFieldsAccordion type="Warning" fields={warnings} />
      )}
    </>
  );
};
