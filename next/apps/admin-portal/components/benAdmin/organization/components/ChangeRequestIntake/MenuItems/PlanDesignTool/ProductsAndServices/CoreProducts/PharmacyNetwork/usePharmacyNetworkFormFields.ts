import {
  Feature,
  OrganizationDetails,
  Plan,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import {
  buildPicklistsMap,
  getOptionsMap,
  mapFieldType,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/Configurable/fieldUtils';
import {
  buildFieldPath,
  findMatchingPlanFeatureItem,
  findMatchingPlanFeatures,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import { usePBMType } from 'apps/admin-portal/components/benAdmin/organization/hooks/usePBMTypeHook';
import {
  defineDropdownField,
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { useURLIndex } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/useUrlParameters';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  getIdMap,
  syncAcrossPlanDesigns,
} from '../../../ClinicalDesigns/DynamicForm/paramsUtils';
import { getCategoryMap } from './utils';

type FieldType = 'dropdownSelect' | 'input' | 'checkboxGroup' | 'radioGroup';

/**
 * Hook to dynamically generate subCategories for the Pharmacy Network feature with custom grouping.
 * @param feature The Pharmacy Network feature object
 * @param currentDetails The current organization details
 * @param formMethods The react-hook-form methods for managing form state
 * @returns An object containing the generated subCategories
 */
export function usePharmacyNetworkFormFields(
  feature: Feature,
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<any>
) {
  const urlIndex = useURLIndex(undefined, { debug: false });
  const pbmType = usePBMType(formMethods);
  // Get categoryMap based on pbmType using the utility function
  const categoryMap = getCategoryMap(pbmType);

  // Memoize picklistsMap to avoid recomputation
  const picklistsMap = useMemo(
    () => buildPicklistsMap(currentDetails.picklists || []),
    [currentDetails.picklists]
  );

  // Memoize feature items map for faster lookups
  const featureItemsMap = useMemo(() => {
    const map = new Map<string, typeof feature.feature_items[0]>();
    feature.feature_items?.forEach((item) => {
      const key = item.picklist_name || item.name || '';
      map.set(key, item);
    });
    return map;
  }, [feature.feature_items]);

  // Use memoization to prevent regenerating fields unnecessarily
  const subCategories = useMemo(() => {
    // Early return if feature is invalid
    if (!feature || !feature.feature_items) {
      return [];
    }

    const matchingPlanFeatures = findMatchingPlanFeatures(
      currentDetails.plan as Plan,
      feature,
      urlIndex || 0
    );

    // Helper function to generate fields for a given set of feature items
    const generateFields = (items: typeof feature.feature_items) =>
      items.map((item) => {
        const matchingItem = findMatchingPlanFeatureItem(
          matchingPlanFeatures,
          item
        );

        // Determine field type based on field_type_label
        const fieldType = mapFieldType(item.field_type_label) as FieldType;

        // Get options map for selection fields
        const optionsMap = getOptionsMap(picklistsMap, item);

        // Build the field path with explicit URL index
        const fieldPath = buildFieldPath(matchingItem, urlIndex);

        // Get the current value from the plan structure
        const currentValue = matchingItem ? matchingItem.value : '';
        const idMap = getIdMap(fieldPath, formMethods);

        const fieldOptions: any = {
          infoText: '',
          placeholder: `Enter ${item.label}`,
          isRequired: false,
        };

        if (fieldType === 'dropdownSelect' && optionsMap) {
          fieldOptions.optionsMap = optionsMap;
          fieldOptions.placeholder = `Select ${item.label}`;
          fieldOptions.showToggle = true;
          fieldOptions.toggleLabel = 'Differs from Plan Design';
          fieldOptions.toggleName = `${fieldPath}_differs`;
          fieldOptions.toggleDefaultValue = false;
          fieldOptions.formPath = PLAN_DESIGNS_BASE_PATH;
          fieldOptions.sync = true;
          fieldOptions.idMap = idMap;
          fieldOptions.syncFunction = syncAcrossPlanDesigns;
          return defineDropdownField(
            item.label || '',
            fieldPath,
            currentValue,
            optionsMap,
            fieldOptions
          );
        }

        if (['checkboxGroup', 'radioGroup'].includes(fieldType) && optionsMap) {
          fieldOptions.optionsMap = optionsMap;
        }
        if (fieldType === 'input') {
          fieldOptions.showToggle = true;
          fieldOptions.toggleLabel = 'Differs from Plan Design';
          fieldOptions.toggleName = `${fieldPath}_differs`;
          fieldOptions.toggleDefaultValue = false;
          fieldOptions.formPath = PLAN_DESIGNS_BASE_PATH;
          fieldOptions.sync = true;
          fieldOptions.idMap = idMap;
          fieldOptions.syncFunction = syncAcrossPlanDesigns;
        }

        return defineFormField(
          item.label || '',
          fieldType,
          fieldPath,
          currentValue,
          fieldOptions
        );
      });

    // If categoryMap is non-empty (e.g., EsiCategoryMap, OptumCategoryMap, CareMarkCategoryMap), use categorized logic
    if (categoryMap.length > 0) {
      return categoryMap.map(({ title: categoryTitle, picklistFields }) => {
        // Filter fields by matching picklist_name or name against picklistFields
        const categoryFields = picklistFields
          .filter((field): field is string => field != null)
          .map((field) => featureItemsMap.get(field))
          .filter(
            (item): item is NonNullable<typeof feature.feature_items[0]> =>
              item !== undefined && item !== null
          );

        const fields = generateFields(categoryFields);
        const inlineGroups = [];
        for (let i = 0; i < fields.length; i += 2) {
          inlineGroups.push(defineInlineFieldGroup(fields.slice(i, i + 2)));
        }

        return defineSubCategory(categoryTitle, '', inlineGroups);
      });
    }

    // Fallback to non-categorized logic if categoryMap is empty (e.g., pbmType is undefined or invalid)
    const fields = generateFields(feature.feature_items);
    const inlineGroups = [];
    for (let i = 0; i < fields.length; i += 2) {
      inlineGroups.push(defineInlineFieldGroup(fields.slice(i, i + 2)));
    }

    return [
      defineSubCategory(feature.label || feature.name || '', '', inlineGroups),
    ];
  }, [
    feature,
    currentDetails.plan,
    urlIndex,
    picklistsMap,
    categoryMap,
    formMethods,
  ]);

  return { subCategories };
}
