import { Box, Text } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { formatDate } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/utils/tableUtils';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { useSearchParams } from 'next/navigation';
import React from 'react';

import { IntakeHeader } from './IntakeHeader';

interface MainContentProps {
  organizationDetails: OrganizationDetails;
  currentComponent: React.ReactNode;
  currentMode: string;
  sidebarConfig: SidebarConfig;
  activeItem: string;
  onBreadcrumbNavigate: (id: string) => void;
  shareCurrentValues?: OrganizationDetails;
}

const MainContent: React.FC<MainContentProps> = ({
  organizationDetails,
  currentComponent,
  currentMode,
  sidebarConfig,
  activeItem,
  onBreadcrumbNavigate,
  shareCurrentValues,
}) => {
  const searchParams = useSearchParams();

  //Below code is to get the index of the plan design from the url
  const indexParam = searchParams?.get('index');
  const indexToUse = indexParam ? parseInt(indexParam, 10) : undefined;

  let benefitPlanNameTitle = '',
    planEffectiveDate = '';

  //Initially the shareCureentValues from the form is undefined, hence use organizationDetials to get Benefit plan name and effective date
  if (
    shareCurrentValues === undefined &&
    organizationDetails?.plan?.plan_designs &&
    indexToUse !== undefined &&
    indexToUse >= 0 &&
    indexToUse < organizationDetails.plan.plan_designs.length
  ) {
    benefitPlanNameTitle =
      organizationDetails.plan.plan_designs[indexToUse].name;
    planEffectiveDate =
      organizationDetails.plan.plan_designs[indexToUse].plan_design_details[0]
        .effective_date;
  } else if (
    indexToUse !== undefined &&
    shareCurrentValues?.plan?.plan_designs &&
    indexToUse >= 0 &&
    indexToUse < shareCurrentValues.plan.plan_designs.length
  ) {
    benefitPlanNameTitle =
      shareCurrentValues.plan.plan_designs[indexToUse].name;
    planEffectiveDate =
      shareCurrentValues.plan.plan_designs[indexToUse].plan_design_details[0]
        .effective_date;
  }

  // Fix double-encoded title parameter
  const encodedTitle = searchParams?.get('title') || undefined;
  const title = encodedTitle
    ? decodeURIComponent(decodeURIComponent(encodedTitle))
    : undefined;

  // Generate breadcrumb paths based on current state
  const getBreadcrumbPaths = () => {
    const paths = [{ label: 'Project Overview', id: 'overview' }];

    // For main mode, just add Dashboard and return
    if (currentMode === 'main') {
      paths.unshift({ label: 'Dashboard', id: 'dashboard' });
      return paths;
    }

    const items = sidebarConfig?.sections?.[0]?.items || [];

    // Search for active item in both top-level and nested items
    for (const item of items) {
      // Case 1: Match at top level
      if (item.id === activeItem && item.component) {
        paths.push({ label: item.label, id: item.id });
        return paths;
      }

      // Case 2: Match in dropdown
      if (item.hasDropdown && item.dropdownItems) {
        const nestedItem = item.dropdownItems.find(
          (nested) => nested.id === activeItem && nested.component
        );

        if (nestedItem) {
          paths.push({ label: item.label, id: '' }); // Parent with empty id for non-clickable
          paths.push({ label: nestedItem.label, id: nestedItem.id }); // Child (clickable)
          return paths;
        }
      }
    }

    return paths;
  };

  return (
    <Box flex="1" display="flex" flexDirection="column" overflow="hidden">
      <IntakeHeader
        organizationDetails={organizationDetails}
        getBreadcrumbPaths={getBreadcrumbPaths}
        onBreadcrumbNavigate={onBreadcrumbNavigate}
        title={title}
      />

      {/* Displays Benefit Plan Name and effective date as Title on Edit plan design UI */}
      {benefitPlanNameTitle && (
        <Box w="98%" mx="auto" px={5}>
          <Text fontSize="2xl">
            {benefitPlanNameTitle}
            {planEffectiveDate ? ` - ${formatDate(planEffectiveDate)}` : ''}
          </Text>
        </Box>
      )}
      <Box flex="1" w="98%" mx="auto" overflowY="auto" p={4}>
        {currentComponent}
      </Box>
    </Box>
  );
};

export default MainContent;
