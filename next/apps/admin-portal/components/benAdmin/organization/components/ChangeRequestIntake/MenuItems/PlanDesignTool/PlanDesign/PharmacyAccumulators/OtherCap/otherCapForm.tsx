import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from '@chakra-ui/react';
import { AccumulationOther } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  planYearRenewalMap,
  yesNoMap,
} from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/FieldData/maps';
import { getOtherCapBasePath } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/otherPAConfig';
import {
  drugListMap,
  excludeDrugListMap,
} from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/FieldGroup/maps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import get from 'lodash/get';
import { FC, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { currencyValidation } from '../../../validations';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';

interface OtherCapFormProps {
  initialData?: Partial<AccumulationOther>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  isNewItem?: boolean;
}

export const OtherCapForm: FC<OtherCapFormProps> = ({
  initialData = {},
  onSave,
  onCancel,
  itemIndex = -1,
  isNewItem = true,
}) => {
  const {
    carryoverPhaseMap,
    cdhClassCodeMap,
    pharmacyChannelMap,
    networkStatusMap,
    sharedIndicatorMap,
    esiAccumPeriodMap,
    benefitPeriodLengthMap,
    drugTypeStatusMap,
    formularyStatusMap,
    otherAccumTypeMap,
  } = usePicklistMaps();

  const localFormMethods = useForm({
    defaultValues: useMemo(() => initialData, [initialData]),
  });

  useEffect(() => {
    localFormMethods.reset(initialData);
  }, [initialData, localFormMethods]);

  const basePath = getOtherCapBasePath(isNewItem, itemIndex);

  const formConfig = useMemo(() => {
    return {
      subCategories: [
        defineSubCategory('', '', [
          defineInlineFieldGroup([
            defineFormField(
              'Accums Tier Type',
              'dropdownSelect',
              `${basePath}.other_accum_type_ind`,
              initialData?.other_accum_type_ind,
              {
                isRequired: true,
                infoText: 'Accums Tier Type',
                optionsMap: otherAccumTypeMap,
              }
            ),
            defineFormField(
              'Accums Tier Name',
              'input',
              `${basePath}.accums_tier_name`,
              initialData?.accums_tier_name,
              {
                isRequired: true,
                infoText: 'Accums Tier Name',
                placeholder: 'Enter Accums Tier Name',
                validations: z
                  .string()
                  .max(
                    255,
                    'Accums Tier Name must be less than 255 characters'
                  ),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Accums Tier Effective Date',
              'datepicker',
              `${basePath}.effective_date`,
              initialData?.effective_date,
              {
                isRequired: true,
                infoText: 'Accum Tier Effective Date',
                validations: z.date(),
              }
            ),
            defineFormField(
              'Accums Tier End Date',
              'datepicker',
              `${basePath}.expiration_date`,
              initialData?.expiration_date,
              {
                infoText: 'Accum Tier End Date',
                validations: z.date().optional(),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Accums Tier PBC Order',
              'input',
              `${basePath}.pbc_order`,
              initialData?.pbc_order,
              {
                isRequired: true,
                infoText: 'Accums Tier PBC Order',
                placeholder: 'Enter Accums Tier PBC Order',
                validations: z.number().int(),
              }
            ),
            defineFormField(
              'Accumulation Period',
              'dropdownSelect',
              `${basePath}.accum_period_ind`,
              initialData?.accum_period_ind,
              {
                isRequired: true,
                infoText: 'Accumulation Period',
                optionsMap: esiAccumPeriodMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Specify Other Cap Accumulation Period',
              'dropdownSelect',
              `${basePath}.specify_accum_period_ind`,
              initialData?.specify_accum_period_ind,
              {
                isRequired: true,
                infoText: 'Specify Other Cap Accumulation Period',
                optionsMap: planYearRenewalMap,
              }
            ),
            defineFormField(
              'Benefit Period Length',
              'dropdownSelect',
              `${basePath}.benefit_period_length_ind`,
              initialData?.benefit_period_length_ind,
              {
                isRequired: true,
                infoText: 'Benefit Period Length',
                optionsMap: benefitPeriodLengthMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Benefit Period Length - Other',
              'input',
              `${basePath}.benefit_period_length_other`,
              initialData?.benefit_period_length_other,
              {
                isRequired: true,
                infoText: 'Benefit Period Length - Other',
                placeholder: 'Benefit Period Length - Other',
                validations: z.number().int().positive(),
              }
            ),
            defineFormField(
              'Do Priming Balances Apply?',
              'dropdownSelect',
              `${basePath}.priming_balances_ind`,
              initialData?.priming_balances_ind,
              {
                isRequired: true,
                infoText: 'Do Priming Balances Apply?',
                optionsMap: yesNoMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Carryover Phase',
              'dropdownSelect',
              `${basePath}.carryover_phase_ind`,
              initialData?.carryover_phase_ind,
              {
                isRequired: true,
                infoText: 'Carryover Phase',
                optionsMap: carryoverPhaseMap,
              }
            ),
            defineFormField(
              'Describe Carryover Phase',
              'input',
              `${basePath}.describe_carryover_phase`,
              initialData?.describe_carryover_phase,
              {
                infoText: 'Describe Carryover Phase',
                placeholder: 'Describe Carryover Phase',
                validations: z
                  .string()
                  .max(255, 'Value must be less than 255 characters')
                  .optional(),
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Individual Cap Amount',
              'input',
              `${basePath}.individual_plan_amount`,
              initialData?.individual_plan_amount,
              {
                isRequired: true,
                infoText: 'Individual Cap Amount',
                placeholder: 'Enter Individual Cap Amount',
                validations: currencyValidation,
              }
            ),
            defineFormField(
              'Family Cap Amount',
              'input',
              `${basePath}.family_plan_amount`,
              initialData?.family_plan_amount,
              {
                isRequired: true,
                infoText: 'Family Cap Amount',
                placeholder: 'Enter Family Cap Amount',
                validations: currencyValidation,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Employee +1 Cap Amount',
              'input',
              `${basePath}.employee_1_dep_amount`,
              initialData?.employee_1_dep_amount,
              {
                isRequired: true,
                infoText: 'Employee +1 Cap Amount',
                placeholder: 'Enter Employee +1 Cap Amount',
                validations: currencyValidation,
              }
            ),
            defineFormField(
              'Individual within Family Cap Amount',
              'input',
              `${basePath}.individual_within_family_amount`,
              initialData?.individual_within_family_amount,
              {
                isRequired: true,
                infoText: 'Individual within Family Cap Amount',
                placeholder: 'Enter Individual within Family Cap Amount',
                validations: currencyValidation,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Max Allowable Cap',
              'input',
              `${basePath}.max_allowable_cap`,
              initialData?.max_allowable_cap,
              {
                infoText: 'Max Allowable Cap',
                placeholder: 'Enter Max Allowable Cap',
                validations: currencyValidation.optional(),
              }
            ),
            defineFormField(
              'CDH Class Code',
              'dropdownSelect',
              `${basePath}.cdh_class_code_ind`,
              initialData?.cdh_class_code_ind,
              {
                isRequired: true,
                infoText: 'CDH Class Code',
                optionsMap: cdhClassCodeMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Shared Indicator',
              'dropdownSelect',
              `${basePath}.shared_ind`,
              initialData?.shared_ind,
              {
                isRequired: true,
                infoText: 'Shared Indicator',
                optionsMap: sharedIndicatorMap,
              }
            ),
            defineFormField(
              'Drug Type Status',
              'dropdownSelect',
              `${basePath}.drug_type_status_ind`,
              initialData?.drug_type_status_ind,
              {
                isRequired: true,
                infoText: 'Drug Type Status',
                optionsMap: drugTypeStatusMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Formulary Status',
              'dropdownSelect',
              `${basePath}.formulary_status_ind`,
              initialData?.formulary_status_ind,
              {
                isRequired: true,
                infoText: 'Formulary Status',
                optionsMap: formularyStatusMap,
              }
            ),
            defineFormField(
              'Network Status',
              'dropdownSelect',
              `${basePath}.network_status_ind`,
              initialData?.network_status_ind,
              {
                isRequired: true,
                infoText: 'Network Status',
                optionsMap: networkStatusMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Pharmacy Channel',
              'dropdownSelect',
              `${basePath}.pharmacy_channel_ind`,
              initialData?.pharmacy_channel_ind,
              {
                isRequired: true,
                infoText: 'Pharmacy Channel',
                optionsMap: pharmacyChannelMap,
              }
            ),
            defineFormField(
              'Include Drug List',
              'dropdownSelect',
              `${basePath}.include_drug_list_ind`,
              initialData?.include_drug_list_ind,
              {
                infoText: 'Include Drug List',
                optionsMap: drugListMap,
              }
            ),
          ]),
          defineInlineFieldGroup([
            defineFormField(
              'Exclude Drug List',
              'dropdownSelect',
              `${basePath}.exclude_drug_list_ind`,
              initialData?.exclude_drug_list_ind,
              {
                infoText: 'Exclude Drug List',
                optionsMap: excludeDrugListMap,
              }
            ),
            defineFormField(
              'Other Cap Notes',
              'input',
              `${basePath}.notes`,
              initialData?.notes,
              {
                infoText: 'Other Cap Notes',
                placeholder: 'Enter Other Cap Notes',
                validations: z
                  .string()
                  .max(2000, 'Value must be less than 2000 characters')
                  .optional(),
              }
            ),
          ]),
        ]),
      ],
    };
  }, [initialData, basePath]);

  const handleFormSubmit = (data: any) => {
    if (onSave) {
      const productData = get(data, isNewItem ? 'temp' : basePath, {});
      onSave(productData);
    }
  };

  return (
    <>
      <ModalBody>
        <GenericForm
          formMethods={localFormMethods}
          formName="Pharmacy Accumulators - Other Cap"
          formDescription="Here's a brief description placeholder of what this page will have the user work or make changes to. It should be able to provide context and information to the user to confidently answer information."
          subCategories={formConfig?.subCategories || []}
          showButtons={false} // Hide the built-in buttons
          isInModal
        />
      </ModalBody>
      <ModalFooter>
        <Button variant="outline" mr={3} onClick={onCancel}>
          Cancel
        </Button>
        <Button
          colorScheme="green"
          onClick={localFormMethods.handleSubmit(handleFormSubmit)}
        >
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
