import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getDeductiblePAPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/deductiblePAConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { currencyValidation, smallintValidation } from '../../../validations';

export function useDeductibleForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const {
    yesNoMap,
    esiAccumPeriodMap,
    benefitPeriodsMap,
    benefitPeriodLengthMap,
    carryoverPhaseMap,
    accumDrugListMap,
    drugTypeStatusMap,
    embeddedMap,
    formularyStatusMap,
    integratedMap,
    networkApplicabilityMap,
    yesNoViewBenefitMap,
    pharmacyChannelMap,
    sharedIndicatorMap,
  } = usePicklistMaps();

  const deductibleConfig = getDeductiblePAPaths(indexToUse);
  const accumulationDeductible =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0]
      ?.accumulation_deductible?.[0];

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Does Deductible Apply?',
          'dropdownSelect',
          deductibleConfig.apply_ind,
          accumulationDeductible?.apply_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Accums Tier Name',
          'input',
          deductibleConfig.accums_tier_name,
          accumulationDeductible?.accums_tier_name,
          {
            placeholder: 'Enter Accums Tier Name',
            validations: z
              .string()
              .max(255, 'Accums Tier Name must be less than 255 characters')
              .optional(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Accums Tier Effective Date',
          'datepicker',
          deductibleConfig.effective_date,
          accumulationDeductible?.effective_date,
          {
            isRequired: true,
            validations: z.date({
              required_error: 'Effective Date is required',
              invalid_type_error: 'Effective Date must be a valid date',
            }),
          }
        ),
        defineFormField(
          'Accums Tier End Date',
          'datepicker',
          deductibleConfig.expiration_date,
          accumulationDeductible?.expiration_date,
          {
            validations: z
              .date({
                invalid_type_error: 'End Date must be a valid date',
              })
              .optional(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Accums Tier PBC Order',
          'input',
          deductibleConfig.pbc_order,
          accumulationDeductible?.pbc_order,
          {
            isRequired: true,
            placeholder: 'Enter Accums Tier PBC Order',
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Deductible Accumulation Period',
          'dropdownSelect',
          deductibleConfig.accum_period_ind,
          accumulationDeductible?.accum_period_ind,
          {
            isRequired: true,
            infoText: 'Deductible Accumulation Period',
            optionsMap: esiAccumPeriodMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Specify Deductible Accumulation Period',
          'dropdownSelect',
          deductibleConfig.specify_accum_period_ind,
          accumulationDeductible?.specify_accum_period_ind,
          {
            isRequired: true,
            infoText: 'Specify Deductible Accumulation Period',
            optionsMap: benefitPeriodsMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Benefit Period Length',
          'dropdownSelect',
          deductibleConfig.benefit_period_length_ind,
          accumulationDeductible?.benefit_period_length_ind,
          {
            isRequired: true,
            infoText:
              "Whether the accumulator resets every year, lasts for the patient's lifetime, or other (specify).",
            optionsMap: benefitPeriodLengthMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Period Length - Other',
          'input',
          deductibleConfig.benefit_period_length_other,
          accumulationDeductible?.benefit_period_length_other,
          {
            isRequired: true,
            placeholder: 'Enter Benefit Period Length - Other',
            infoText:
              'Used to specify the Benefit Period, if not calendar or lifetime.',
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Do Priming Balances Apply?',
          'dropdownSelect',
          deductibleConfig.priming_balances_ind,
          accumulationDeductible?.priming_balances_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            infoText:
              'Used for mid plan-year changes to credit existing member deductible spend to the new plan.',
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Carryover Phase',
          'dropdownSelect',
          deductibleConfig.carryover_phase_ind,
          accumulationDeductible?.carryover_phase_ind,
          {
            isRequired: true,
            infoText:
              'Denotes whether any part of the benefit carries over from one benefit/accum year to the next.',
            optionsMap: carryoverPhaseMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Describe Carryover Phase',
          'input',
          deductibleConfig.describe_carryover_phase,
          accumulationDeductible?.describe_carryover_phase,
          {
            infoText: 'Provides additional details for the carryover phase.',
            placeholder: 'Describe Carryover Phase',
            validations: z
              .string()
              .max(
                255,
                'Describe Carryover Phase must be less than 255 characters'
              )
              .optional(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Deductible Integrated?',
          'dropdownSelect',
          deductibleConfig.integrated_ind,
          accumulationDeductible?.integrated_ind,
          {
            isRequired: true,
            optionsMap: integratedMap,
            infoText:
              'Whether deductible is Rx-only (separate) or combined with medical (integrated).',
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Deductible Embedded?',
          'dropdownSelect',
          deductibleConfig.embedded_ind,
          accumulationDeductible?.embedded_ind,
          {
            isRequired: true,
            optionsMap: embeddedMap,
            infoText:
              'Embedded Accums = Each member must meet the individual deductible.',
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Individual Plan Deductible Amount',
          'input',
          deductibleConfig.individual_plan_amount,
          accumulationDeductible?.individual_plan_amount,
          {
            isRequired: true,
            placeholder: 'Enter Individual Plan Deductible Amount',
            validations: currencyValidation,
          }
        ),
        defineFormField(
          'Family Plan Deductible Amount',
          'input',
          deductibleConfig.family_plan_amount,
          accumulationDeductible?.family_plan_amount,
          {
            isRequired: true,
            placeholder: 'Enter Family Plan Deductible Amount',
            validations: currencyValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Employee +1 Dependent Deductible Amount',
          'input',
          deductibleConfig.employee_1_dep_amount,
          accumulationDeductible?.employee_1_dep_amount,
          {
            isRequired: true,
            placeholder: 'Enter Employee +1 Dependent Deductible Amount',
            validations: currencyValidation,
          }
        ),
        defineFormField(
          'Individual Deductible within Family Amount',
          'input',
          deductibleConfig.individual_within_family_amount,
          accumulationDeductible?.individual_within_family_amount,
          {
            isRequired: true,
            infoText:
              'For non-embedded plans. Some plans only allow the individual to hit up to a certain amount of the family deductible.',
            placeholder: 'Enter Individual Deductible within Family Amount',
            validations: currencyValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Does Deductible Apply to Maximum Out of Pocket?',
          'dropdownSelect',
          deductibleConfig.apply_to_moop_ind,
          accumulationDeductible?.apply_to_moop_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
        defineFormField(
          'Deductible Applies to Retail, Mail & Paper',
          'dropdownSelect',
          deductibleConfig.apply_retail_mail_paper_ind,
          accumulationDeductible?.apply_retail_mail_paper_ind,
          {
            isRequired: true,
            infoText:
              'Any claim from retail, mail or submitted via paper applies to deductible.',
            optionsMap: yesNoMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Penalties Apply to Deductible',
          'dropdownSelect',
          deductibleConfig.penalties_apply_ind,
          accumulationDeductible?.penalties_apply_ind,
          {
            isRequired: true,
            infoText: 'Penalties Apply to Deductible',
            optionsMap: yesNoViewBenefitMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Copay Apply During Deductible Phase',
          'dropdownSelect',
          deductibleConfig.copay_apply_during_deductible_phase_ind,
          accumulationDeductible?.copay_apply_during_deductible_phase_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Does Deductible Apply to Brand Medications Only?',
          'dropdownSelect',
          deductibleConfig.apply_brand_only_ind,
          accumulationDeductible?.apply_brand_only_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Does Deductible Apply to Specialty Medications Only?',
          'dropdownSelect',
          deductibleConfig.apply_specialty_only_ind,
          accumulationDeductible?.apply_specialty_only_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Shared Indicator',
          'dropdownSelect',
          deductibleConfig.shared_ind,
          accumulationDeductible?.shared_ind,
          {
            isRequired: true,
            infoText: 'Shared Indicator',
            optionsMap: sharedIndicatorMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Drug Type Status',
          'dropdownSelect',
          deductibleConfig.drug_type_status_ind,
          accumulationDeductible?.drug_type_status_ind,
          {
            isRequired: true,
            optionsMap: drugTypeStatusMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Formulary Status',
          'dropdownSelect',
          deductibleConfig.formulary_status_ind,
          accumulationDeductible?.formulary_status_ind,
          {
            isRequired: true,
            optionsMap: formularyStatusMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Network Status',
          'dropdownSelect',
          deductibleConfig.network_status_ind,
          accumulationDeductible?.network_status_ind,
          {
            isRequired: true,
            infoText: 'Network Status',
            optionsMap: networkApplicabilityMap,
            validations: smallintValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Pharmacy Channel',
          'dropdownSelect',
          deductibleConfig.pharmacy_channel_ind,
          accumulationDeductible?.pharmacy_channel_ind,
          {
            isRequired: true,
            infoText: 'Pharmacy Channel',
            optionsMap: pharmacyChannelMap,
            validations: smallintValidation,
          }
        ),
        defineFormField(
          'Include Drug List',
          'dropdownSelect',
          deductibleConfig.include_drug_list_ind,
          accumulationDeductible?.include_drug_list_ind,
          {
            placeholder: 'Include Drug List',
            optionsMap: accumDrugListMap,
            allowSearch: true,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Exclude Drug List',
          'dropdownSelect',
          deductibleConfig.exclude_drug_list_ind,
          accumulationDeductible?.exclude_drug_list_ind,
          {
            placeholder: 'Exclude Drug List',
            optionsMap: accumDrugListMap,
            allowSearch: true,
          }
        ),
        defineFormField(
          'Deductible Notes',
          'input',
          deductibleConfig.notes,
          accumulationDeductible?.notes,
          {
            placeholder: 'Enter Deductible Notes',
            validations: z
              .string()
              .max(2000, 'Deductible Notes must be less than 2000 characters')
              .optional(),
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
