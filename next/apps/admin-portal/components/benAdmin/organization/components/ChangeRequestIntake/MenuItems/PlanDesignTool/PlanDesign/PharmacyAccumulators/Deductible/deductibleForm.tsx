import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from '@chakra-ui/react';
import { AccumulationDeductible } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getDedectibleBasePath } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/deductiblePAConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { FC, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { productNames } from '../../../productNameConstants';
import { currencyValidation, smallintValidation } from '../../../validations';

interface DeductibleFormModalProps {
  initialData?: Partial<AccumulationDeductible>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  isNewItem?: boolean;
  productName: string;
}

export const DeductibleFormModal: FC<DeductibleFormModalProps> = ({
  initialData = {},
  onSave,
  onCancel,
  itemIndex = -1,
  isNewItem = true,
  productName,
}) => {
  const {
    yesNoMap,
    esiAccumPeriodMap,
    benefitPeriodsMap,
    benefitPeriodLengthMap,
    carryoverPhaseMap,
    accumDrugListMap,
    drugTypeStatusMap,
    formularyStatusMap,
    integratedMap,
    networkApplicabilityMap,
    pharmacyChannelMap,
    sharedIndicatorMap,
    embeddedMap,
    yesNoViewBenefitMap,
  } = usePicklistMaps();

  const basePath = getDedectibleBasePath(isNewItem, itemIndex);

  const formMethods = useForm({
    defaultValues: { [basePath]: initialData },
    shouldUnregister: false,
  });

  const currentData = formMethods.watch(basePath) || initialData;

  const [doesDeductibleApply, setDoesDeductibleApply] = useState<number>(
    Number(initialData?.apply_ind) || 0
  );

  useEffect(() => {
    formMethods.setValue(basePath, initialData);
    setDoesDeductibleApply(Number(initialData?.apply_ind) || 0);
  }, [basePath, formMethods, initialData]);

  useEffect(() => {
    const subscription = formMethods.watch((_, { name }) => {
      if (name === `${basePath}.apply_ind`) {
        const latestValue = formMethods.getValues(`${basePath}.apply_ind`);
        setDoesDeductibleApply(Number(latestValue));
      }
    });
    return () => subscription.unsubscribe();
  }, [formMethods, basePath]);

  const handleFormSubmit = () => {
    if (!onSave) return;
    const submitted = formMethods.getValues(basePath);
    const finalData = isNewItem
      ? { ...initialData, ...submitted }
      : { ...currentData, ...submitted };
    onSave(finalData);
  };

  const formConfig = useMemo(() => {
    return {
      subCategories: [
        defineSubCategory('', '', [
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
            ? [
                defineInlineFieldGroup([
                  defineFormField(
                    'Does Deductible Apply?',
                    'dropdownSelect',
                    `${basePath}.apply_ind`,
                    currentData?.apply_ind,
                    {
                      isRequired: true,
                      optionsMap: yesNoMap,
                      validations: smallintValidation,
                    }
                  ),
                  ...(doesDeductibleApply === 1
                    ? [
                        defineFormField(
                          'Accums Tier Name',
                          'input',
                          `${basePath}.accums_tier_name`,
                          currentData?.accums_tier_name,
                          {
                            placeholder: 'Enter Accums Tier Name',
                            validations: z
                              .string()
                              .max(
                                255,
                                'Accums Tier Name must be less than 255 characters'
                              )
                              .optional(),
                          }
                        ),
                      ]
                    : []),
                ]),
                ...(doesDeductibleApply === 1
                  ? [
                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.ESI_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Accums Tier Effective Date',
                                'datepicker',
                                `${basePath}.effective_date`,
                                currentData?.effective_date,
                                {
                                  validations: z.date({
                                    invalid_type_error:
                                      'Effective Date must be a valid date',
                                  }),
                                }
                              ),
                              defineFormField(
                                'Accums Tier End Date',
                                'datepicker',
                                `${basePath}.expiration_date`,
                                currentData?.expiration_date,
                                {
                                  validations: z
                                    .date({
                                      invalid_type_error:
                                        'End Date must be a valid date',
                                    })
                                    .optional(),
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Accums Tier PBC Order',
                                'input',
                                `${basePath}.pbc_order`,
                                currentData?.pbc_order,
                                {
                                  placeholder: 'Enter Accums Tier PBC Order',
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Deductible Accumulation Period',
                                'dropdownSelect',
                                `${basePath}.accum_period_ind`,
                                currentData?.accum_period_ind,
                                {
                                  infoText: 'Deductible Accumulation Period',
                                  optionsMap: esiAccumPeriodMap,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      defineInlineFieldGroup([
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Specify Deductible Accumulation Period',
                                'dropdownSelect',
                                `${basePath}.specify_accum_period_ind`,
                                currentData?.specify_accum_period_ind,
                                {
                                  infoText:
                                    'Specify Deductible Accumulation Period',
                                  optionsMap: benefitPeriodsMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                        defineInlineFieldGroup([
                          ...(productName === productNames.ESI_360
                            ? [
                                defineFormField(
                                  'Benefit Period Length',
                                  'dropdownSelect',
                                  `${basePath}.benefit_period_length_ind`,
                                  currentData?.benefit_period_length_ind,
                                  {
                                    infoText:
                                      "Whether the accumulator resets every year, lasts for the patient's lifetime, or other (specify).",
                                    optionsMap: benefitPeriodLengthMap,
                                    validations: smallintValidation,
                                  }
                                ),
                              ]
                            : []),
                        ]),
                      ]),
                      defineInlineFieldGroup([
                        ...(productName === productNames.ESI_360
                          ? [
                              defineFormField(
                                'Benefit Period Length - Other',
                                'input',
                                `${basePath}.benefit_period_length_other`,
                                currentData?.benefit_period_length_other,
                                {
                                  placeholder:
                                    'Enter Benefit Period Length - Other',
                                  infoText:
                                    'Used to specify the Benefit Period, if not calendar or lifetime.',
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Do Priming Balances Apply?',
                                'dropdownSelect',
                                `${basePath}.priming_balances_ind`,
                                currentData?.priming_balances_ind,
                                {
                                  optionsMap: yesNoMap,
                                  infoText:
                                    'Used for mid plan-year changes to credit existing member deductible spend to the new plan.',
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),
                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.ESI_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Carryover Phase',
                                'dropdownSelect',
                                `${basePath}.carryover_phase_ind`,
                                currentData?.carryover_phase_ind,
                                {
                                  infoText:
                                    'Denotes whether any part of the benefit carries over from one benefit/accum year to the next.',
                                  optionsMap: carryoverPhaseMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Describe Carryover Phase',
                                'input',
                                `${basePath}.describe_carryover_phase`,
                                currentData?.describe_carryover_phase,
                                {
                                  infoText:
                                    'Provides additional details for the carryover phase.',
                                  placeholder: 'Describe Carryover Phase',
                                  validations: z
                                    .string()
                                    .max(
                                      255,
                                      'Describe Carryover Phase must be less than 255 characters'
                                    )
                                    .optional(),
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Maximum Out of Pocket Integrated?',
                                'dropdownSelect',
                                `${basePath}.integrated_ind`,
                                currentData?.integrated_ind,
                                {
                                  infoText:
                                    'Whether MOOP is Rx-only (separate) or combined with medical (integrated).',
                                  optionsMap: integratedMap,
                                }
                              ),
                              defineFormField(
                                'Maximum Out of Pocket Embedded?',
                                'dropdownSelect',
                                `${basePath}.embedded_ind`,
                                currentData?.embedded_ind,
                                {
                                  infoText:
                                    'Embedded Accums = Each member must meet the individual MOOP.',
                                  optionsMap: embeddedMap,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Individual Plan Deductible Amount',
                                'input',
                                `${basePath}.individual_plan_amount`,
                                currentData?.individual_plan_amount,
                                {
                                  placeholder:
                                    'Enter Individual Plan Deductible Amount',
                                  validations: currencyValidation,
                                }
                              ),
                              defineFormField(
                                'Family Plan Deductible Amount',
                                'input',
                                `${basePath}.family_plan_amount`,
                                currentData?.family_plan_amount,
                                {
                                  placeholder:
                                    'Enter Family Plan Deductible Amount',
                                  validations: currencyValidation,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Employee +1 Dependent Deductible Amount',
                                'input',
                                `${basePath}.employee_1_dep_amount`,
                                currentData?.employee_1_dep_amount,
                                {
                                  placeholder:
                                    'Enter Employee +1 Dependent Deductible Amount',
                                  validations: currencyValidation,
                                }
                              ),
                              defineFormField(
                                'Individual within Family',
                                'input',
                                `${basePath}.individual_within_family_amount`,
                                currentData?.individual_within_family_amount,
                                {
                                  infoText:
                                    'For non-embedded plans. Some plans only allow the individual to hit up to a certain amount of the family deductible.',
                                  placeholder:
                                    'Enter Individual Deductible within Family Amount',
                                  validations: currencyValidation,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      defineInlineFieldGroup([
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Does Deductible Apply to Maximum Out of Pocket?',
                                'dropdownSelect',
                                `${basePath}.apply_to_moop_ind`,
                                currentData?.apply_to_moop_ind,
                                {
                                  optionsMap: yesNoMap,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Deductible Applies to Retail, Mail & Paper',
                                'dropdownSelect',
                                `${basePath}.apply_retail_mail_paper_ind`,
                                currentData?.apply_retail_mail_paper_ind,
                                {
                                  infoText:
                                    'Any claim from retail, mail or submitted via paper applies to deductible.',
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),

                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.ESI_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Penalties Apply to Deductible',
                                'dropdownSelect',
                                `${basePath}.penalties_apply_ind`,
                                currentData?.penalties_apply_ind,
                                {
                                  infoText: 'Penalties Apply to Deductible',
                                  optionsMap: yesNoViewBenefitMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Copay Apply During Deductible Phase',
                                'dropdownSelect',
                                `${basePath}.copay_apply_during_deductible_phase_ind`,
                                currentData?.copay_apply_during_deductible_phase_ind,
                                {
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),

                            defineInlineFieldGroup([
                              defineFormField(
                                'Does Deductible Apply to Brand Medications Only?',
                                'dropdownSelect',
                                `${basePath}.apply_brand_only_ind`,
                                currentData?.apply_brand_only_ind,
                                {
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Does Deductible Apply to Specialty Medications Only?',
                                'dropdownSelect',
                                `${basePath}.apply_specialty_only_ind`,
                                currentData?.apply_specialty_only_ind,
                                {
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      ...(productName === productNames.ESI_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Shared Indicator',
                                'dropdownSelect',
                                `${basePath}.shared_ind`,
                                currentData?.shared_ind,
                                {
                                  infoText: 'Shared Indicator',
                                  optionsMap: sharedIndicatorMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Drug Type Status',
                                'dropdownSelect',
                                `${basePath}.drug_type_status_ind`,
                                currentData?.drug_type_status_ind,
                                {
                                  optionsMap: drugTypeStatusMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      defineInlineFieldGroup([
                        ...(productName === productNames.ESI_360
                          ? [
                              defineFormField(
                                'Formulary Status',
                                'dropdownSelect',
                                `${basePath}.formulary_status_ind`,
                                currentData?.formulary_status_ind,
                                {
                                  optionsMap: formularyStatusMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Network Status',
                                'dropdownSelect',
                                `${basePath}.network_status_ind`,
                                currentData?.network_status_ind,
                                {
                                  infoText: 'Network Status',
                                  optionsMap: networkApplicabilityMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),
                      ...(productName === productNames.ESI_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Pharmacy Channel',
                                'dropdownSelect',
                                `${basePath}.pharmacy_channel_ind`,
                                currentData?.pharmacy_channel_ind,
                                {
                                  infoText: 'Pharmacy Channel',
                                  optionsMap: pharmacyChannelMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Include Drug List',
                                'dropdownSelect',
                                `${basePath}.include_drug_list_ind`,
                                currentData?.include_drug_list_ind,
                                {
                                  placeholder: 'Include Drug List',
                                  optionsMap: accumDrugListMap,
                                  allowSearch: true,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Exclude Drug List',
                                'dropdownSelect',
                                `${basePath}.exclude_drug_list_ind`,
                                currentData?.exclude_drug_list_ind,
                                {
                                  placeholder: 'Exclude Drug List',
                                  optionsMap: accumDrugListMap,
                                  allowSearch: true,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Notes',
                                'textarea',
                                `${basePath}.notes`,
                                currentData?.notes,
                                {
                                  placeholder: 'Enter Deductible Notes',
                                  validations: z
                                    .string()
                                    .max(
                                      2000,
                                      'Deductible Notes must be less than 2000 characters'
                                    )
                                    .optional(),
                                  rows: 5,
                                  customProps: {
                                    minHeight: '120px',
                                    overflow: 'hidden',
                                  },
                                }
                              ),
                            ]),
                          ]
                        : []),
                    ]
                  : []),
              ]
            : []),
        ]),
      ],
    };
  }, [basePath, currentData, productName, doesDeductibleApply]);

  return (
    <>
      <ModalBody>
        <GenericForm
          formMethods={formMethods}
          formName="Deductible"
          formDescription="Here's a brief description placeholder of what this page will have the user work or make changes to. It should be able to provide context and information to the user to confidently answer information."
          subCategories={formConfig?.subCategories || []}
          showButtons={false}
          isInModal
        />
      </ModalBody>
      <ModalFooter>
        <Button variant="outline" mr={3} onClick={onCancel}>
          Cancel
        </Button>
        <Button
          colorScheme="green"
          onClick={formMethods.handleSubmit(handleFormSubmit)}
        >
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
