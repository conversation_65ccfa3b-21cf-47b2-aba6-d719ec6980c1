import {
  ChangeRequest,
  CostShareDesign,
  PLAN_DESIGNS_BASE_PATH,
} from 'apps/admin-portal/components/benAdmin';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import isEqual from 'lodash/isEqual';
import omit from 'lodash/omit';
import { useCallback, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { COST_SHARE_TYPES, DEFAULT_COST_SHARE_VALUES } from '../constants';
import { TableRowData } from '../types';

/**
 * Custom hook providing helper functions for cost share component
 */
export const useCostShareHelpers = (
  formMethods: UseFormReturn<any>,
  basePath: string,
  isUnbreakable: boolean
) => {
  /**
   * Format title based on whether it's for unbreakable cost share
   */
  const formatTitle = useCallback(
    (baseTitle: string) =>
      isUnbreakable ? `${baseTitle} (Unbreakable)` : baseTitle,
    [isUnbreakable]
  );

  /**
   * Get the index from URL or default to 0
   */
  const urlIndex = useMemo(() => {
    try {
      const index = getIndexFromURL();
      return typeof index === 'number' ? index : 0;
    } catch {
      return 0;
    }
  }, []);

  /**
   * Determine the form path based on basePath or URL index
   */
  const getFormPath = useCallback(
    (providedPath?: string) =>
      providedPath ||
      `${PLAN_DESIGNS_BASE_PATH}.${urlIndex}.plan_design_details.[0].cost_share_design`,
    [urlIndex]
  );

  /**
   * Create a filter function for cost share items
   */
  const createFilterItems = useCallback(
    (unbreakable: boolean) => (item: CostShareDesign) => {
      if (!item || typeof item.pre_packaged_ind !== 'number') {
        return false;
      }
      return unbreakable
        ? item.pre_packaged_ind === COST_SHARE_TYPES.UNBREAKABLE
        : item.pre_packaged_ind === COST_SHARE_TYPES.STANDARD;
    },
    []
  );

  /**
   * Create a new cost share item
   */
  const createNewItem = useCallback(
    (tier_ind: string, unbreakable: boolean): CostShareDesign => {
      const effectiveDate = formMethods.getValues('plan.effective_date');

      const changeRequest: ChangeRequest | null = JSON.parse(
        sessionStorage.getItem('selectedChangeRequest') || 'null'
      );

      const changeRequestEffectiveDate =
        changeRequest?.target_effective_date || effectiveDate;
      const pre_packaged_ind = unbreakable
        ? COST_SHARE_TYPES.UNBREAKABLE
        : COST_SHARE_TYPES.STANDARD;

      const newCostShare = {
        ...DEFAULT_COST_SHARE_VALUES,
        tier_ind,
        pre_packaged_ind,
        effective_date: changeRequestEffectiveDate,
        cost_share_tiers: [],
      } as CostShareDesign;

      console.log(newCostShare);

      return newCostShare;
    },
    []
  );

  /**
   * Update form state with new items
   */
  const updateFormState = useCallback(
    (
      items: CostShareDesign[],
      emptyState = false,
      path: string,
      setModalKey?: (fn: (prev: number) => number) => void
    ) => {
      const currentFormValues = formMethods.getValues();
      formMethods.reset(
        {
          ...currentFormValues,
          emptyState,
          [path]: items,
        },
        {
          keepDirty: true,
          keepTouched: true,
        }
      );
      if (setModalKey) {
        setModalKey((prev) => prev + 1);
      }
    },
    [formMethods]
  );

  /**
   * Find items matching a clicked item
   */
  const findMatchingItems = useCallback(
    (clickedObject: CostShareDesign, path: string) => {
      const allItems = formMethods.getValues(path) || [];

      const originalIndex = allItems.findIndex(
        (item: CostShareDesign) =>
          item === clickedObject ||
          (item.tier_ind === clickedObject.tier_ind &&
            item.pre_packaged_ind === clickedObject.pre_packaged_ind)
      );

      if (originalIndex === -1) return [];

      const cleanClickedItem = omit(clickedObject, ['  pre_packaged_ind']);

      const matchingIndexes = allItems.reduce(
        (acc: number[], item: CostShareDesign, idx: number) => {
          if (idx === originalIndex) return acc;

          const cleanCurrentItem = omit(item, ['  pre_packaged_ind']);
          if (isEqual(cleanClickedItem, cleanCurrentItem)) {
            acc.push(idx);
          }
          return acc;
        },
        [] as number[]
      );

      return [originalIndex, ...matchingIndexes];
    },
    [formMethods]
  );

  /**
   * Get a filter function based on the current tab
   */
  const getCurrentTabFilter = useCallback(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');

    if (tab === 'standard') {
      return (row: TableRowData) =>
        row.pre_packaged_ind === COST_SHARE_TYPES.STANDARD;
    } else if (tab === 'unbreakable') {
      return (row: TableRowData) =>
        row.pre_packaged_ind === COST_SHARE_TYPES.UNBREAKABLE;
    }
    return () => true;
  }, []);

  return {
    formatTitle,
    urlIndex,
    getFormPath,
    createFilterItems,
    createNewItem,
    updateFormState,
    findMatchingItems,
    getCurrentTabFilter,
  };
};
