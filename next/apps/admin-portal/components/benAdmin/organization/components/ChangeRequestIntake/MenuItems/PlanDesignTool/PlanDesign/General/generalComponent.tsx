import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/context/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  GENERAL_ITEM,
  PLAN_DESIGN_LIST_ITEM,
  XML_ITEM,
} from '../../../../Navigation/navigationConstants';
import { useGeneralForm } from './generalForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const GeneralComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch, getValues } = formMethods;
  const currentDetails = watch();

  /**
   * VALIDATION IMPLEMENTATION GUIDE FOR DEVELOPERS:
   *
   * 1. GET VALIDATION DATA FOR YOUR PAGE:
   *    Use `useValidateByPageFromContext(YOUR_PAGE_CONSTANT)` where YOUR_PAGE_CONSTANT
   *    is the navigation constant for your specific page (e.g., GENERAL_ITEM, XML_ITEM, etc.)
   *
   *    This hook returns:
   *    - refetch: Function to manually refresh validation data
   *    - validationData: Current validation state for your page (errors, status, etc.)
   *    - isLoading: Boolean indicating if validation is currently being fetched
   *
   * 2. USING VALIDATION DATA:
   *    The validationData contains:
   *    - errors: Array of validation errors for your page
   *    - status: Validation status
   *    - validation_results: Detailed validation information
   *
   *    Use this data to:
   *    - Show/hide error messages
   *    - Enable/disable navigation buttons
   *    - Display validation status indicators
   */
  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(GENERAL_ITEM);

  console.log('Current validation data for GENERAL_ITEM:', validationData);

  const baseSaveHandler = useSaveChangeRequestHandler(formMethods);
  const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  const handleSaveAndExit = useCallback(() => {
    const currentValues = getValues();
    submitHandler(currentValues);
  }, [getValues, submitHandler]);

  const { subCategories } = useGeneralForm(
    currentDetails as Partial<OrganizationDetails>
  );

  /**
   * CONTINUE HANDLER IMPLEMENTATION GUIDE:
   *
   * 1. SETUP THE CONTINUE HANDLER HOOK:
   *    Use `useContinueHandler` and pass:
   *    - refetch: The refetch function from useValidateByPageFromContext
   *    - onUpdateActiveItem: Navigation function to change pages
   *    - formMethods: React Hook Form methods for saving data
   *
   * 2. CREATE PAGE-SPECIFIC CONTINUE HANDLER:
   *    Call `createContinueHandler(CURRENT_PAGE, NEXT_PAGE)` where:
   *    - CURRENT_PAGE: Navigation constant for the page you're on (e.g., GENERAL_ITEM)
   *    - NEXT_PAGE: Navigation constant for where to navigate next (e.g., XML_ITEM)
   *
   * 3. WHAT HAPPENS ON CONTINUE:
   *    When user clicks continue, the handler:
   *    a) Saves current form data using auto-save
   *    b) Calls refetch() to get fresh validation data from API
   *    c) Waits 100ms for state to update
   *    d) Uses the page constant to get the correct UI context index
   *    e) Checks for validation errors in the refreshed data
   *    f) If no errors, navigates to the next page
   *    g) If errors exist, stays on current page (user can see validation issues)
   *
   * 4. VALIDATION DATA UPDATES:
   *    - During refetch, isLoading becomes true
   *    - After refetch completes, validationData is updated with fresh API data
   *    - isLoading becomes false
   *    - Your component re-renders with new validation state
   *
   * EXAMPLE FOR OTHER PAGES:
   * ```typescript
   * // For XML page navigating to Standard page:
   * const { refetch, validationData, isLoading } = useValidateByPageFromContext(XML_ITEM);
   * const { createContinueHandler } = useContinueHandler({ refetch, onUpdateActiveItem, formMethods });
   * const handleContinue = createContinueHandler(XML_ITEM, STANDARD_ITEM);
   * ```
   */
  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(GENERAL_ITEM, XML_ITEM);

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(PLAN_DESIGN_LIST_ITEM);
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="General"
      formDescription=""
      subCategories={subCategories}
      onContinue={handleContinue}
      onBack={handleBack}
      onSaveExit={handleSaveAndExit}
      isProcessing={isLoading}
    />
  );
};

export default GeneralComponent;
