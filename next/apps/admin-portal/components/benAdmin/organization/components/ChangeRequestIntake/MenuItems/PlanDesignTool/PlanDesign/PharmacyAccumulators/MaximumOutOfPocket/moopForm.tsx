import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  planYearRenewalMap,
  yesNoMap,
} from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/FieldData/maps';
import { getMoopPAPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/moopPAConfig';
import {
  drugListMap,
  excludeDrugListMap,
} from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/FieldGroup/maps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { currencyValidation } from '../../../validations';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';

export function useMoopForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const moopConfig = getMoopPAPaths(indexToUse);

  const maps = usePicklistMaps();

  const accumulationMoop =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0]
      ?.accumulation_moop?.[0];

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Does Maximum Out of Pocket Apply?',
          'dropdownSelect',
          moopConfig.apply_ind,
          accumulationMoop?.apply_ind,
          {
            isRequired: true,
            optionsMap: yesNoMap,
          }
        ),
        defineFormField(
          'Accums Tier Name',
          'input',
          moopConfig.accums_tier_name,
          accumulationMoop?.accums_tier_name,
          {
            placeholder: 'Enter Accums Tier Name',
            validations: z
              .string()
              .max(255, 'Accums Tier Name must be less than 255 characters')
              .optional(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Accums Tier Effective Date',
          'datepicker',
          moopConfig.effective_date,
          accumulationMoop?.effective_date,
          {
            isRequired: true,
            validations: z.date(),
          }
        ),
        defineFormField(
          'Accums Tier End Date',
          'datepicker',
          moopConfig.expiration_date,
          accumulationMoop?.expiration_date,
          {
            validations: z.date().optional(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Accums Tier PBC Order',
          'input',
          moopConfig.pbc_order,
          accumulationMoop?.pbc_order,
          {
            isRequired: true,
            placeholder: 'Enter Accums Tier PBC Order',
            validations: z.number().int(),
          }
        ),
        defineFormField(
          'Maximum Out of Pocket Accumulation Period',
          'dropdownSelect',
          moopConfig.accum_period_ind,
          accumulationMoop?.accum_period_ind,
          {
            isRequired: true,
            infoText:
              'Whether the accums period is calendar year (Jan-Dec) or other (e.g. Jul-Jun).',
            optionsMap: maps.esiAccumPeriodMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Specify Maximum Out of Pocket Accumulation Period',
          'dropdownSelect',
          moopConfig.specify_accum_period_ind,
          accumulationMoop?.specify_accum_period_ind,
          {
            isRequired: true,
            infoText:
              'The first through last months that Rx spend counts towards the MOOP (e.g. Apr-Mar).',
            optionsMap: planYearRenewalMap,
          }
        ),
        defineFormField(
          'Benefit Period Length',
          'dropdownSelect',
          moopConfig.benefit_period_length_ind,
          accumulationMoop?.benefit_period_length_ind,
          {
            isRequired: true,
            infoText:
              "Whether the accumulator resets every year, lasts for the patient's lifetime, or other (specify).",
            optionsMap: maps.benefitPeriodLengthMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Period Length - Other',
          'input',
          moopConfig.benefit_period_length_other,
          accumulationMoop?.benefit_period_length_other,
          {
            isRequired: true,
            infoText:
              'Used to specify the Benefit Period, if not calendar or lifetime.',
            placeholder: 'Enter Benefit Period Length - Other',
            validations: z.number().int(),
          }
        ),
        defineFormField(
          'Do Priming Balances Apply?',
          'dropdownSelect',
          moopConfig.priming_balances_ind,
          accumulationMoop?.priming_balances_ind,
          {
            isRequired: true,
            infoText:
              'Carries existing MOOP balances forward when starting a new plan in the middle of current benefit period.',
            optionsMap: yesNoMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Carryover Phase',
          'dropdownSelect',
          moopConfig.carryover_phase_ind,
          accumulationMoop?.carryover_phase_ind,
          {
            isRequired: true,
            infoText:
              'Denotes whether any part of the benefit carries over from one benefit/accum year to the next.',
            optionsMap: maps.carryoverPhaseMap,
          }
        ),
        defineFormField(
          'Describe Carryover Phase',
          'input',
          moopConfig.describe_carryover_phase,
          accumulationMoop?.describe_carryover_phase,
          {
            infoText: 'Provides details of the carryover phase.',
            placeholder: 'Describe Carryover Phase',
            validations: z
              .string()
              .max(2000, 'Value must be less than 2000 characters')
              .optional(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Maximum Out of Pocket Integrated?',
          'dropdownSelect',
          moopConfig.integrated_ind,
          accumulationMoop?.integrated_ind,
          {
            isRequired: true,
            infoText:
              'Whether MOOP is Rx-only (separate) or combined with medical (integrated).',
            optionsMap: maps.integratedMap,
          }
        ),
        defineFormField(
          'Maximum Out of Pocket Embedded?',
          'dropdownSelect',
          moopConfig.embedded_ind,
          accumulationMoop?.embedded_ind,
          {
            isRequired: true,
            infoText:
              'Embedded Accums = Each member must meet the individual MOOP.',
            optionsMap: maps.embeddedMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Individual Maximum Out of Pocket Amount',
          'input',
          moopConfig.individual_plan_amount,
          accumulationMoop?.individual_plan_amount,
          {
            isRequired: true,
            placeholder: 'Enter Individual Maximum Out of Pocket Amount',
            validations: currencyValidation,
          }
        ),
        defineFormField(
          'Family Maximum Out of Pocket Amount',
          'input',
          moopConfig.family_plan_amount,
          accumulationMoop?.family_plan_amount,
          {
            isRequired: true,
            placeholder: 'Enter Family Maximum Out of Pocket Amount',
            validations: currencyValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Employee +1 Dep Maximum Out of Pocket Amount',
          'input',
          moopConfig.employee_1_dep_amount,
          accumulationMoop?.employee_1_dep_amount,
          {
            isRequired: true,
            placeholder: 'Enter Employee +1 Dep Maximum Out of Pocket Amount',
            validations: currencyValidation,
          }
        ),
        defineFormField(
          'Individual Maximum Out of Pocket within Family Amount',
          'input',
          moopConfig.individual_within_family_amount,
          accumulationMoop?.individual_within_family_amount,
          {
            isRequired: true,
            infoText:
              'For non-embedded plans. Some plans only allow the individual to hit up to a certain amount of the family MOOP.',
            placeholder:
              'Enter Individual Maximum Out of Pocket within Family Amount',
            validations: currencyValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Maximum Out of Pocket Applies to Retail, Mail & Paper',
          'dropdownSelect',
          moopConfig.apply_retail_mail_paper_ind,
          accumulationMoop?.apply_retail_mail_paper_ind,
          {
            isRequired: true,
            infoText:
              'Any claim from retail, mail or submitted via paper applies to MOOP.',
            optionsMap: yesNoMap,
          }
        ),
        defineFormField(
          'Penalties Apply to Maximum Out of Pocket?',
          'dropdownSelect',
          moopConfig.penalties_apply_ind,
          accumulationMoop?.penalties_apply_ind,
          {
            isRequired: true,
            infoText: 'Indicates whether DAW penalties count towards the MOOP.',
            optionsMap: maps.yesNoViewBenefitMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Penalties Apply After Maximum Out of Pocket?',
          'dropdownSelect',
          moopConfig.penalties_apply_after_ind,
          accumulationMoop?.penalties_apply_after_ind,
          {
            isRequired: true,
            infoText:
              'Indicates whether DAW penalties are incurred after the MOOP is met.',
            optionsMap: maps.yesNoViewBenefitMap,
          }
        ),
        defineFormField(
          'Shared Indicator',
          'dropdownSelect',
          moopConfig.shared_ind,
          accumulationMoop?.shared_ind,
          {
            isRequired: true,
            optionsMap: maps.sharedIndicatorMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Drug Type Status',
          'dropdownSelect',
          moopConfig.drug_type_status_ind,
          accumulationMoop?.drug_type_status_ind,
          {
            isRequired: true,
            optionsMap: maps.drugTypeStatusMap,
          }
        ),
        defineFormField(
          'Formulary Status',
          'dropdownSelect',
          moopConfig.formulary_status_ind,
          accumulationMoop?.formulary_status_ind,
          {
            isRequired: true,
            optionsMap: maps.formularyStatusMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Network Status',
          'dropdownSelect',
          moopConfig.network_status_ind,
          accumulationMoop?.network_status_ind,
          {
            isRequired: true,
            infoText: 'Network Status',
            optionsMap: maps.networkApplicabilityMap,
          }
        ),
        defineFormField(
          'Pharmacy Channel',
          'dropdownSelect',
          moopConfig.pharmacy_channel_ind,
          accumulationMoop?.pharmacy_channel_ind,
          {
            isRequired: true,
            infoText: 'Pharmacy Channel',
            optionsMap: maps.pharmacyChannelMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Include Drug List',
          'dropdownSelect',
          moopConfig.include_drug_list_ind,
          accumulationMoop?.include_drug_list_ind,
          {
            optionsMap: drugListMap,
          }
        ),
        defineFormField(
          'Exclude Drug List',
          'dropdownSelect',
          moopConfig.exclude_drug_list_ind,
          accumulationMoop?.exclude_drug_list_ind,
          {
            optionsMap: excludeDrugListMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Maximum Out of Pocket Notes',
          'input',
          moopConfig.notes,
          accumulationMoop?.notes,
          {
            placeholder: 'Enter Maximum Out of Pocket Notes',
            validations: z
              .string()
              .max(2000, 'Value must be less than 2000 characters')
              .optional(),
          }
        ),
      ]),
    ]),
  ];

  return { subCategories };
}
