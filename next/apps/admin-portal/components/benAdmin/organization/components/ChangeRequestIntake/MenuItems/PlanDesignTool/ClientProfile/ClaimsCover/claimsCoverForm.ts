import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { claimsCoverConfig } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/claimsCoverConfig';

export function useClaimsCoverForm(
  currentDetails: Partial<OrganizationDetails>
) {
  const {
    yesNoMap,
    cobAllowedMap,
    medicaidSubrogationMap,
    paperClaimsPricingMap,
    esiForeignClaimsMap,
  } = usePicklistMaps();

  const planPdx: Partial<OrganizationDetails['plan']['plan_pdx']> =
    currentDetails?.plan?.plan_pdx ?? {};

  const subCategories: SubCategoryType[] = [
    defineSubCategory(
      'Claims Coverage Settings',
      'Configure which types of claims are covered for this organization.',
      [
        defineInlineFieldGroup([
          defineFormField(
            'Cover Retail Claims',
            'dropdownSelect',
            claimsCoverConfig.retail_claims_covered_ind,
            planPdx?.retail_claims_covered_ind || '',
            {
              optionsMap: yesNoMap || {},
            }
          ),
          defineFormField(
            'Cover Mail Claims',
            'dropdownSelect',
            claimsCoverConfig.mail_claims_covered_ind,
            planPdx.mail_claims_covered_ind || '',
            {
              optionsMap: yesNoMap || {},
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Cover Paper Claims',
            'dropdownSelect',
            claimsCoverConfig.paper_claims_covered_ind,
            planPdx.paper_claims_covered_ind || '',
            {
              optionsMap: yesNoMap || {},

              infoText:
                'Specifies if the group allows paper claims to be submitted for reimbursement.',
            }
          ),
          defineFormField(
            'Cover Out of Network',
            'dropdownSelect',
            claimsCoverConfig.out_of_network_claims_covered_ind,
            planPdx.out_of_network_claims_covered_ind || '',
            {
              optionsMap: yesNoMap || {},

              infoText:
                'Indicates whether the group pays for Rxs filled at out-of-network pharmacies.',
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Cover Foreign Claims',
            'dropdownSelect',
            claimsCoverConfig.foreign_claims_covered,
            planPdx.foreign_claims_covered || '',
            {
              optionsMap: esiForeignClaimsMap || {},

              infoText:
                'Indicates whether the group pays for Rxs filled overseas in the event of emergency.',
            }
          ),
          defineFormField(
            'Allow for Coordination of Benefits (COB)',
            'dropdownSelect',
            claimsCoverConfig.cob_allowed_ind,
            planPdx.cob_allowed_ind || '',
            {
              optionsMap: cobAllowedMap || {},

              infoText:
                'Denotes whether the plan will support coordination of benefits.',
            }
          ),
        ]),
        defineInlineFieldGroup([
          defineFormField(
            'Medicaid Subrogation Claims',
            'dropdownSelect',
            claimsCoverConfig.medicaid_subrogation_ind,
            planPdx.medicaid_subrogation_ind || '',
            {
              optionsMap: medicaidSubrogationMap || {},

              infoText:
                'Who manages claims processing from Medicaid agency, when seeking reimbursement for claims paid on behalf of a member.',
            }
          ),
          defineFormField(
            'Paper Claims Covered - Pricing',
            'dropdownSelect',
            claimsCoverConfig.paper_claims_pricing_ind,
            planPdx.paper_claims_pricing_ind || '',
            {
              optionsMap: paperClaimsPricingMap || {},
            }
          ),
        ]),
      ]
    ),
  ];

  return {
    subCategories,
  };
}
