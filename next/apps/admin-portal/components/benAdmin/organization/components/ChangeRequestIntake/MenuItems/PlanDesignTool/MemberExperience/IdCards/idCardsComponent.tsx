import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useIdCardsForm } from './idCardsForm';

interface IdCardsComponentProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const IdCardsComponent: React.FC<IdCardsComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { subCategories } = useIdCardsForm(
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );

  const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleSaveAndExit = () => {
    // Submit the current form data
    const currentValues = formMethods.getValues();
    submitHandler(currentValues);
  };

  const handleContinue = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem('transition-files-and-details');
    }
  };

  return (
    <GenericForm
      formMethods={formMethods}
      formName="ID cards"
      formDescription="Captures who is responsible for production and distribution of member ID cards."
      subCategories={subCategories}
      onContinue={handleContinue}
      onSaveExit={handleSaveAndExit}
    />
  );
};

export default IdCardsComponent;
