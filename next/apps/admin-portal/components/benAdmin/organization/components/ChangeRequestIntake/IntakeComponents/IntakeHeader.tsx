import { Box, Flex, Heading, Tag, Text } from '@chakra-ui/react';
import { useShowOrganizationHeader } from 'apps/admin-portal/components/benAdmin';
import {
  ChangeRequest,
  OrganizationDetails,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';

import Breadcrumbs from '../Navigation/breadcrumbs';

export const IntakeHeader = ({
  organizationDetails,
  getBreadcrumbPaths,
  onBreadcrumbNavigate,
  title,
}: {
  organizationDetails: OrganizationDetails;
  getBreadcrumbPaths: () => any;
  onBreadcrumbNavigate: (id: string) => void;
  title?: string;
}) => {
  const changeRequest: ChangeRequest | null = JSON.parse(
    sessionStorage.getItem('selectedChangeRequest') || 'null'
  );

  const showOrganizationHeader = useShowOrganizationHeader();

  // Format the go live date
  const goLiveDate = new Date(
    `${changeRequest?.target_effective_date}T00:00:00`
  );
  const formattedDate = goLiveDate.toLocaleDateString('en-US', {
    month: 'long',
    day: '2-digit',
    year: 'numeric',
  });

  const today = new Date();
  const timeDiff = goLiveDate.getTime() - today.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

  const getDaysLeftText = () => {
    if (daysDiff > 0) {
      return `${daysDiff} Days Remaining`;
    } else {
      return `${Math.abs(daysDiff)} Days Ago`;
    }
  };

  return (
    <Box>
      <Flex px={8} py={4} direction="column" gap={3}>
        <Breadcrumbs
          paths={getBreadcrumbPaths()}
          onNavigate={onBreadcrumbNavigate}
        />
        {title && (
          <Heading color="#69696A" size="md" mt={2} mb={2}>
            {title}
          </Heading>
        )}
        {showOrganizationHeader && (
          <>
            <Heading color="#69696A" size="lg">
              {organizationDetails?.organization?.name}
            </Heading>
            <Flex color="#69696A" fontSize="sm" gap={2}>
              Go Live: <Text as="b">{formattedDate}</Text>
              <Tag colorScheme="yellow" size="sm">
                {getDaysLeftText()}
              </Tag>
            </Flex>
          </>
        )}
      </Flex>
    </Box>
  );
};
