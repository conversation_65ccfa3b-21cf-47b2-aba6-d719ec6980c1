import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getDeductiblePAPaths } from '../Config/deductiblePAConfig';
import { drugListMap, excludeDrugListMap } from './maps';

export function getDeductiblePAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getDeductiblePAPaths(selectedIndex);

  // Use map with early return pattern, then filter out null values
  return planDesignDetails
    .map((designDetail) => {
      const planDesignDestailsAccumDeductible =
        designDetail?.accumulation_deductible?.[0];

      // Skip plans without accumulation_deductible details by returning null
      if (!planDesignDestailsAccumDeductible) {
        return null;
      }

      return {
        subtitle: `Pharmacy Accumulator - Deductible - ${
          planDesign?.name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Does Deductible Apply?',
            value: planDesignDestailsAccumDeductible?.apply_ind,
            name: planDesignConfig.apply_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Accums Tier Name',
            value: planDesignDestailsAccumDeductible?.accums_tier_name,
            name: planDesignConfig.accums_tier_name,
            type: 'input',
            placeholder: 'Enter Accums Tier Name',
          },
          {
            label: 'Accums Tier Effective Date',
            value: planDesignDestailsAccumDeductible?.effective_date,
            name: planDesignConfig.effective_date,
            type: 'datepicker',
          },
          {
            label: 'Accum Tier End Date',
            value: planDesignDestailsAccumDeductible?.expiration_date,
            name: planDesignConfig.expiration_date,
            type: 'datepicker',
          },
          {
            label: 'Accums Tier PBC order',
            value: planDesignDestailsAccumDeductible?.pbc_order,
            name: planDesignConfig.pbc_order,
            type: 'input',
            placeholder: 'Enter Accums Tier PBC order',
          },
          {
            label: 'Deductible Accumulation Period',
            value: planDesignDestailsAccumDeductible?.accum_period_ind,
            name: planDesignConfig.accum_period_ind,
            type: 'dropdownSelect',
            optionsMap: maps.esiAccumPeriodMap,
          },
          {
            label: 'Specify Deductible Accumulation Period',
            value: planDesignDestailsAccumDeductible?.specify_accum_period_ind,
            name: planDesignConfig.specify_accum_period_ind,
            type: 'dropdownSelect',
            optionsMap: maps.benefitPeriodsMap,
          },
          {
            label: 'Benefit Period Length',
            value: planDesignDestailsAccumDeductible?.benefit_period_length_ind,
            name: planDesignConfig.benefit_period_length_ind,
            type: 'dropdownSelect',
            optionsMap: maps.benefitPeriodLengthMap,
          },
          {
            label: 'Benefit Period Length - Other',
            value:
              planDesignDestailsAccumDeductible?.benefit_period_length_other,
            name: planDesignConfig.benefit_period_length_other,
            type: 'input',
            placeholder: 'Enter Benefit Period Length - Other',
          },
          {
            label: 'Do Priming Balances Apply?',
            value: planDesignDestailsAccumDeductible?.priming_balances_ind,
            name: planDesignConfig.priming_balances_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Carryover Phase',
            value: planDesignDestailsAccumDeductible?.carryover_phase_ind,
            name: planDesignConfig.carryover_phase_ind,
            type: 'dropdownSelect',
            optionsMap: maps.carryoverPhaseMap,
          },
          {
            label: 'Describe Carryover Phase',
            value: planDesignDestailsAccumDeductible?.describe_carryover_phase,
            name: planDesignConfig.describe_carryover_phase,
            type: 'input',
          },
          {
            label: 'Deductible Integrated?',
            value: planDesignDestailsAccumDeductible?.integrated_ind,
            name: planDesignConfig.integrated_ind,
            type: 'dropdownSelect',
            optionsMap: maps.integratedMap,
          },
          {
            label: 'Deductible Embedded?',
            value: planDesignDestailsAccumDeductible?.embedded_ind,
            name: planDesignConfig.embedded_ind,
            type: 'dropdownSelect',
            optionsMap: maps.embeddedMap,
          },
          {
            label: 'Individual Plan Deductible Amount',
            value: planDesignDestailsAccumDeductible?.individual_plan_amount,
            name: planDesignConfig.individual_plan_amount,
            type: 'input',
            placeholder: 'Enter Individual Plan Deductible Amount',
          },
          {
            label: 'Family Plan Deductible Amount',
            value: planDesignDestailsAccumDeductible?.family_plan_amount,
            name: planDesignConfig.family_plan_amount,
            type: 'input',
            placeholder: 'Enter Family Plan Deductible Amount',
          },
          {
            label: 'Employee + 1 Dedependent Deductible Amount',
            value: planDesignDestailsAccumDeductible?.employee_1_dep_amount,
            name: planDesignConfig.employee_1_dep_amount,
            type: 'input',
            placeholder: 'Enter Employee + 1 Dedependent Deductible Amount',
          },
          {
            label: 'Individual Deductible within Family Amount',
            value:
              planDesignDestailsAccumDeductible?.individual_within_family_amount,
            name: planDesignConfig.individual_within_family_amount,
            type: 'input',
            placeholder: 'Enter Individual Deductible within Family Amount',
          },
          {
            label: 'Does Deductible Apply to Maximum Out of Pocket?',
            value: planDesignDestailsAccumDeductible?.apply_to_moop_ind,
            name: planDesignConfig.apply_to_moop_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Deductible Applies to Retail, Mail & Paper',
            value:
              planDesignDestailsAccumDeductible?.apply_retail_mail_paper_ind,
            name: planDesignConfig.apply_retail_mail_paper_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Penalties Apply to Deductible',
            value: planDesignDestailsAccumDeductible?.penalties_apply_ind,
            name: planDesignConfig.penalties_apply_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoViewBenefitMap,
          },
          {
            label: 'Copay Apply During Deductible Phase',
            value:
              planDesignDestailsAccumDeductible?.copay_apply_during_deductible_phase_ind,
            name: planDesignConfig.copay_apply_during_deductible_phase_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Does Deductible Apply to Brand Medications Only?',
            value: planDesignDestailsAccumDeductible?.apply_brand_only_ind,
            name: planDesignConfig.apply_brand_only_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Does Deductible Apply to Specialty Medications Only?',
            value: planDesignDestailsAccumDeductible?.apply_specialty_only_ind,
            name: planDesignConfig.apply_specialty_only_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Shared Indicator',
            value: planDesignDestailsAccumDeductible?.shared_ind,
            name: planDesignConfig.shared_ind,
            type: 'dropdownSelect',
            optionsMap: maps.sharedIndicatorMap,
          },
          {
            label: 'Drug Type Status',
            value: planDesignDestailsAccumDeductible?.drug_type_status_ind,
            name: planDesignConfig.drug_type_status_ind,
            type: 'dropdownSelect',
            optionsMap: maps.drugTypeStatusMap,
          },
          {
            label: 'Formulary Status',
            value: planDesignDestailsAccumDeductible?.formulary_status_ind,
            name: planDesignConfig.formulary_status_ind,
            type: 'dropdownSelect',
            optionsMap: maps.formularyStatusMap,
          },
          {
            label: 'Network Status',
            value: planDesignDestailsAccumDeductible?.network_status_ind,
            name: planDesignConfig.network_status_ind,
            type: 'dropdownSelect',
            optionsMap: maps.networkApplicabilityMap,
          },
          {
            label: 'Pharmacy Channel',
            value: planDesignDestailsAccumDeductible?.pharmacy_channel_ind,
            name: planDesignConfig.pharmacy_channel_ind,
            type: 'dropdownSelect',
            optionsMap: maps.pharmacyChannelAccumMap,
          },
          {
            label: 'Include Drug List',
            value: planDesignDestailsAccumDeductible?.include_drug_list_ind,
            name: planDesignConfig.include_drug_list_ind,
            type: 'dropdownSelect',
            optionsMap: drugListMap,
          },
          {
            label: 'Exclude Drug List',
            value: planDesignDestailsAccumDeductible?.exclude_drug_list_ind,
            name: planDesignConfig.exclude_drug_list_ind,
            type: 'dropdownSelect',
            optionsMap: excludeDrugListMap,
          },
          {
            label: 'Deductible Notes',
            value: planDesignDestailsAccumDeductible?.notes,
            name: planDesignConfig.notes,
            type: 'input',
            placeholder: 'Enter Deductible Notes',
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[]; // Remove null values and assert type
}
