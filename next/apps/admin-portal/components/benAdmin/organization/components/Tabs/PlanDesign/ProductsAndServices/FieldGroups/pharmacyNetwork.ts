// getPharmacyNetwork.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { buildFieldPath } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

/**
 * getPharmacyNetwork - Generates an array of TemplateFieldConfig for pharmacy network fields.
 *
 * @param formData - The organization details containing pharmacy network data.
 * @returns Array of field configurations for the pharmacy network section.
 */
export const getPharmacyNetwork = (
  formData: OrganizationDetails,
  selectedIndex: number
): Partial<TemplateFieldConfig>[] => {
  const productFeature = formData.features.find((feature) =>
    feature.name.includes('PharmacyNetwork')
  );

  const planDesign = formData.plan.plan_designs?.[selectedIndex];

  // Add proper array validation before calling find
  const pharmacyNetwork =
    planDesign?.plan_features && Array.isArray(planDesign.plan_features)
      ? planDesign.plan_features.find(
          (planFeature) =>
            planFeature.product_feature_id ===
            productFeature?.product_feature_id
        )
      : undefined;

  const getPicklist = (picklistId: number) => {
    const picklistValues = formData.picklists.find(
      (picklist) => picklist.picklist_id === picklistId
    )?.picklist_values;
    return picklistValues?.reduce((acc, item) => {
      acc[item.label] = item.value;
      return acc;
    }, {} as Record<string, string>);
  };

  const pharmacyNetworkFieldConfig = pharmacyNetwork?.plan_feature_items.map(
    (feature_item, index) => {
      return {
        name: buildFieldPath(feature_item, index),
        label: productFeature?.feature_items.find(
          (item) =>
            item.feature_item_id === feature_item.product_feature_item_id
        )?.label,
        value: feature_item.value,
        optionsMap: getPicklist(
          productFeature?.feature_items.find(
            (item) =>
              item.feature_item_id === feature_item.product_feature_item_id
          )?.picklist_id || 0
        ),
        type: 'dropdownSelect',
      };
    }
  );

  return pharmacyNetworkFieldConfig as TemplateFieldConfig[];
};
