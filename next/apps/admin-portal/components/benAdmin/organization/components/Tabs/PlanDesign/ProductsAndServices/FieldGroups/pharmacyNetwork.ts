// getPharmacyNetwork.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { buildFieldPath } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { getCategoryMap } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/ProductsAndServices/CoreProducts/PharmacyNetwork/utils';
/**
 * getPharmacyNetwork - Generates an array of TemplateFieldConfig for pharmacy network fields.
 *
 * @param formData - The organization details containing pharmacy network data.
 * @param selectedIndex - The index of the selected plan design.
 * @param pbmType - The type of PBM ('IsESI', 'IsOptum', 'IsCMK', or undefined).
 * @returns Array of field configurations for the pharmacy network section.
 */
export const getPharmacyNetwork = (
  formData: OrganizationDetails,
  selectedIndex: number,
  pbmType: string | undefined
): Partial<TemplateFieldConfig>[] => {
  const productFeature = formData.features.find((feature) =>
    feature.name.includes('PharmacyNetwork')
  );

  const planDesign = formData.plan.plan_designs?.[selectedIndex];

  // Add proper array validation before calling find
  const pharmacyNetwork =
    planDesign?.plan_features && Array.isArray(planDesign.plan_features)
      ? planDesign.plan_features.find(
          (planFeature) =>
            planFeature.product_feature_id ===
            productFeature?.product_feature_id
        )
      : undefined;

  const getPicklist = (picklistId: number) => {
    const picklistValues = formData.picklists.find(
      (picklist) => picklist.picklist_id === picklistId
    )?.picklist_values;
    return picklistValues?.reduce((acc, item) => {
      acc[item.label] = item.value;
      return acc;
    }, {} as Record<string, string>);
  };

  // Get categoryMap based on pbmType using the utility function
  const categoryMap = getCategoryMap(pbmType);

  // Precompute mapping of name to picklist_name (or name for Text fields)
  const nameToPicklistName = new Map<string, string>();
  const pharmacyNetworkFieldConfig = pharmacyNetwork?.plan_feature_items
    .map((feature_item, index) => {
      const featureItemDetail = productFeature?.feature_items.find(
        (item) => item.feature_item_id === feature_item.product_feature_item_id
      );

      if (!featureItemDetail) return null;

      const fieldPath = buildFieldPath(feature_item, index);
      const optionsMap = getPicklist(featureItemDetail.picklist_id || 0);
      const type =
        featureItemDetail.field_type_label === 'Text'
          ? 'input'
          : 'dropdownSelect';

      // Use picklist_name for Picklist fields, name for Text fields
      const mappingKey =
        featureItemDetail.field_type_label === 'Text'
          ? featureItemDetail.name || ''
          : featureItemDetail.picklist_name || featureItemDetail.name || '';

      nameToPicklistName.set(fieldPath, mappingKey);

      return {
        name: fieldPath,
        label: featureItemDetail.label,
        value: feature_item.value,
        optionsMap,
        type,
      };
    })
    .filter((field) => field !== null) as Partial<TemplateFieldConfig>[];

  // If categoryMap is non-empty, sort fields based on categoryMap order
  if (categoryMap.length > 0) {
    return (
      pharmacyNetworkFieldConfig?.sort((a, b) => {
        const aName = a.name!;
        const bName = b.name!;
        const aPicklistName = nameToPicklistName.get(aName) || '';
        const bPicklistName = nameToPicklistName.get(bName) || '';

        // Determine category and field indices based on categoryMap
        let aCategoryIndex = -1;
        let aFieldIndex = -1;
        let bCategoryIndex = -1;
        let bFieldIndex = -1;

        for (let i = 0; i < categoryMap.length; i++) {
          const category = categoryMap[i];
          const fieldIndices = category.picklistFields.map((field, idx) => ({
            field,
            index: idx,
          }));
          const aMatch = fieldIndices.find((fi) => fi.field === aPicklistName);
          const bMatch = fieldIndices.find((fi) => fi.field === bPicklistName);
          if (aMatch) {
            aCategoryIndex = i;
            aFieldIndex = aMatch.index;
          }
          if (bMatch) {
            bCategoryIndex = i;
            bFieldIndex = bMatch.index;
          }
        }

        // If both fields are in categories, sort by category index first, then field index
        if (aCategoryIndex !== -1 && bCategoryIndex !== -1) {
          if (aCategoryIndex === bCategoryIndex) {
            return aFieldIndex - bFieldIndex;
          }
          return aCategoryIndex - bCategoryIndex;
        }
        // If only one is in a category, prioritize it
        if (aCategoryIndex !== -1) return -1;
        if (bCategoryIndex !== -1) return 1;
        // If neither matches, maintain original order (stable sort)
        return 0;
      }) || []
    );
  }

  // Fallback to default order if categoryMap is empty
  return pharmacyNetworkFieldConfig || [];
};
