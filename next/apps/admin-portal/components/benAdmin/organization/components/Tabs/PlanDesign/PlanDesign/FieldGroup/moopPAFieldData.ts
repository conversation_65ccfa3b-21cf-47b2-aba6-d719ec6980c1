import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { getMoopPAPath } from '../Config/moopPAConfig';

export function getMoopPAFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) return [];

  const planDesignDetails = planDesign?.plan_design_details || [];

  const moopTemplates: TemplateFieldGroup[] = [];

  const productName = planData?.plan?.product?.name;

  planDesignDetails.forEach((detail) => {
    const accumulationMoopList = detail?.accumulation_moop;
    if (!Array.isArray(accumulationMoopList)) return;

    accumulationMoopList.forEach((moopEntry, index) => {
      moopTemplates.push({
        subtitle: `Pharmacy Accumulator - Maximum Out of Pocket - ${
          moopEntry.accums_tier_name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          ...([
            productNames.CMK_360,
            productNames.ESI_360,
            productNames.OPT_360,
            productNames.IRX_360,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'Does Maximum Out of Pocket Apply?',
                  value: moopEntry?.apply_ind,
                  name: getMoopPAPath('apply_ind', index),
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                },
              ]
            : []),
          ...(moopEntry.apply_ind === 1
            ? [
                ...([
                  productNames.CMK_360,
                  productNames.ESI_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Accums Tier Name',
                        value: moopEntry?.accums_tier_name,
                        name: getMoopPAPath('accums_tier_name', index),
                        type: 'input' as const,
                        placeholder: 'Enter Accums Tier Name',
                      },
                      {
                        label: 'Accums Tier Effective Date',
                        value: moopEntry?.effective_date,
                        name: getMoopPAPath('effective_date', index),
                        type: 'datepicker' as const,
                      },
                      {
                        label: 'Accums Tier End Date',
                        value: moopEntry?.expiration_date,
                        name: getMoopPAPath('expiration_date', index),
                        type: 'datepicker' as const,
                      },
                      {
                        label: 'Accums Tier PBC order',
                        value: moopEntry?.pbc_order,
                        name: getMoopPAPath('pbc_order', index),
                        type: 'input' as const,
                        placeholder: 'Enter Accums Tier PBC order',
                      },
                      {
                        label: 'Maximum Out of Pocket Accumulation Period',
                        value: moopEntry?.accum_period_ind,
                        name: getMoopPAPath('accum_period_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.esiAccumPeriodMap,
                      },
                      {
                        label:
                          'Specify Maximum Out of Pocket Accumulation Period',
                        value: moopEntry?.specify_accum_period_ind,
                        name: getMoopPAPath('specify_accum_period_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.benefitPeriodsMap,
                      },
                    ]
                  : []),
                ...(productName === productNames.ESI_360
                  ? [
                      {
                        label: 'Benefit Period Length',
                        value: moopEntry?.benefit_period_length_ind,
                        name: getMoopPAPath('benefit_period_length_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.benefitPeriodLengthMap,
                      },
                      {
                        label: 'Benefit Period Length - Other',
                        value: moopEntry?.benefit_period_length_other,
                        name: getMoopPAPath(
                          'benefit_period_length_other',
                          index
                        ),
                        type: 'input' as const,
                        placeholder: 'Enter Benefit Period Length - Other',
                      },
                    ]
                  : []),
                ...([
                  productNames.CMK_360,
                  productNames.ESI_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Do Priming Balances Apply?',
                        value: moopEntry?.priming_balances_ind,
                        name: getMoopPAPath('priming_balances_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label: 'Carryover Phase',
                        value: moopEntry?.carryover_phase_ind,
                        name: getMoopPAPath('carryover_phase_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.carryoverPhaseMap,
                      },
                      {
                        label: 'Describe Carryover Phase',
                        value: moopEntry?.describe_carryover_phase,
                        name: getMoopPAPath('describe_carryover_phase', index),
                        type: 'input' as const,
                        placeholder: 'Enter Describe Carryover Phase',
                      },
                      {
                        label: 'Maximum Out of Pocket Integrated?',
                        value: moopEntry?.integrated_ind,
                        name: getMoopPAPath('integrated_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.integratedMap,
                      },
                      {
                        label: 'Maximum Out of Pocket Embedded?',
                        value: moopEntry?.embedded_ind,
                        name: getMoopPAPath('embedded_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.embeddedMap,
                      },
                      {
                        label: 'Individual Maximum Out of Pocket Amount',
                        value: moopEntry?.individual_plan_amount,
                        name: getMoopPAPath('individual_plan_amount', index),
                        type: 'input' as const,
                        placeholder:
                          'Enter Individual Maximum Out of Pocket Amount',
                      },
                      {
                        label: 'Family Maximum Out of Pocket Amount',
                        value: moopEntry?.family_plan_amount,
                        name: getMoopPAPath('family_plan_amount', index),
                        type: 'input' as const,
                        placeholder:
                          'Enter Family Maximum Out of Pocket Amount',
                      },
                      {
                        label: 'Employee +1 Dep Maximum Out of Pocket Amount',
                        value: moopEntry?.employee_1_dep_amount,
                        name: getMoopPAPath('employee_1_dep_amount', index),
                        type: 'input' as const,
                        placeholder:
                          'Enter Employee +1 Dep Maximum Out of Pocket Amount',
                      },
                      {
                        label:
                          'Individual Maximum Out of Pocket within Family Amount',
                        value: moopEntry?.individual_within_family_amount,
                        name: getMoopPAPath(
                          'individual_within_family_amount',
                          index
                        ),
                        type: 'input' as const,
                        placeholder:
                          'Enter Individual Maximum Out of Pocket within Family Amount',
                      },
                      {
                        label:
                          'Maximum Out of Pocket Applies to Retail, Mail & Paper',
                        value: moopEntry?.apply_retail_mail_paper_ind,
                        name: getMoopPAPath(
                          'apply_retail_mail_paper_ind',
                          index
                        ),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoMap,
                      },
                      {
                        label: 'DAW Penalties Apply To Maximum Out of Pocket?',
                        value: moopEntry?.penalties_apply_ind,
                        name: getMoopPAPath('penalties_apply_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoViewBenefitMap,
                      },
                      {
                        label:
                          'DAW Penalties Apply After Maximum Out of Pocket?',
                        value: moopEntry?.penalties_apply_after_ind,
                        name: getMoopPAPath('penalties_apply_after_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.yesNoViewBenefitMap,
                      },
                    ]
                  : []),
                ...(productName === productNames.ESI_360
                  ? [
                      {
                        label: 'Shared Indicator',
                        value: moopEntry?.shared_ind,
                        name: getMoopPAPath('shared_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.sharedIndicatorMap,
                      },
                      {
                        label: 'Drug Type Status',
                        value: moopEntry?.drug_type_status_ind,
                        name: getMoopPAPath('drug_type_status_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.drugTypeStatusMap,
                      },
                      {
                        label: 'Formulary Status',
                        value: moopEntry?.formulary_status_ind,
                        name: getMoopPAPath('formulary_status_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.formularyStatusMap,
                      },
                    ]
                  : []),
                ...([
                  productNames.CMK_360,
                  productNames.ESI_360,
                  productNames.OPT_360,
                  productNames.IRX_360,
                ].includes(productName as productNames)
                  ? [
                      {
                        label: 'Network Status',
                        value: moopEntry?.network_status_ind,
                        name: getMoopPAPath('network_status_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.networkApplicabilityMap || {},
                      },
                    ]
                  : []),
                ...(productName === productNames.ESI_360
                  ? [
                      {
                        label: 'Pharmacy Channel',
                        value: moopEntry?.pharmacy_channel_ind,
                        name: getMoopPAPath('pharmacy_channel_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.pharmacyChannelMap,
                      },
                      {
                        label: 'Include Drug List',
                        value: moopEntry?.include_drug_list_ind,
                        name: getMoopPAPath('include_drug_list_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.accumDrugListMap,
                      },
                      {
                        label: 'Exclude Drug List',
                        value: moopEntry?.exclude_drug_list_ind,
                        name: getMoopPAPath('exclude_drug_list_ind', index),
                        type: 'dropdownSelect' as const,
                        optionsMap: maps.accumDrugListMap,
                      },
                      {
                        label: 'Maximum Out of Pocket Notes',
                        value: moopEntry?.notes,
                        name: getMoopPAPath('notes', index),
                        type: 'input' as const,
                        placeholder: 'Enter Maximum Out of Pocket Notes',
                      },
                    ]
                  : []),
              ]
            : []),
        ],
      });
    });
  });

  return moopTemplates.filter(Boolean) as TemplateFieldGroup[];
}
