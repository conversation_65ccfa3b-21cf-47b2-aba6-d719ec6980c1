// getIdCardFields.ts
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { idCardConfig } from '../Config/idCardConfig';

export const getIdCardFields = (
  formData: OrganizationDetails,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  // Extract plan_material from formData
  const planMaterial = formData?.plan?.plan_material;
  const productName = formData?.plan?.product?.name;
  return [
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Responsible for ID Cards',
            value: planMaterial?.id_card_responsible_ind,
            name: idCardConfig.id_card_responsible_ind, // Uses the flattened config
            type: 'dropdownSelect' as const,
            optionsMap: maps.idCardsResponsibleMap,
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'ID Card Type',
            value: planMaterial?.id_card_type_ind,
            name: idCardConfig.id_card_type_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.idCardTypeMap,
          },

          {
            label: 'Logo on ID Cards',
            value: planMaterial?.id_card_logo_ind,
            name: idCardConfig.id_card_logo_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoNaMap,
          },
          {
            label: 'Extract Alternate ID',
            value: planMaterial?.employee_id_source_ind,
            name: idCardConfig.employee_id_source_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.employeeIdSourceMap,
          },
          {
            label: 'ID Card Mailing',
            value: planMaterial?.id_card_mailing_ind,
            name: idCardConfig.id_card_mailing_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.idCardMailingMap,
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'PCN',
            value: planMaterial?.pcn,
            name: idCardConfig.pcn,
          },
        ]
      : []),
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Rx BIN',
            value: planMaterial?.rx_bin,
            name: idCardConfig.rx_bin,
          },
        ]
      : []),
  ];
};
