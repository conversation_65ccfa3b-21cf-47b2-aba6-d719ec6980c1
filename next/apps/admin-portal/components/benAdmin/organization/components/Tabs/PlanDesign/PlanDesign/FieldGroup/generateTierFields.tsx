// generateTierFields.ts
import { PlanDesignDetail } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import GenericTableModal from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Template/GenericTableModalComponent';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';

import { getTierTableColumns } from '../Config/tierConfigs';

/**
 * Flexible generator function for tier fields with modal.
 * Accepts either formMethods (from react-hook-form) or direct formData.
 */
export function generateTierFields(
  formMethodsOrData: UseFormReturn<any> | any,
  isUnbreakable: boolean,
  selectedIndex: number,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] {
  // If formMethods is provided, use watch to get reactive data
  const formData = formMethodsOrData?.watch
    ? formMethodsOrData.watch()
    : formMethodsOrData;

  const tierTableColumns = getTierTableColumns();
  let tierCounter = 1;
  const fields: Partial<TemplateFieldConfig>[] = [];
  const planDesigns = formData?.plan?.plan_designs ?? [];

  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }
  // Ensure planDesignDetails is always an array
  const planDesignDetails = Array.isArray(planDesign?.plan_design_details)
    ? planDesign.plan_design_details
    : [planDesign?.plan_design_details].filter(Boolean);
  // Now safely iterate through the array
  planDesignDetails.forEach((planDesignDetail: PlanDesignDetail) => {
    if (!planDesignDetail) return;

    const costShareDesigns = Array.isArray(planDesignDetail.cost_share_design)
      ? planDesignDetail.cost_share_design
      : [];

    const filteredCostShareDesigns = costShareDesigns.filter(
      (c) => c.pre_packaged_ind === (isUnbreakable ? 1 : 0)
    );

    const groupedTiers: Record<string, any[]> = {};
    const tierMetadata: Record<string, any> = {};

    filteredCostShareDesigns.forEach((costShareDesign) => {
      const tierName = costShareDesign.tier_ind || `Tier ${tierCounter++}`;
      const costShareTiers = costShareDesign.cost_share_tiers ?? [];

      const pharmacyChannelIndicator =
        costShareDesign.pharmacy_channel_ind === 5
          ? costShareDesign.pharmacy_list || '-'
          : costShareDesign.pharmacy_channel_ind != null
          ? maps.pharmacyChannelMap?.[costShareDesign.pharmacy_channel_ind] ||
            '-'
          : '-';
      const formattedCostShareTiers =
        costShareTiers.length > 0
          ? costShareTiers.map((tier) => {
              const costShareType = costShareDesign.cost_share_type_ind;
              const isCoInsurance = costShareType === 2 || costShareType === 3;
              const amount =
                tier.co_pay_amount !== null &&
                tier.co_pay_amount !== undefined &&
                !isCoInsurance
                  ? Number(tier.co_pay_amount)
                  : tier.co_insurance_pct !== null &&
                    tier.co_insurance_pct !== undefined
                  ? Number(tier.co_insurance_pct)
                  : null;
              let datatype = 'percent';

              if (!isCoInsurance) {
                datatype = 'dollar';
              }

              return {
                ...tier,
                days_supply:
                  typeof tier.days_supply === 'string'
                    ? tier.days_supply.replace('-', ' - ')
                    : String(tier.days_supply),
                drug_list_ind: costShareDesign.drug_list_ind,
                pharmacy_channel_ind: pharmacyChannelIndicator,
                copay_coinsurance_amt: amount,
                datatype: datatype,
                effective_date: costShareDesign.effective_date,
                expiration_date: costShareDesign.expiration_date,
                ...(tier.co_insurance_ltgt_ind === 0 && {
                  co_insurance_cap_amount_min: tier.co_insurance_cap_amount,
                }),
                ...(tier.co_insurance_ltgt_ind === 1 && {
                  co_insurance_cap_amount_max: tier.co_insurance_cap_amount,
                }),
              };
            })
          : [
              {
                pharmacy_channel_ind: pharmacyChannelIndicator,
                effective_date: costShareDesign.effective_date,
                expiration_date: costShareDesign.expiration_date,
                drug_list_ind: costShareDesign.drug_list_ind,
              },
            ];

      if (!groupedTiers[tierName]) {
        groupedTiers[tierName] = [];
      }
      groupedTiers[tierName].push(...formattedCostShareTiers);
    });

    Object.entries(groupedTiers).forEach(([tierName, tierRows]) => {
      fields.push({
        label: `Tier Type ${tierCounter}`,
        name: `tier_type_${tierCounter}`,
        value: (
          <>
            <div style={{ fontWeight: 'bold' }}>{tierName}</div>
            <div>{tierMetadata[tierName]}</div>
            <GenericTableModal
              data={tierRows}
              columns={tierTableColumns}
              linkText="View Details"
              modalTitle={`Patient Pay - ${
                isUnbreakable ? 'Unbreakable Package' : 'Standard'
              }`}
              description="This table shows detailed cost sharing information for this tier."
              metadata={[
                {
                  label: 'Tier Type',
                  value: `${tierName}`,
                },
              ]}
            />
          </>
        ),
      });
      tierCounter++;
    });
  });

  return fields;
}
