import {
  generatePaths,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { PLAN_DESIGN_DETAILS_BASE_PATH } from './coreConfig';

export interface AccumsGeneralPaths extends Record<string, string> {
  accum_transfer_ind: string;
  accum_integrated_ind: string;
  max_coverage_amount: string;
  medical_integration_tier_ind: string;
  accumulation_other: string;
  accumulation_deductible: string;
  accumulation_moop: string;
}

export const accumsGeneralConfig: AccumsGeneralPaths = {
  accum_transfer_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.accum_transfer_ind`,
  accum_integrated_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.accum_integrated_ind`,
  max_coverage_amount: `${PLAN_DESIGN_DETAILS_BASE_PATH}.max_coverage_amount`,
  medical_integration_tier_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.medical_integration_tier_ind`,
  accumulation_other: `${PLAN_DESIGN_DETAILS_BASE_PATH}.accumulation_other`,
  accumulation_deductible: `${PLAN_DESIGN_DETAILS_BASE_PATH}.accumulation_deductible`,
  accumulation_moop: `${PLAN_DESIGN_DETAILS_BASE_PATH}.accumulation_moop`,
};

export function getAccumsGeneralPath(
  field: keyof AccumsGeneralPaths,
  index: number
): string {
  return replacePlaceholder(accumsGeneralConfig[field], index);
}

export function getAccumsGeneralPaths(index: number): AccumsGeneralPaths {
  return generatePaths(
    accumsGeneralConfig as Record<string, string>,
    index
  ) as AccumsGeneralPaths;
}
