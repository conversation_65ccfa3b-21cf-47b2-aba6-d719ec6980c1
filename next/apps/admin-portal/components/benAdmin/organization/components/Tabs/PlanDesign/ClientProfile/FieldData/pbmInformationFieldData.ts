// Example usage of the flattened PBM information configuration

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { getMedicalVendorFields, pbmConfig } from '../Config/pbmConfig';

export const getPBMInformationFields = (
  planDetails: Partial<OrganizationDetails>
): Partial<TemplateFieldConfig>[] => {
  const productName = planDetails?.plan?.product?.name;
  return [
    {
      label: 'Chosen PBM',
      name: pbmConfig.vendor_name,
      value: planDetails?.plan?.product?.vendor?.name,
    },
    ...(productName === productNames.CMK_360 ||
    productName === productNames.ESI_360 ||
    productName === productNames.OPT_360 ||
    productName === productNames.IRX_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? getMedicalVendorFields(planDetails) || []
      : []),
    {
      label: 'Carrier Number',
      value: planDetails?.plan?.carrier_number,
      name: pbmConfig.carrier_number,
    },
    ...(productName !== productNames.ESI_360
      ? [
          {
            label: 'Account Number',
            value: planDetails?.plan?.account_number,
            name: pbmConfig.account_number,
          },
        ]
      : []),
    ...(productName === productNames.ESI_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          {
            label: 'Contract Name',
            value: planDetails?.plan?.contract_name,
            name: pbmConfig.contract_name,
          },
          {
            label: 'Paid Contract Number',
            value: planDetails?.plan?.paid_contract_number,
            name: pbmConfig.paid_contract_number,
          },
          {
            label: 'Umbrella Number',
            value: planDetails?.plan?.group_umbrella_number,
            name: pbmConfig.group_umbrella_number,
          },
        ]
      : []),
  ];
};
