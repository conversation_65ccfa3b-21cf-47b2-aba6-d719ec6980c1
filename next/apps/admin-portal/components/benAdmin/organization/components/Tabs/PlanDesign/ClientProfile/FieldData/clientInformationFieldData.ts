/**
 * getClientInformationFields
 *
 * This function defines the field configuration for the client information section,
 * which is used to build a single UI section via a reusable template.
 *
 * Each field configuration includes:
 *  - label: The display label for the field.
 *  - value: The value retrieved from the backend. For nested properties, optional chaining is used (e.g., `orgDetails?.address?.address_line_1`).
 *  - name: The JSON key path used to structure the final form data. It must match the value's path but without the optional chaining (e.g., `address.address_line_1`).
 *
 * IMPORTANT:
 *  The 'name' property must exactly mirror the backend value structure (minus the question marks)
 *  to ensure that the final JSON output is correctly structured. For example, if a field's value is
 *  defined as `orgDetails?.address?.address_line_1`, then its corresponding name must be `address.address_line_1`.
 *
 * Developer Enforcement Ideas:
 *  - Create a helper function or utility that generates the 'name' property automatically from the expected data path.
 *  - Write unit tests or custom ESLint rules to verify that for each field configuration, the 'name'
 *    follows the correct pattern based on the backend value structure.
 *
 * This consistency is crucial because any mismatch will result in an incorrect JSON structure in the final form submission.
 */

// Example usage of the flattened client configuration

import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { clientConfig } from '../Config/clientConfig';

// Define field configuration with the flattened client config
export const getClientInformationFields = (
  orgDetails: Partial<OrganizationDetails>,
  orgName: string,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => [
  {
    label: 'Legal Name',
    value: orgName,
    name: 'orgName',
  },
  {
    label: 'Employer Address',
    name: clientConfig.address_line_1,
    value: orgDetails?.organization?.address?.address_line_1,
  },
  {
    label: 'Employer Address 2',
    name: clientConfig.address_line_2,
    value: orgDetails?.organization?.address?.address_line_2,
  },
  {
    label: 'Employer City',
    name: clientConfig.city,
    value: orgDetails?.organization?.address?.city,
  },
  {
    label: 'Employer State',
    name: clientConfig.state,
    value: orgDetails?.organization?.address?.state,
  },
  {
    label: 'Employer Zip',
    name: clientConfig.postal_code,
    value: orgDetails?.organization?.address?.postal_code,
  },
  {
    label: 'Employer Country',
    name: clientConfig.country,
    value: orgDetails?.organization?.address?.country,
  },
  (() => {
    const planBenefitPeriod =
      orgDetails?.plan?.plan_designs?.[0]?.plan_design_details?.[0]
        ?.benefit_period_ind;

    // Check if all plan designs have the same benefit period
    const allMatch = orgDetails?.plan?.plan_designs?.every((design) =>
      design?.plan_design_details?.every(
        (detail) => detail?.benefit_period_ind == planBenefitPeriod
      )
    );

    if (allMatch) {
      return {
        label: 'Benefit Period',
        value: planBenefitPeriod,
        name: clientConfig.benefit_period_ind,
        type: 'dropdownSelect',
        optionsMap: maps.benefitPeriodsMap,
      };
    } else {
      return {
        label: 'Benefit Period',
        value: 'View on Benefit',
        name: clientConfig.benefit_period_ind,
      };
    }
  })(),
  {
    label: 'Plan Year/Renewal',
    value: orgDetails?.plan?.plan_year_renewal,
    name: clientConfig.plan_year_renewal,
    type: 'dropdownSelect',
    optionsMap: maps.benefitPeriodsMap,
  },
  {
    label: 'Renewal Month',
    value: orgDetails?.plan?.renewal_month,
    name: clientConfig.renewal_month,
    type: 'dropdownSelect',
    optionsMap: maps.monthsMap,
  },
  {
    label: 'Annual Reporting Start Month',
    value: orgDetails?.plan?.annual_reporting_start_month,
    name: clientConfig.annual_reporting_start_month,
    type: 'dropdownSelect',
    optionsMap: maps.monthsMap,
  },
  {
    label: 'Plan Class',
    value: orgDetails?.plan?.product?.product_class_id,
    name: clientConfig.product_class_id,
    type: 'dropdownSelect',
    optionsMap: maps.planClassMap,
  },
  {
    label: 'ERISA Status',
    value: orgDetails?.plan?.erisa_ind,
    name: clientConfig.erisa_ind,
    type: 'dropdownSelect',
    optionsMap: maps.erisaMap,
  },
  {
    label: 'Previous PBM',
    value: orgDetails?.plan?.previous_pbm,
    name: clientConfig.previous_pbm,
  },
  {
    label: 'Primary Termination Reason',
    value: orgDetails?.organization?.primary_termination_reason,
    name: clientConfig.primary_termination_reason,
    type: 'dropdownSelect',
    optionsMap: maps.terminationReasonMap,
  },
  {
    label: 'Secondary Termination Reason',
    value: orgDetails?.organization?.secondary_termination_reason,
    name: clientConfig.secondary_termination_reason,
    type: 'dropdownSelect',
    optionsMap: maps.terminationReasonMap,
  },
  {
    label: 'Termination Details',
    value: orgDetails?.organization?.termination_notes,
    name: clientConfig.termination_notes,
  },
  {
    label: 'Is Client a 340B entity?',
    value: orgDetails?.plan?.is_340b_ind,
    name: clientConfig.is_340b_ind,
    type: 'dropdownSelect',
    optionsMap: maps.yesNoMap,
  },
  {
    label: 'Plan Overview Note',
    value: orgDetails?.plan?.overview_notes,
    name: clientConfig.overview_notes,
  },
  {
    label: 'Company Head Quarter State',
    value: orgDetails?.organization?.company_hq_state_ind,
    name: clientConfig.company_hq_state_ind,
    type: 'dropdownSelect',
    optionsMap: maps.stateMap,
  },
];
