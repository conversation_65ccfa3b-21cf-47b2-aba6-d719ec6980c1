import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import { welcomeLettersConfig } from '../Config/welcomeLettersConfig';
/**
 * getWelcomeLettersFields - Generates an array of TemplateFieldConfig for the
 * welcome letters section using centralized field names.
 *
 * @param formData - The organization details containing plan_material data.
 * @returns Array of field configurations for the welcome letters section.
 */
export const getWelcomeLettersFields = (
  formData: OrganizationDetails,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planMaterial = formData?.plan?.plan_material;
  const productName = formData?.plan?.product?.name;
  return [
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Name on Member Materials',
            value: planMaterial?.member_materials_name,
            name: welcomeLettersConfig.member_materials_name,
          },
          {
            label: 'Member Packets',
            value: planMaterial?.member_packets_ind,
            name: welcomeLettersConfig.member_packets_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.memberPacketsMap,
          },
        ]
      : []),
    {
      label: 'Disruption Letters',
      value: planMaterial?.disruption_letters_ind,
      name: welcomeLettersConfig.disruption_letters_ind,
      type: 'dropdownSelect',
      optionsMap: maps.yesNoMap,
    },
    ...([
      productNames.CMK_360,
      productNames.ESI_360,
      productNames.OPT_360,
      productNames.IRX_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Specialty Letters',
            value: planMaterial?.specialty_letters_ind,
            name: welcomeLettersConfig.specialty_letters_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Mail Order Letters',
            value: planMaterial?.mail_order_letters_ind,
            name: welcomeLettersConfig.mail_order_letters_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Allow for customization',
            value: planMaterial?.allow_customization_ind,
            name: welcomeLettersConfig.allow_customization_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
          },
        ]
      : []),
    ...([
      productNames.ESI_360,
      productNames.CMK_DIRECT,
      productNames.ESI_DIRECT,
    ].includes(productName as productNames)
      ? [
          {
            label: 'Marketing Outreach Opt Out Home Delivery',
            value: planMaterial?.marketing_outreach_home_delivery_ind,
            name: welcomeLettersConfig.marketing_outreach_home_delivery_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Marketing Outreach Opt Out Specialty Service',
            value: planMaterial?.marketing_outreach_specialty_service_ind,
            name: welcomeLettersConfig.marketing_outreach_specialty_service_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
          },
        ]
      : []),
    {
      label: 'ID Cards & Member Materials Note',
      value: planMaterial?.notes,
      name: welcomeLettersConfig.notes,
    },
  ];
};
