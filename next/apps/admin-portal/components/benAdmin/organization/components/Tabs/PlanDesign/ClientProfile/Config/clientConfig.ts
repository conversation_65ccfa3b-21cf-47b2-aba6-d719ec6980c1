// clientConfig.ts

import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { BASE_PATHS } from './configs';

// Define field names without full paths
const ADDRESS_FIELDS = {
  address_line_1: 'address_line_1',
  address_line_2: 'address_line_2',
  city: 'city',
  state: 'state',
  postal_code: 'postal_code',
  country: 'country',
};

const ORGANIZATION_FIELDS = {
  org_name: 'name',
  legal_entity_name: 'legal_entity_name',
  org_type_id: 'org_type_id',
  ein: 'ein',
  employer_size: 'employer_size',
  industry_id: 'industry_id',
  primary_termination_reason: 'primary_termination_reason',
  secondary_termination_reason: 'secondary_termination_reason',
  termination_notes: 'termination_notes',
  company_hq_state_ind: 'company_hq_state_ind',
};

const PLAN_FIELDS = {
  benefit_period_ind: 'benefit_period_ind',
  plan_year_renewal: 'plan_year_renewal',
  renewal_month: 'renewal_month',
  annual_reporting_start_month: 'annual_reporting_start_month',
  erisa_ind: 'erisa_ind',
  previous_pbm: 'previous_pbm',
  carrier_number: 'carrier_number',
  account_number: 'account_number',
  contract_name: 'contract_name',
  paid_contract_number: 'paid_contract_number',
  group_umbrella_number: 'group_umbrella_number',
  implementation_start_date: 'implementation_start_date',
  implementation_timeline: 'implementation_timeline',
  open_enrollment_start_date: 'open_enrollment_start_date',
  open_enrollment_end_date: 'open_enrollment_end_date',
  open_enrollment_support: 'open_enrollment_support',
  is_340b_ind: 'is_340b_ind',
  overview_notes: 'overview_notes',
};

/**
 * Type definition for the flattened client configuration
 */
export interface ClientConfig {
  // Address fields
  address_line_1: string;
  address_line_2: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;

  // Organization fields
  org_name: string;
  legal_entity_name: string;
  org_type_id: string;
  ein: string;
  employer_size: string;
  industry_id: string;
  primary_termination_reason: string;
  secondary_termination_reason: string;
  termination_notes: string;
  company_hq_state_ind: string;

  // Plan fields
  benefit_period_ind: string;
  plan_year_renewal: string;
  renewal_month: string;
  annual_reporting_start_month: string;
  product_class_id: string;
  erisa_ind: string;
  previous_pbm: string;
  chosen_pbm_legal_entity_name: string;
  carrier_number: string;
  account_number: string;
  contract_name: string;
  paid_contract_number: string;
  group_umbrella_number: string;
  implementation_start_date: string;
  implementation_timeline: string;
  open_enrollment_start_date: string;
  open_enrollment_end_date: string;
  open_enrollment_support: string;
  is_340b_ind: string;
  overview_notes: string;
  cobra_ind: string;
  active_employees_ind: string;
  retirees_ind: string;
  retirees_trust_ind: string;
  retiree_subsidy_ind: string;
  dependent_age: string;
  dependent_age_ind: string;
  student_age: string;
  student_age_ind: string;
  dependent_alt_groups_ind: string;
  fsa_ind: string;
  fsa_substantiation_needed: string;
  hra_ind: string;
  hra_substantiation_needed: string;
  hsa_ind: string;
  hsa_substantiation_needed: string;
}

// Generate the full paths
export const clientConfig: ClientConfig = {
  // Address fields
  address_line_1: getPropertyPath(
    BASE_PATHS.ORGANIZATION_ADDRESS,
    ADDRESS_FIELDS.address_line_1
  ),
  address_line_2: getPropertyPath(
    BASE_PATHS.ORGANIZATION_ADDRESS,
    ADDRESS_FIELDS.address_line_2
  ),
  city: getPropertyPath(BASE_PATHS.ORGANIZATION_ADDRESS, ADDRESS_FIELDS.city),
  state: getPropertyPath(BASE_PATHS.ORGANIZATION_ADDRESS, ADDRESS_FIELDS.state),
  postal_code: getPropertyPath(
    BASE_PATHS.ORGANIZATION_ADDRESS,
    ADDRESS_FIELDS.postal_code
  ),
  country: getPropertyPath(
    BASE_PATHS.ORGANIZATION_ADDRESS,
    ADDRESS_FIELDS.country
  ),

  // Organization fields
  org_name: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.org_name
  ),
  legal_entity_name: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.legal_entity_name
  ),
  org_type_id: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.org_type_id
  ),
  ein: getPropertyPath(BASE_PATHS.ORGANIZATION, ORGANIZATION_FIELDS.ein),
  employer_size: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.employer_size
  ),
  industry_id: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.industry_id
  ),
  primary_termination_reason: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.primary_termination_reason
  ),
  secondary_termination_reason: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.secondary_termination_reason
  ),
  termination_notes: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.termination_notes
  ),
  company_hq_state_ind: getPropertyPath(
    BASE_PATHS.ORGANIZATION,
    ORGANIZATION_FIELDS.company_hq_state_ind
  ),

  // Plan fields
  benefit_period_ind: getPropertyPath(
    BASE_PATHS.PLAN_DESIGN_DETAILS,
    PLAN_FIELDS.benefit_period_ind
  ),
  plan_year_renewal: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.plan_year_renewal
  ),
  renewal_month: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.renewal_month),
  annual_reporting_start_month: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.annual_reporting_start_month
  ),
  erisa_ind: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.erisa_ind),
  previous_pbm: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.previous_pbm),
  carrier_number: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.carrier_number),
  account_number: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.account_number),
  contract_name: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.contract_name),
  paid_contract_number: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.paid_contract_number
  ),
  group_umbrella_number: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.group_umbrella_number
  ),
  implementation_start_date: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.implementation_start_date
  ),
  implementation_timeline: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.implementation_timeline
  ),
  open_enrollment_start_date: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.open_enrollment_start_date
  ),
  open_enrollment_end_date: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.open_enrollment_end_date
  ),
  open_enrollment_support: getPropertyPath(
    BASE_PATHS.PLAN,
    PLAN_FIELDS.open_enrollment_support
  ),
  is_340b_ind: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.is_340b_ind),
  overview_notes: getPropertyPath(BASE_PATHS.PLAN, PLAN_FIELDS.overview_notes),

  // Product specific fields
  product_class_id: getPropertyPath(
    BASE_PATHS.PLAN_PRODUCT,
    'product_class_id'
  ),
  chosen_pbm_legal_entity_name: getPropertyPath(
    BASE_PATHS.PLAN_PRODUCT_VENDOR_PBM,
    'name'
  ),

  // Eligibility fields
  cobra_ind: getPropertyPath(BASE_PATHS.PLAN_ELIGIBILITY, 'cobra_ind'),
  active_employees_ind: getPropertyPath(
    BASE_PATHS.PLAN_ELIGIBILITY,
    'active_employees_ind'
  ),
  retirees_ind: getPropertyPath(BASE_PATHS.PLAN_ELIGIBILITY, 'retirees_ind'),
  retirees_trust_ind: getPropertyPath(
    BASE_PATHS.PLAN_PDX,
    'retirees_trust_ind'
  ),
  retiree_subsidy_ind: getPropertyPath(
    BASE_PATHS.PLAN_ELIGIBILITY,
    'retiree_subsidy_ind'
  ),
  dependent_age: getPropertyPath(BASE_PATHS.PLAN_ELIGIBILITY, 'dependent_age'),
  dependent_age_ind: getPropertyPath(
    BASE_PATHS.PLAN_ELIGIBILITY,
    'dependent_age_ind'
  ),
  student_age: getPropertyPath(BASE_PATHS.PLAN_ELIGIBILITY, 'student_age'),
  student_age_ind: getPropertyPath(
    BASE_PATHS.PLAN_ELIGIBILITY,
    'student_age_ind'
  ),
  dependent_alt_groups_ind: getPropertyPath(
    BASE_PATHS.PLAN_ELIGIBILITY,
    'dependent_alt_groups_ind'
  ),

  // Transition fields
  fsa_ind: getPropertyPath(BASE_PATHS.PLAN_TRANSITION, 'fsa_ind'),
  fsa_substantiation_needed: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    'fsa_substantiation_needed'
  ),
  hra_ind: getPropertyPath(BASE_PATHS.PLAN_TRANSITION, 'hra_ind'),
  hra_substantiation_needed: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    'hra_substantiation_needed'
  ),
  hsa_ind: getPropertyPath(BASE_PATHS.PLAN_TRANSITION, 'hsa_ind'),
  hsa_substantiation_needed: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    'hsa_substantiation_needed'
  ),
};

/**
 * getClientConfigPath
 * Returns a single path for the given field.
 */
export function getClientConfigPath(field: keyof ClientConfig): string {
  return clientConfig[field];
}
