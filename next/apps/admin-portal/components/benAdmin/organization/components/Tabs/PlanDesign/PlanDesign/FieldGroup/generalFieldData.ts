import {
  OrganizationDetails,
  PlanDesignDetail,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';

import { getGeneralPaths } from '../Config/generalConfig';

export function getGeneralFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  // Early return if no plan designs available
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getGeneralPaths(selectedIndex);

  // Map through each detail and create field configurations
  return planDesignDetails
    .map((designDetail: PlanDesignDetail) => {
      // Skip if no design detail
      if (!designDetail) {
        return null;
      }

      // Return the field configurations for this detail
      return {
        subtitle: `General Information - ${planDesign?.name || ''}`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Benefit Name',
            value: planDesign?.name,
            name: planDesignConfig.name,
            type: 'input',
            placeholder: 'Enter Benefit Name',
          },
          {
            label: 'Benefit Order',
            value: planDesign.sort_seq,
            name: planDesignConfig.sort_seq,
            type: 'input',
            placeholder: 'Enter Benefit Order',
          },
          {
            label: 'Benefit Plan Type',
            value: planDesign.type_ind,
            name: planDesignConfig.type_ind,
            type: 'dropdownSelect',
            optionsMap: maps.planDesignTypeMap,
          },
          {
            label: 'Plan Design Benefit Period',
            value: designDetail.benefit_period_ind,
            name: planDesignConfig.benefit_period_ind,
            type: 'dropdownSelect',
            optionsMap: maps.benefitPeriodsMap,
          },
          {
            label: 'ESI BPLID',
            value: designDetail.esi_bplid,
            name: planDesignConfig.esi_bplid,
            type: 'input',
            placeholder: 'Enter ESI BPLID',
          },
          {
            label: 'Effective Date',
            value: designDetail.effective_date,
            name: planDesignConfig.effective_date,
            type: 'datepicker',
          },
          {
            label: 'End Date',
            value: designDetail.expiration_date,
            name: planDesignConfig.expiration_date,
            type: 'datepicker',
          },
          {
            label: 'Qualified HDHP',
            value: designDetail.qualified_hdhp_ind,
            name: planDesignConfig.qualified_hdhp_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Union Benefit',
            value: designDetail.union_benefit_ind,
            name: planDesignConfig.union_benefit_ind,
            type: 'dropdownSelect',
            optionsMap: maps.yesNoMap,
          },
          {
            label: 'Healthcare Reform Status',
            value: designDetail.healthcare_reform_status,
            name: planDesignConfig.healthcare_reform_status,
            type: 'dropdownSelect',
            optionsMap: maps.healthcareReformStatusMap,
          },
          {
            label: 'Non-Grandfathered Date',
            value: designDetail.non_grandfathered_date,
            name: planDesignConfig.non_grandfathered_date,
            type: 'datepicker',
          },
          {
            label: 'Overview Notes',
            value: designDetail.overview_notes,
            name: planDesignConfig.overview_notes,
            type: 'input',
            placeholder: 'Enter Overview Notes',
          },
          {
            label: 'Diabetic Meds and Supplies Copays',
            value: designDetail.diabetic_supplies_copay_setup_ind,
            name: planDesignConfig.diabetic_supplies_copay_setup_ind,
            type: 'dropdownSelect',
            optionsMap: maps.diabeticSuppliesCopayMap,
          },
          {
            label: 'Copays for Pre-Packaged Drugs',
            value: designDetail.pre_packaged_copays_ind,
            name: planDesignConfig.pre_packaged_copays_ind,
            type: 'dropdownSelect',
            optionsMap: maps.prepackagedCopaysMap,
          },
          {
            label: 'Diabetic Meds and Supplies Description',
            value: designDetail.diabetic_supplies_description,
            name: planDesignConfig.diabetic_supplies_description,
            type: 'input',
            placeholder: 'Enter description for diabetic meds and supplies',
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[];
}
