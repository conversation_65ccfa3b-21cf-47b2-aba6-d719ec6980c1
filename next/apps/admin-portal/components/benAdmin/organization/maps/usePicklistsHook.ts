/**
 * Hook for fetching picklists from the API
 */
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { usePathname } from 'next/navigation';

// API query cache durations
const CACHE_DURATIONS = {
  STALE_TIME: 5 * 60 * 1000, // 5 minutes
  CACHE_TIME: 10 * 60 * 1000, // 10 minutes
};

/**
 * Formats raw picklist data from API into a structured map
 *
 * @param arr - Array of picklist items from API
 * @returns A map of value/code to label/description
 */
const formatPicklist = (arr: any[]) =>
  arr?.reduce((acc, item) => {
    const value = String(item.value ?? item.id ?? item.code ?? '');
    const label = String(item.label ?? item.name ?? item.description ?? value);
    if (value) acc[value] = label;
    return acc;
  }, {} as Record<string, string>) || {};

/**
 * Hook for fetching and formatting picklists from the API
 *
 * @param picklistNames - Array of picklist names to fetch
 * @returns Object containing formatted picklists and query state
 */
export const useGlobalPicklists = (picklistNames: string[]) => {
  const { useApiQuery } = useBenAdmin();
  const pathname = usePathname();

  const shouldFetch = pathname?.includes('/ben-admin/');

  // Call the API query hook using the correct type structure
  const result = useApiQuery([
    {
      key: 'picklist',
      options: {
        enabled: shouldFetch,
        staleTime: CACHE_DURATIONS.STALE_TIME,
        cacheTime: CACHE_DURATIONS.CACHE_TIME,
      },
    },
  ]);

  // Format each picklist as a map
  const formatted: Record<string, Record<string, string>> = {};
  if (result?.picklist) {
    for (const name of picklistNames) {
      const arr = Array.isArray(result.picklist[name]?.[name])
        ? result.picklist[name][name]
        : Array.isArray(result.picklist[name])
        ? result.picklist[name]
        : [];
      formatted[name] = formatPicklist(arr);
    }
  }

  return {
    picklists: formatted,
    isLoading: result?.isLoading,
    isError: result?.isError,
    refetch: result?.refetch,
  };
};
