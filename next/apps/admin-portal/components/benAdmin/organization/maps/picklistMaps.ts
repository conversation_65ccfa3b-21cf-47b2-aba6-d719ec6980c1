import {
  CMK_PICKLISTS,
  ESI_PICKLISTS,
  MISC_PICKLISTS,
  OPT_PICKLISTS,
  PICKLISTS,
  PRODUCT_OPTIONS,
} from './picklistConstants';
import { useGlobalPicklists } from '../usePicklistsHook';

/**
 * Custom hook that returns all picklist maps for use throughout the application
 * This centralizes the picklist map creation to avoid duplicate code
 *
 * @returns An object containing all picklist maps and loading/error states
 */
export const usePicklistMaps = () => {
  // Request all picklists from all categories
  const allPicklistNames = [
    ...Object.values(PICKLISTS),
    ...Object.values(ESI_PICKLISTS),
    ...Object.values(OPT_PICKLISTS),
    ...Object.values(CMK_PICKLISTS),
    ...Object.values(PRODUCT_OPTIONS),
    ...Object.values(MISC_PICKLISTS),
  ];

  // Use the global picklists hook to fetch all picklists
  const { picklists } = useGlobalPicklists(allPicklistNames);
  console.log('>>>> PICKLISTS', picklists);

  // Create individual maps from the picklists response

  // General Picklists
  const yesNoMap = picklists[PICKLISTS.YES_NO] ?? {};
  const yesNoNaMap = picklists[PICKLISTS.YES_NO_NA] ?? {};
  const yesNoEsiMap = picklists[PICKLISTS.YES_NO_ESI] ?? {};
  const yesOtherAddNoteMap = picklists[PICKLISTS.YES_OTHER_ADD_NOTE] ?? {};
  const yesNoViewBenefitMap = picklists[PICKLISTS.YES_NO_VIEW_BENEFIT] ?? {};
  const activeMap = picklists[PICKLISTS.ACTIVE] ?? {};
  const activeInactiveMap = picklists[PICKLISTS.ACTIVE_INACTIVE] ?? {};
  const coverExcludeMap = picklists[PICKLISTS.COVER_EXCLUDE] ?? {};
  const successFailMap = picklists[PICKLISTS.SUCCESS_FAIL] ?? {};
  const excludeMap = picklists[PICKLISTS.EXCLUDE] ?? {};
  const studentAgeIndicatorMap =
    picklists[PICKLISTS.STUDENT_AGE_INDICATOR] ?? {};
  const dependentAgeIndicatorMap =
    picklists[PICKLISTS.DEPENDENT_AGE_INDICATOR] ?? {};

  // Pharmacy Related
  const pharmacyBillingMap = picklists[PICKLISTS.PHARMACY_BILLING] ?? {};
  const pharmacyPricingMap = picklists[PICKLISTS.PHARMACY_PRICING] ?? {};
  const pharmacyStatusMap = picklists[PICKLISTS.PHARMACY_STATUS] ?? {};
  const pharmacyOwnershipMap = picklists[PICKLISTS.PHARMACY_OWNERSHIP] ?? {};
  const pharmacyChannelMap = picklists[PICKLISTS.PHARMACY_CHANNEL] ?? {};
  const paperClaimsPricingMap = picklists[PICKLISTS.PAPER_CLAIMS_PRICING] ?? {};
  const prepackagedCopayMap = picklists[PICKLISTS.PREPACKAGED_COPAY] ?? {};
  const prepackagedCopaysMap = picklists[PICKLISTS.PREPACKAGED_COPAYS] ?? {};
  const diabeticSuppliesCopayMap =
    picklists[PICKLISTS.DIABETIC_SUPPLIES_COPAY] ?? {};
  const specialtyArrangementMap =
    picklists[PICKLISTS.SPECIALTY_ARRANGEMENT] ?? {};
  const dispenseAsWrittenMap = picklists[PICKLISTS.DISPENSE_AS_WRITTEN] ?? {};
  const networkStatusMap = picklists[PICKLISTS.NETWORK_STATUS] ?? {};
  const compoundCopayMap = picklists[PICKLISTS.COMPOUND_COPAY] ?? {};

  // Cost Share Related
  const costShareTierMap = picklists[PICKLISTS.COST_SHARE_TIER] ?? {};
  const costShareTypeMap = picklists[PICKLISTS.COST_SHARE_TYPE] ?? {};
  const costShareTierDaysSupplyMap =
    picklists[PICKLISTS.COST_SHARE_TIER_DAYS_SUPPLY] ?? {};
  const coInsuranceLesserGreaterMap =
    picklists[PICKLISTS.CO_INSURANCE_LESSER_GREATER] ?? {};

  // Business/Industry Related
  const lineOfBusinessMap = picklists[PICKLISTS.LINE_OF_BUSINESS] ?? {};
  const industryMap = picklists[PICKLISTS.INDUSTRY] ?? {};
  const industryVerticalMap = picklists[PICKLISTS.INDUSTRY_VERTICAL] ?? {};
  const terminationReasonMap = picklists[PICKLISTS.TERMINATION_REASON] ?? {};
  const legalEntityTypeMap = picklists[PICKLISTS.LEGAL_ENTITY_TYPE] ?? {};
  const stateMap = picklists[PICKLISTS.STATE] ?? {};
  const erisaMap = picklists[PICKLISTS.ERISA_STATUS] ?? {};

  // ID Card Related
  const idCardsResponsibleMap = picklists[PICKLISTS.ID_CARDS_RESPONSIBLE] ?? {};
  const idCardTypeMap = picklists[PICKLISTS.ID_CARD_TYPE] ?? {};
  const employeeIdSourceMap = picklists[PICKLISTS.EMPLOYEE_ID_SOURCE] ?? {};
  const idCardMailingMap = picklists[PICKLISTS.ID_CARD_MAILING] ?? {};
  const memberPacketsMap = picklists[PICKLISTS.MEMBER_PACKETS] ?? {};

  // Healthcare Related
  const healthcareReformStatusMap =
    picklists[PICKLISTS.HEALTHCARE_REFORM_STATUS] ?? {};
  const benefitPeriodsMap = picklists[PICKLISTS.BENEFIT_PERIODS] ?? {};
  const networkApplicabilityMap =
    picklists[PICKLISTS.NETWORK_APPLICABILITY] ?? {};
  const spendingAccountTypeMap =
    picklists[PICKLISTS.SPENDING_ACCOUNT_TYPE] ?? {};
  const hraMembersAccessMap = picklists[PICKLISTS.HRA_MEMBERS_ACCESS] ?? {};
  const planDesignTypeMap = picklists[PICKLISTS.PLAN_DESIGN_TYPE] ?? {};
  const accumTransferMap = picklists[PICKLISTS.ACCUM_TRANSFER] ?? {};
  const cobAllowedMap = picklists[PICKLISTS.COB_ALLOWED] ?? {};
  const medicaidSubrogationMap =
    picklists[PICKLISTS.MEDICAID_SUBROGATION] ?? {};
  const integratedMap = picklists[PICKLISTS.INTEGRATED] ?? {};
  const embeddedMap = picklists[PICKLISTS.EMBEDDED] ?? {};
  const carryoverPhaseMap = picklists[PICKLISTS.CARRYOVER_PHASE] ?? {};
  const penaltiesApplyMap = picklists[PICKLISTS.PENALTIES_APPLY] ?? {};
  const benefitPeriodLengthMap =
    picklists[PICKLISTS.BENEFIT_PERIOD_LENGTH] ?? {};
  const accumDrugListMap = picklists[PICKLISTS.ACCUM_DRUG_LIST] ?? {};

  // Document Related
  const documentInternalMap = picklists[PICKLISTS.DOCUMENT_INTERNAL] ?? {};
  const implementationTimelineMap =
    picklists[PICKLISTS.IMPLEMENTATION_TIMELINE] ?? {};

  // Expression/Validation Related
  const expressionContextMap = picklists[PICKLISTS.EXPRESSION_CONTEXT] ?? {};
  const expressionEntityScopeMap =
    picklists[PICKLISTS.EXPRESSION_ENTITY_SCOPE] ?? {};
  const planValidationRuleScopeMap =
    picklists[PICKLISTS.PLAN_VALIDATION_RULE_SCOPE] ?? {};
  const planValidationRuleSeverityMap =
    picklists[PICKLISTS.PLAN_VALIDATION_RULE_SEVERITY] ?? {};

  //Other
  const monthsMap = picklists[PICKLISTS.MONTHS] ?? {};
  const planClassMap = picklists[PICKLISTS.PLAN_CLASS] ?? {};
  const otherAccumTypeMap = picklists[PICKLISTS.OTHER_ACCUM_TYPE] ?? {};
  const cdhClassCodeMap = picklists[PICKLISTS.CDH_CLASS_CODE] ?? {};
  const priorAuthorizationReviewerMap =
    picklists[PICKLISTS.PRIOR_AUTHORIZATION_REVIEWER] ?? {};
  const pharmacyChannelAccumMap =
    picklists[PICKLISTS.PHARMACY_CHANNEL_ACCUM] ?? {};
  const formularyStatusMap = picklists[PICKLISTS.FORMULARY_STATUS] ?? {};
  const drugTypeStatusMap = picklists[PICKLISTS.DRUG_TYPE_STATUS] ?? {};
  const sharedIndicatorMap = picklists[PICKLISTS.SHARED_INDICATOR] ?? {};
  const medicalIntegrationTierMap =
    picklists[PICKLISTS.MEDICAL_INTEGRATION_TIER] ?? {};

  // ESI Specific Picklists
  const esiRetailPharmacyNetworkMap =
    picklists[ESI_PICKLISTS.RETAIL_PHARMACY_NETWORK] ?? {};
  const esiMaintenancePharmacyNetworkMap =
    picklists[ESI_PICKLISTS.MAINTENANCE_PHARMACY_NETWORK] ?? {};
  const esiSpecialtyPharmacyNetworkMap =
    picklists[ESI_PICKLISTS.SPECIALTY_PHARMACY_NETWORK] ?? {};
  const esiSpecialtyGraceFillsRetailMap =
    picklists[ESI_PICKLISTS.SPECIALTY_GRACE_FILLS_RETAIL] ?? {};
  const esiHomeDeliveryProgramMap =
    picklists[ESI_PICKLISTS.HOME_DELIVERY_PROGRAM] ?? {};
  const esiFormularyNameMap = picklists[ESI_PICKLISTS.FORMULARY_NAME] ?? {};
  const esiIrsHdhpPreventativeListMap =
    picklists[ESI_PICKLISTS.IRS_HDHP_PREVENTATIVE_LIST] ?? {};
  const esiUtilizationManagementBundleMap =
    picklists[ESI_PICKLISTS.UTILIZATION_MANAGEMENT_BUNDLE] ?? {};
  const esiDrugListMap = picklists[ESI_PICKLISTS.DRUG_LIST] ?? {};
  const esiCopayTierMap = picklists[ESI_PICKLISTS.COPAY_TIER] ?? {};
  const esiCopayChannelMap = picklists[ESI_PICKLISTS.COPAY_CHANNEL] ?? {};
  const esiCopayStructureMap = picklists[ESI_PICKLISTS.COPAY_STRUCTURE] ?? {};
  const esiCopayNetworkMap = picklists[ESI_PICKLISTS.COPAY_NETWORK] ?? {};
  const esiNinetyDayProgramMap =
    picklists[ESI_PICKLISTS.NINETY_DAY_PROGRAM] ?? {};
  const esiMandatorySpecialtyDaysSupplyMap =
    picklists[ESI_PICKLISTS.MANDATORY_SPECIALTY_DAYS_SUPPLY] ?? {};
  const esiMandatorySpecialtyFillLimitMap =
    picklists[ESI_PICKLISTS.MANDATORY_SPECIALTY_FILL_LIMIT] ?? {};
  const esiRetailDaysSupplyMap =
    picklists[ESI_PICKLISTS.RETAIL_DAYS_SUPPLY] ?? {};
  const esiHomeDeliveryFillLimitMap =
    picklists[ESI_PICKLISTS.HOME_DELIVERY_FILL_LIMIT] ?? {};
  const esiHomeDeliveryProgramEsiMap =
    picklists[ESI_PICKLISTS.HOME_DELIVERY_PROGRAM_ESI] ?? {};
  const esiPaperClaimsCoveredMap =
    picklists[ESI_PICKLISTS.PAPER_CLAIMS_COVERED] ?? {};
  const esiCompoundManagementProgramMap =
    picklists[ESI_PICKLISTS.COMPOUND_MANAGEMENT_PROGRAM] ?? {};
  const esiForeignClaimsMap = picklists[ESI_PICKLISTS.FOREIGN_CLAIMS] ?? {};
  const esiAccumPeriodMap = picklists[ESI_PICKLISTS.ACCUM_PERIOD] ?? {};
  const esiDependentAgeMap = picklists[ESI_PICKLISTS.DEPENDENT_AGE] ?? {};
  const esiInsulinMethodMap = picklists[ESI_PICKLISTS.INSULIN_METHOD] ?? {};
  const esiRetailNetworkMap = picklists[ESI_PICKLISTS.RETAIL_NETWORK] ?? {};
  const esiClaimSubmissionTypeMap =
    picklists[ESI_PICKLISTS.CLAIM_SUBMISSION_TYPE] ?? {};

  // OptumRx Specific Picklists
  const optRetailPharmacyNetworkMap =
    picklists[OPT_PICKLISTS.RETAIL_PHARMACY_NETWORK] ?? {};
  const optMaintenancePharmacyNetworkMap =
    picklists[OPT_PICKLISTS.MAINTENANCE_PHARMACY_NETWORK] ?? {};
  const optSpecialtyPharmacyNetworkMap =
    picklists[OPT_PICKLISTS.SPECIALTY_PHARMACY_NETWORK] ?? {};
  const optMandatoryMailProgramMap =
    picklists[OPT_PICKLISTS.MANDATORY_MAIL_PROGRAM] ?? {};
  const optHomeDeliveryProgramMap =
    picklists[OPT_PICKLISTS.HOME_DELIVERY_PROGRAM] ?? {};
  const optFormularyNameMap = picklists[OPT_PICKLISTS.FORMULARY_NAME] ?? {};
  const optAbortifacientsMap = picklists[OPT_PICKLISTS.ABORTIFACIENTS] ?? {};
  const optAcaContraceptivesMap =
    picklists[OPT_PICKLISTS.ACA_CONTRACEPTIVES] ?? {};
  const optIrsHdhpPreventativeListMap =
    picklists[OPT_PICKLISTS.IRS_HDHP_PREVENTATIVE_LIST] ?? {};
  const optUtilizationManagementBundleMap =
    picklists[OPT_PICKLISTS.UTILIZATION_MANAGEMENT_BUNDLE] ?? {};

  // Caremark Specific Picklists
  const cmkRetailPharmacyNetworkMap =
    picklists[CMK_PICKLISTS.RETAIL_PHARMACY_NETWORK] ?? {};
  const cmkMaintenancePharmacyNetworkMap =
    picklists[CMK_PICKLISTS.MAINTENANCE_PHARMACY_NETWORK] ?? {};
  const cmkSpecialtyPharmacyNetworkMap =
    picklists[CMK_PICKLISTS.SPECIALTY_PHARMACY_NETWORK] ?? {};
  const cmkFormularyNameMap = picklists[CMK_PICKLISTS.FORMULARY_NAME] ?? {};
  const cmkCoverBroaderVaccinationNetworkMap =
    picklists[CMK_PICKLISTS.COVER_BROADER_VACCINATION_NETWORK] ?? {};
  const cmkIrsHdhpPreventativeListMap =
    picklists[CMK_PICKLISTS.IRS_HDHP_PREVENTATIVE_LIST] ?? {};
  const cmkUtilizationManagementBundleMap =
    picklists[CMK_PICKLISTS.UTILIZATION_MANAGEMENT_BUNDLE] ?? {};
  const cmkStepTherapyOptionMap =
    picklists[CMK_PICKLISTS.STEP_THERAPY_OPTION] ?? {};

  // Product Options Picklists
  const mcapCmkMap = picklists[PRODUCT_OPTIONS.MCAP_CMK] ?? {};
  const mcapEsiMap = picklists[PRODUCT_OPTIONS.MCAP_ESI] ?? {};
  const mcapOptMap = picklists[PRODUCT_OPTIONS.MCAP_OPT] ?? {};
  const pvpEsiMap = picklists[PRODUCT_OPTIONS.PVP_ESI] ?? {};
  const internationalDrugSourcingMap =
    picklists[PRODUCT_OPTIONS.INTERNATIONAL_DRUG_SOURCING] ?? {};
  const optimizeMyCareMap = picklists[PRODUCT_OPTIONS.OPTIMIZE_MY_CARE] ?? {};
  const papMap = picklists[PRODUCT_OPTIONS.PAP] ?? {};
  const saveOnSpAdaptMap = picklists[PRODUCT_OPTIONS.SAVE_ON_SP_ADAPT] ?? {};

  // Miscellaneous Picklists
  const mandatoryMailGraceFillsMap =
    picklists[MISC_PICKLISTS.MANDATORY_MAIL_GRACE_FILLS] ?? {};
  const redListMap = picklists[MISC_PICKLISTS.RED_LIST] ?? {};

  return {
    // General Picklists
    yesNoMap,
    yesNoNaMap,
    yesNoEsiMap,
    yesOtherAddNoteMap,
    yesNoViewBenefitMap,
    activeMap,
    activeInactiveMap,
    coverExcludeMap,
    successFailMap,
    excludeMap,
    studentAgeIndicatorMap,
    dependentAgeIndicatorMap,

    // Pharmacy Related
    pharmacyBillingMap,
    pharmacyPricingMap,
    pharmacyStatusMap,
    pharmacyOwnershipMap,
    pharmacyChannelMap,
    paperClaimsPricingMap,
    prepackagedCopayMap,
    prepackagedCopaysMap,
    diabeticSuppliesCopayMap,
    specialtyArrangementMap,
    dispenseAsWrittenMap,
    networkStatusMap,
    compoundCopayMap,

    // Cost Share Related
    costShareTierMap,
    costShareTypeMap,
    costShareTierDaysSupplyMap,
    coInsuranceLesserGreaterMap,

    // Business/Industry Related
    lineOfBusinessMap,
    industryMap,
    industryVerticalMap,
    terminationReasonMap,
    legalEntityTypeMap,
    stateMap,
    erisaMap,

    // ID Card Related
    idCardsResponsibleMap,
    idCardTypeMap,
    employeeIdSourceMap,
    idCardMailingMap,
    memberPacketsMap,

    // Healthcare Related
    healthcareReformStatusMap,
    benefitPeriodsMap,
    networkApplicabilityMap,
    spendingAccountTypeMap,
    hraMembersAccessMap,
    planDesignTypeMap,
    accumTransferMap,
    cobAllowedMap,
    medicaidSubrogationMap,
    integratedMap,
    embeddedMap,
    carryoverPhaseMap,
    penaltiesApplyMap,
    benefitPeriodLengthMap,
    accumDrugListMap,

    // Document Related
    documentInternalMap,
    implementationTimelineMap,

    // Expression/Validation Related
    expressionContextMap,
    expressionEntityScopeMap,
    planValidationRuleScopeMap,
    planValidationRuleSeverityMap,

    //Other
    monthsMap,
    planClassMap,
    otherAccumTypeMap,
    cdhClassCodeMap,
    priorAuthorizationReviewerMap,
    pharmacyChannelAccumMap,
    formularyStatusMap,
    drugTypeStatusMap,
    sharedIndicatorMap,
    medicalIntegrationTierMap,

    // ESI Specific Picklists
    esiRetailPharmacyNetworkMap,
    esiMaintenancePharmacyNetworkMap,
    esiSpecialtyPharmacyNetworkMap,
    esiSpecialtyGraceFillsRetailMap,
    esiHomeDeliveryProgramMap,
    esiFormularyNameMap,
    esiIrsHdhpPreventativeListMap,
    esiUtilizationManagementBundleMap,
    esiDrugListMap,
    esiCopayTierMap,
    esiCopayChannelMap,
    esiCopayStructureMap,
    esiCopayNetworkMap,
    esiNinetyDayProgramMap,
    esiMandatorySpecialtyDaysSupplyMap,
    esiMandatorySpecialtyFillLimitMap,
    esiRetailDaysSupplyMap,
    esiHomeDeliveryFillLimitMap,
    esiHomeDeliveryProgramEsiMap,
    esiPaperClaimsCoveredMap,
    esiCompoundManagementProgramMap,
    esiForeignClaimsMap,
    esiAccumPeriodMap,
    esiDependentAgeMap,
    esiInsulinMethodMap,
    esiRetailNetworkMap,
    esiClaimSubmissionTypeMap,

    // OptumRx Specific Picklists
    optRetailPharmacyNetworkMap,
    optMaintenancePharmacyNetworkMap,
    optSpecialtyPharmacyNetworkMap,
    optMandatoryMailProgramMap,
    optHomeDeliveryProgramMap,
    optFormularyNameMap,
    optAbortifacientsMap,
    optAcaContraceptivesMap,
    optIrsHdhpPreventativeListMap,
    optUtilizationManagementBundleMap,

    // Caremark Specific Picklists
    cmkRetailPharmacyNetworkMap,
    cmkMaintenancePharmacyNetworkMap,
    cmkSpecialtyPharmacyNetworkMap,
    cmkFormularyNameMap,
    cmkCoverBroaderVaccinationNetworkMap,
    cmkIrsHdhpPreventativeListMap,
    cmkUtilizationManagementBundleMap,
    cmkStepTherapyOptionMap,

    // Product Options Picklists
    mcapCmkMap,
    mcapEsiMap,
    mcapOptMap,
    pvpEsiMap,
    internationalDrugSourcingMap,
    optimizeMyCareMap,
    papMap,
    saveOnSpAdaptMap,

    // Miscellaneous Picklists
    mandatoryMailGraceFillsMap,
    redListMap,
  };
};
