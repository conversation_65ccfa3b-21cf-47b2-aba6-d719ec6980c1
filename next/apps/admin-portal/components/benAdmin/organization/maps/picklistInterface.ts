// picklistTypes.ts

interface PicklistItem {
  code: string;
  description: string;
  [key: string]: any; // For any additional properties
}

export type PicklistMap = {
  [code: string]: string | PicklistItem;
};

/**
 * Interface defining the return type of usePicklistMaps hook
 */
export interface PicklistMaps {
  // General Picklists
  yesNoMap: PicklistMap;
  yesNoNaMap: PicklistMap;
  yesNoEsiMap: PicklistMap;
  yesOtherAddNoteMap: PicklistMap;
  yesNoViewBenefitMap: PicklistMap;
  activeMap: PicklistMap;
  activeInactiveMap: PicklistMap;
  coverExcludeMap: PicklistMap;
  successFailMap: PicklistMap;
  excludeMap: PicklistMap;
  studentAgeIndicatorMap: PicklistMap;
  dependentAgeIndicatorMap: PicklistMap;

  // Pharmacy Related
  pharmacyBillingMap: PicklistMap;
  pharmacyPricingMap: PicklistMap;
  pharmacyStatusMap: PicklistMap;
  pharmacyOwnershipMap: PicklistMap;
  pharmacyChannelMap: PicklistMap;
  pharmacyChannelAccumMap: PicklistMap;
  paperClaimsPricingMap: PicklistMap;
  prepackagedCopayMap: PicklistMap;
  prepackagedCopaysMap: PicklistMap;
  diabeticSuppliesCopayMap: PicklistMap;
  specialtyArrangementMap: PicklistMap;
  dispenseAsWrittenMap: PicklistMap;
  compoundCopayMap: PicklistMap;

  // Cost Share Related
  costShareTierMap: PicklistMap;
  costShareTypeMap: PicklistMap;
  costShareTierDaysSupplyMap: PicklistMap;
  coInsuranceLesserGreaterMap: PicklistMap;

  // Business/Industry Related
  lineOfBusinessMap: PicklistMap;
  industryMap: PicklistMap;
  industryVerticalMap: PicklistMap;
  terminationReasonMap: PicklistMap;
  legalEntityTypeMap: PicklistMap;
  stateMap: PicklistMap;
  erisaMap: PicklistMap;

  // ID Card Related
  idCardsResponsibleMap: PicklistMap;
  idCardTypeMap: PicklistMap;
  employeeIdSourceMap: PicklistMap;
  idCardMailingMap: PicklistMap;
  memberPacketsMap: PicklistMap;

  // Healthcare Related
  healthcareReformStatusMap: PicklistMap;
  benefitPeriodsMap: PicklistMap;
  networkApplicabilityMap: PicklistMap;
  spendingAccountTypeMap: PicklistMap;
  hraMembersAccessMap: PicklistMap;
  planDesignTypeMap: PicklistMap;
  accumTransferMap: PicklistMap;
  cobAllowedMap: PicklistMap;
  medicaidSubrogationMap: PicklistMap;
  integratedMap: PicklistMap;
  embeddedMap: PicklistMap;
  carryoverPhaseMap: PicklistMap;
  penaltiesApplyMap: PicklistMap;
  benefitPeriodLengthMap: PicklistMap;
  accumDrugListMap: PicklistMap;

  // Document Related
  documentInternalMap: PicklistMap;
  implementationTimelineMap: PicklistMap;

  // Expression/Validation Related
  expressionContextMap: PicklistMap;
  expressionEntityScopeMap: PicklistMap;
  planValidationRuleScopeMap: PicklistMap;
  planValidationRuleSeverityMap: PicklistMap;

  // Drug and Formulary Related
  drugTypeStatusMap: PicklistMap;
  formularyStatusMap: PicklistMap;
  foreignClaimsMap: PicklistMap;

  // Medical Integration
  medicalIntegrationTierMap: PicklistMap;
  medicalVendorMap: PicklistMap;

  // System/Internal
  cdhClassCodeMap: PicklistMap;
  sharedIndicatorMap: PicklistMap;

  // Other
  monthsMap: PicklistMap;
  otherAccumTypeMap: PicklistMap;
  planClassMap: PicklistMap;
  priorAuthorizationReviewerMap: PicklistMap;

  // ESI Specific Picklists
  esiRetailPharmacyNetworkMap: PicklistMap;
  esiMaintenancePharmacyNetworkMap: PicklistMap;
  esiSpecialtyPharmacyNetworkMap: PicklistMap;
  esiSpecialtyGraceFillsRetailMap: PicklistMap;
  esiHomeDeliveryProgramMap: PicklistMap;
  esiFormularyNameMap: PicklistMap;
  esiIrsHdhpPreventativeListMap: PicklistMap;
  esiUtilizationManagementBundleMap: PicklistMap;
  esiDrugListMap: PicklistMap;
  esiCopayTierMap: PicklistMap;
  esiCopayChannelMap: PicklistMap;
  esiCopayStructureMap: PicklistMap;
  esiCopayNetworkMap: PicklistMap;
  esiNinetyDayProgramMap: PicklistMap;
  esiMandatorySpecialtyDaysSupplyMap: PicklistMap;
  esiMandatorySpecialtyFillLimitMap: PicklistMap;
  esiRetailDaysSupplyMap: PicklistMap;
  esiHomeDeliveryFillLimitMap: PicklistMap;
  esiHomeDeliveryProgramEsiMap: PicklistMap;
  esiPaperClaimsCoveredMap: PicklistMap;
  esiCompoundManagementProgramMap: PicklistMap;
  esiForeignClaimsMap: PicklistMap;
  esiAccumPeriodMap: PicklistMap;
  esiDependentAgeMap: PicklistMap;
  esiInsulinMethodMap: PicklistMap;
  esiRetailNetworkMap: PicklistMap;
  esiClaimSubmissionTypeMap: PicklistMap;

  // OptumRx Specific Picklists
  optRetailPharmacyNetworkMap: PicklistMap;
  optMaintenancePharmacyNetworkMap: PicklistMap;
  optSpecialtyPharmacyNetworkMap: PicklistMap;
  optMandatoryMailProgramMap: PicklistMap;
  optHomeDeliveryProgramMap: PicklistMap;
  optFormularyNameMap: PicklistMap;
  optAbortifacientsMap: PicklistMap;
  optAcaContraceptivesMap: PicklistMap;
  optIrsHdhpPreventativeListMap: PicklistMap;
  optUtilizationManagementBundleMap: PicklistMap;

  // Caremark Specific Picklists
  cmkRetailPharmacyNetworkMap: PicklistMap;
  cmkMaintenancePharmacyNetworkMap: PicklistMap;
  cmkSpecialtyPharmacyNetworkMap: PicklistMap;
  cmkFormularyNameMap: PicklistMap;
  cmkCoverBroaderVaccinationNetworkMap: PicklistMap;
  cmkIrsHdhpPreventativeListMap: PicklistMap;
  cmkUtilizationManagementBundleMap: PicklistMap;
  cmkStepTherapyOptionMap: PicklistMap;

  // Product Options Picklists
  mcapCmkMap: PicklistMap;
  mcapEsiMap: PicklistMap;
  mcapOptMap: PicklistMap;
  pvpEsiMap: PicklistMap;
  internationalDrugSourcingMap: PicklistMap;
  optimizeMyCareMap: PicklistMap;
  papMap: PicklistMap;
  saveOnSpAdaptMap: PicklistMap;

  // Miscellaneous Picklists
  mandatoryMailGraceFillsMap: PicklistMap;
  redListMap: PicklistMap;
}
