import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

/**
 * Custom hook to determine the Pharmacy Benefit Manager (PBM) type based on the product name.
 * @param formMethods The form methods object from react-hook-form
 * @param productNamePath Optional path to the product name field (defaults to 'plan.product.vendor.pbm.legal_entity.name')
 * @returns The PBM type ('IsESI', 'IsOptum', 'IsCMK', or undefined if no match)
 */
export function usePBMType(
  formMethods: UseFormReturn<any>
): string | undefined {
  // Watch the product name field, falling back to alternative path
  const productName =
    formMethods.getValues('plan.product.vendor.pbm.legal_entity.name') ||
    formMethods.getValues('plan.product.vendor.name');

  // Determine the PBM type based on product name
  const pbmType = useMemo(() => {
    if (typeof productName !== 'string') {
      return undefined;
    }

    if (
      productName.includes('Express Scripts') ||
      productName.includes('ESI')
    ) {
      return 'IsESI';
    }
    if (productName.includes('Optum')) {
      return 'IsOptum';
    }
    if (productName.includes('Caremark') || productName.includes('CMK')) {
      return 'IsCMK';
    }
    return undefined;
  }, [productName]);

  return pbmType;
}
