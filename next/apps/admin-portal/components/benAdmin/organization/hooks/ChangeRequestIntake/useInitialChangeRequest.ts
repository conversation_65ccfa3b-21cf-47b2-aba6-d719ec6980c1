'use client';

import { usePathname } from 'next/navigation';
import { useMemo, useRef } from 'react';

import { ChangeRequest } from '../../../Models/interfaces';
import { useOrganizationApi } from '../useOrganizationViewApis';

export const useInitialChangeRequest = (): {
  initialChangeRequest: any | null;
  changeRequestId: string | undefined;
} => {
  // Get orgId and changeRequestId from URL using usePathname (client-only)
  const pathname = usePathname() || '';
  const segments = pathname.split('/').filter(Boolean);
  const orgId = segments[2]; // "162"
  const changeRequestId = segments[4]; // "267"

  const { fetchChangeRequestById } = useOrganizationApi(
    orgId as string,
    changeRequestId as string
  );

  // Use a ref to ensure session storage is updated only once per new fetched data.
  const updatedRef = useRef(false);

  // Compute and memoize the initial change request value.
  const initialChangeRequest = useMemo(() => {
    if (fetchChangeRequestById) {
      // Check if fetchChangeRequestById is an array. If so, use the first element.
      const cr = fetchChangeRequestById as ChangeRequest;
      if (!updatedRef.current) {
        sessionStorage.setItem('selectedChangeRequest', JSON.stringify(cr));
        updatedRef.current = true;
      }
      return cr.change_content;
    }
    return null;
  }, [fetchChangeRequestById]);

  return {
    initialChangeRequest,
    changeRequestId,
  };
};
