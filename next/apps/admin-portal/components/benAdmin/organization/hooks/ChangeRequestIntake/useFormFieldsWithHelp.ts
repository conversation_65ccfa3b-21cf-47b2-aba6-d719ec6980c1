import { HelpText } from '../../../Models/interfaces';
import { defineFormField } from '../../../ReusableComponents/Components/Form/components/hooks/formHelpers';

export const useFormFieldsWithHelp = (helpText: HelpText | undefined) => {
  const getHelpTextByFieldPath = (fieldPath: string) => {
    // help_text API uses lowest-level path
    const lowestLevelPath = fieldPath.split('.').slice(-2).join('.');
    return helpText?.help.fields.find(
      (field) => lowestLevelPath === field.field_name
    )?.help_text;
  };

  // Wrapper function that automatically includes help text
  const defineFormFieldWithHelp = (
    label: string,
    type: any,
    name: string,
    value: any,
    options: any = {}
  ) => {
    return defineFormField(label, type, name, value, {
      ...options,
      infoText: getHelpTextByFieldPath(name) || options.infoText,
    });
  };

  return {
    getHelpTextByFieldPath,
    defineFormFieldWithHelp,
  };
};
