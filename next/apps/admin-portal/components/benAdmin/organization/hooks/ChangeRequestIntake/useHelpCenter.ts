import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { useParams } from 'next/navigation';

import { getUIContextFromNavigationConstant } from '../../components/ChangeRequestIntake/Navigation/uiContextEnum';

export const useHelpCenter = (navigationConstant: string) => {
  const params = useParams();
  const { useApiQuery } = useBenAdmin();
  const {
    helpText: helpCenterData,
    isError,
    isFetching,
    isLoading,
  } = useApiQuery([
    {
      key: 'helpText',
      pathParams: { id: params?.changeRequestId },
      queryParams: {
        ui_context_ind: getUIContextFromNavigationConstant(navigationConstant),
      },
    },
  ]);

  return {
    helpCenterData,
    isError,
    isFetching,
    isLoading,
  };
};
