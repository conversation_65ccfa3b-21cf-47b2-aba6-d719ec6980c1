import { useToast } from '@chakra-ui/react';
import { useCallback, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FieldValidations } from '../../..';
import { filterByPlanDesignField } from '../../../ReusableComponents/Components/SideNavBar/filterUtils';
import { getUIContextFromNavigationConstant } from '../../components/ChangeRequestIntake/Navigation/uiContextEnum';
import { useSaveChangeRequestHandler } from '../useOrganizationHooks';

interface UseContinueHandlerProps {
  refetch: () => Promise<any>;
  onUpdateActiveItem?: (id: string) => void;
  formMethods: UseFormReturn<any>;
}

export const useContinueHandler = ({
  refetch,
  onUpdateActiveItem,
  formMethods,
}: UseContinueHandlerProps) => {
  // Call the hook at the top level
  const saveHandler = useSaveChangeRequestHandler(formMethods, false, true);
  const toast = useToast();
  // Track if warning has been acknowledged for the current page/index
  const warningAcknowledgedRef = useRef<{ [key: string]: boolean }>({});

  const createContinueHandler = useCallback(
    (page: string, navigateTo: string) => {
      return async () => {
        // Save current form data before validation
        const currentValues = formMethods.getValues();
        await saveHandler(currentValues);

        try {
          // Force a fresh validation fetch (bypasses cache and gets latest data from API)
          // This is the ONLY time validation is refetched after the initial page load
          const result = await refetch();
          // Wait a tick for state to update
          await new Promise((resolve) => setTimeout(resolve, 100));

          // Get the UI context index from the page constant
          const uiContextInd = getUIContextFromNavigationConstant(page);

          if (uiContextInd === undefined) {
            console.error(`Could not find UI context for page: ${page}`);
            return;
          }

          // Check if we have validation errors and warnings after refetch
          const pageErrors =
            result?.data?.results?.[uiContextInd]?.errors || [];
          const pageWarnings =
            result?.data?.results?.[uiContextInd]?.warnings || [];
          const filteredErrors = filterByPlanDesignField(
            pageErrors
          ) as FieldValidations[];
          const filteredWarnings = filterByPlanDesignField(
            pageWarnings
          ) as FieldValidations[];
          const hasErrors = filteredErrors.length > 0;

          // Key for tracking warning acknowledgement per page/index
          const warningKey = `${page}-${uiContextInd}`;

          if (filteredWarnings.length > 0) {
            if (!warningAcknowledgedRef.current[warningKey]) {
              toast({
                title: 'Warning',
                description: filteredWarnings[0].message,
                status: 'warning',
                duration: 5000,
                isClosable: true,
              });
              warningAcknowledgedRef.current[warningKey] = true;
              return; // Do not navigate yet
            }
          } else {
            // Reset acknowledgement if no warnings
            warningAcknowledgedRef.current[warningKey] = false;
          }

          if (!hasErrors && onUpdateActiveItem) {
            onUpdateActiveItem(navigateTo);
          }
        } catch (error) {
          console.error('Error during refetch:', error);
        }
      };
    },
    [refetch, onUpdateActiveItem, saveHandler, formMethods, toast]
  );

  return { createContinueHandler };
};
