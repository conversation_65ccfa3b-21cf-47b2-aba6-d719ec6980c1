import { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { getUIContextFromNavigationConstant } from '../../components/ChangeRequestIntake/Navigation/uiContextEnum';
import { useSaveChangeRequestHandler } from '../useOrganizationHooks';

interface UseContinueHandlerProps {
  refetch: () => Promise<any>;
  onUpdateActiveItem?: (id: string) => void;
  formMethods: UseFormReturn<any>;
}

export const useContinueHandler = ({
  refetch,
  onUpdateActiveItem,
  formMethods,
}: UseContinueHandlerProps) => {
  // Call the hook at the top level
  const saveHandler = useSaveChangeRequestHandler(formMethods, false, true);

  const createContinueHandler = useCallback(
    (page: string, navigateTo: string) => {
      return async () => {
        // Save current form data before validation
        const currentValues = formMethods.getValues();
        await saveHandler(currentValues);

        try {
          // Force a fresh validation fetch (bypasses cache and gets latest data from API)
          // This is the ONLY time validation is refetched after the initial page load
          const result = await refetch();
          // Wait a tick for state to update
          await new Promise((resolve) => setTimeout(resolve, 100));

          // Get the UI context index from the page constant
          const uiContextInd = getUIContextFromNavigationConstant(page);

          if (uiContextInd === undefined) {
            console.error(`Could not find UI context for page: ${page}`);
            return;
          }

          // Check if we have validation errors after refetch
          const validationErrors =
            result?.data?.results?.[uiContextInd]?.errors || [];
          const hasErrors = validationErrors.length > 0;

          if (!hasErrors && onUpdateActiveItem) {
            onUpdateActiveItem(navigateTo);
          }
        } catch (error) {
          console.error('Error during refetch:', error);
        }
      };
    },
    [refetch, onUpdateActiveItem, saveHandler, formMethods]
  );

  return { createContinueHandler };
};
