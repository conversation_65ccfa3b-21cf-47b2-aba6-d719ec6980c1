import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { useMemo } from 'react';

import {
  ValidateByPageReturn,
  ValidationResponse,
} from '../../Models/interfaces';
import { getUIContextFromNavigationConstant } from '../components/ChangeRequestIntake/Navigation/uiContextEnum';

export const useValidate = (
  changeRequestId: any,
  page?: any,
  options: {
    fetchOnMount?: boolean;
    staleTime?: number;
    cacheTime?: number;
  } = {}
): ValidateByPageReturn => {
  const { useApiQuery } = useBenAdmin();

  const {
    fetchOnMount = true,
    staleTime = 5 * 60 * 1000, // 5 minutes default cache
    cacheTime = 10 * 60 * 1000, // 10 minutes default cache
  } = options;

  const uiContextInd = useMemo(() => {
    return getUIContextFromNavigationConstant(page);
  }, [page]);

  const { validate, isError, isLoading, isFetching, refetch } = useApiQuery([
    {
      key: 'validate',
      pathParams: { id: changeRequestId },
      queryParams: uiContextInd ? { ui_context_ind: String(uiContextInd) } : {},
      options: {
        enabled: !!changeRequestId && fetchOnMount,
        staleTime, // Use configurable cache time
        cacheTime, // Use configurable cache time
        refetchOnWindowFocus: false, // Don't refetch on window focus
        refetchOnMount: fetchOnMount, // Only refetch on mount if specified
      },
    },
  ]);

  return {
    validationData: validate as ValidationResponse | null,
    isError: isError,
    isLoading: isLoading || isFetching, // Use both loading states
    refetch: refetch.validate,
  };
};
