import { getIndexFromURL } from './parameterUtils';

/**
 * Filters an array of items (with a 'field' property) to only include those
 * that match the current plan design index from the URL.
 * If the item does not have a 'field' property, returns the item (for ValidationRule[]).
 */

export function filterByPlanDesignField<T extends { field: string }>(
  items: T[]
): T[] {
  const index = getIndexFromURL();
  if (index === undefined) return items;
  const pattern = new RegExp(`plan_designs(?:\\.|\\[)${index}(?:\\]|\\.)`);
  return items.filter((item) => pattern.test(item.field));
}
