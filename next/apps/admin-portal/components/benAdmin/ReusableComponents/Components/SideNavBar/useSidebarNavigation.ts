import { usePathname, useRouter } from 'next/navigation';
import {
  MutableRefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import {
  NavigationConfig,
  NavigationState,
  SidebarItem,
} from '../../Models/types';
import { applyParamConfig } from './parameterUtils';

export const useNavigation = (configs: NavigationConfig) => {
  const { baseUrl, organizationDetails, formMethods, modes, defaultMode } =
    configs;

  const pathname = usePathname();
  const router = useRouter();

  const navigateToRef: MutableRefObject<
    ((mode: string, itemId?: string) => void) | null
  > = useRef(null);

  // Enhanced URL update function with configurable parameter preservation
  const updateURL = useCallback(
    (mode: string, itemId: string) => {
      // Get the current URL and parameters
      const currentUrl = new URL(window.location.href);
      const currentParams = new URLSearchParams(currentUrl.search);

      // Get parameter configuration for the target mode
      const modeConfig = modes[mode];
      const paramConfig = modeConfig?.urlParams;

      // Apply parameter configuration to extract parameters to preserve
      const preservedParams = applyParamConfig(currentParams, paramConfig);

      // Create new URLSearchParams with navigation parameters
      const newParams = new URLSearchParams();

      // Add preserved parameters first
      preservedParams.forEach((value, key) => {
        newParams.set(key, value);
      });

      // Set navigation parameters based on mode
      if (mode === defaultMode) {
        if (itemId !== 'overview') {
          newParams.set('tab', itemId);
        }
      } else {
        newParams.set('section', mode);
        newParams.set('tab', itemId);
      }

      // Build new URL string
      const queryString = newParams.toString();
      const newUrl = `${baseUrl}${queryString ? `?${queryString}` : ''}`;

      // Update browser history
      window.history.replaceState({ mode, activeItem: itemId }, '', newUrl);
    },
    [baseUrl, defaultMode, modes]
  );

  const findItemComponentById = useCallback(
    (mode: string, itemId: string) => {
      const modeConfig = modes[mode];
      if (!modeConfig) {
        console.error(`Mode not found: ${mode}`);
        return null;
      }
      const sidebarConfig = modeConfig.getSidebarConfig(
        organizationDetails,
        formMethods,
        (section, tab) => navigateToRef.current?.(section, tab),
        itemId
      );

      const searchItems = (items: SidebarItem[]): SidebarItem | null => {
        for (const item of items) {
          if (item.id === itemId && item.component) {
            return item;
          }
          if (item.hasDropdown && item.dropdownItems) {
            const nestedItem = searchItems(item.dropdownItems);
            if (nestedItem) return nestedItem;
          }
        }
        return null;
      };

      for (const section of sidebarConfig.sections) {
        const foundItem = searchItems(section.items);
        if (foundItem) return foundItem;
      }
      return null;
    },
    [modes, organizationDetails, formMethods]
  );

  const navigateTo = useCallback(
    (section: string, tab?: string) => {
      const modeConfig = configs.modes[section];
      if (!modeConfig) {
        console.error(`Section not found: ${section}`);
        return;
      }
      const targetTab = tab || modeConfig.defaultItemId;
      if (!targetTab) {
        console.error(
          `No tab specified and no default tab for section: ${section}`
        );
        return;
      }
      const foundItem = findItemComponentById(section, targetTab);
      if (!foundItem) {
        console.error(`Item not found: ${targetTab} in section: ${section}`);
        return;
      }

      setNavState({
        currentMode: section,
        activeItem: targetTab,
        currentComponent: foundItem.component,
      });
      updateURL(section, targetTab);
    },
    [configs, findItemComponentById, updateURL]
  );

  useEffect(() => {
    navigateToRef.current = navigateTo;
  }, [navigateTo]);

  const getStateFromUrl = useCallback((): NavigationState => {
    const url = new URL(window.location.href);
    const section = url.searchParams.get('section');
    const tab = url.searchParams.get('tab');
    const mode = section || defaultMode;
    const modeConfig = modes[mode] || modes[defaultMode];
    const activeItem = tab || modeConfig.defaultItemId;
    const foundItem = findItemComponentById(mode, activeItem);
    return {
      currentMode: mode,
      activeItem,
      currentComponent: foundItem?.component || null,
    };
  }, [defaultMode, modes, findItemComponentById]);

  const [navState, setNavState] = useState<NavigationState>(() =>
    getStateFromUrl()
  );

  useEffect(() => {
    if (!navState.currentComponent) {
      const defaultModeConfig = modes[defaultMode];
      const defaultItem = findItemComponentById(
        defaultMode,
        defaultModeConfig.defaultItemId
      );
      if (defaultItem) {
        setNavState({
          currentMode: defaultMode,
          activeItem: defaultModeConfig.defaultItemId,
          currentComponent: defaultItem.component,
        });
        updateURL(defaultMode, defaultModeConfig.defaultItemId);
      } else {
        console.error(`Default item not found for mode: ${defaultMode}`);
      }
    }
  }, [
    navState.currentComponent,
    defaultMode,
    modes,
    findItemComponentById,
    updateURL,
  ]);

  useEffect(() => {
    const newState = getStateFromUrl();
    if (
      newState.currentMode !== navState.currentMode ||
      newState.activeItem !== navState.activeItem
    ) {
      setNavState(newState);
    }
  }, [pathname, getStateFromUrl, navState.currentMode, navState.activeItem]);

  useEffect(() => {
    const handlePopState = () => {
      setNavState(getStateFromUrl());
    };
    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [getStateFromUrl]);

  const handleSelectItem = useCallback(
    (item: SidebarItem) => {
      if (item.modeChange) {
        const targetMode = item.modeChange;
        const targetModeConfig = modes[targetMode];
        if (targetModeConfig) {
          navigateTo(targetMode, targetModeConfig.defaultItemId);
        }
      } else {
        navigateTo(navState.currentMode, item.id);
      }
    },
    [modes, navigateTo, navState.currentMode]
  );

  const handleBreadcrumbNavigation = useCallback(
    (id: string) => {
      if (modes[id]) {
        const modeConfig = modes[id];
        navigateTo(id, modeConfig.defaultItemId);
      } else if (id === 'overview') {
        navigateTo(defaultMode, 'overview');
      } else if (id === 'dashboard') {
        // Dashboard navigation - no parameter preservation
        const dashboardUrl = `/ben-admin/organization/${organizationDetails?.organization?.organization_id}`;
        router.push(dashboardUrl);
      } else {
        navigateTo(navState.currentMode, id);
      }
    },
    [
      modes,
      defaultMode,
      navigateTo,
      router,
      organizationDetails,
      navState.currentMode,
    ]
  );

  const sidebarConfig = useMemo(() => {
    const modeConfig = modes[navState.currentMode];
    if (!modeConfig) return null;
    return modeConfig.getSidebarConfig(
      organizationDetails,
      formMethods,
      (section, tab) => navigateToRef.current?.(section, tab),
      navState.activeItem
    );
  }, [
    modes,
    navState.currentMode,
    navState.activeItem,
    organizationDetails,
    formMethods,
  ]);

  return {
    ...navState,
    sidebarConfig,
    handleSelectItem,
    handleBreadcrumbNavigation,
    navigateTo: (section: string, tab?: string) => {
      navigateToRef.current?.(section, tab);
    },
  };
};
