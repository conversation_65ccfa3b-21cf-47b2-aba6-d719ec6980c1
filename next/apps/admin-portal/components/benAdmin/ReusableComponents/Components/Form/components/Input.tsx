import {
  Box,
  FormControl,
  FormLabel,
  Input,
  InputGroup,
  InputLeftElement,
  VStack,
} from '@chakra-ui/react';
import React, { forwardRef } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FieldError } from '../../InlineFieldIcons';
import { AncillaryToggleAccordion } from './AncillaryToggleAccordion';
import { CircularQuestionIcon } from './sharables/CircularQuestionIcon';
import ToggleWithAccordion from './ToggleWithAccordion';

interface ReusableInputProps {
  label?: string;
  placeholder: string;
  isRequired?: boolean;
  infoText?: string;
  value?: string | number;
  error?: boolean;
  onChange?: (value: string | number) => void;
  onBlur?: () => void;
  ref?: React.Ref<HTMLInputElement>;
  // Add type property for input type (important for number validation)
  type?: React.HTMLInputTypeAttribute;
  isDisabled?: boolean;
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleValue?: boolean;
  onToggleChange?: (isChecked: boolean) => void;
  formMethods: UseFormReturn<any>;
  formPath?: string;
  sync?: boolean;
  name: string;
  idMap?: {
    key: string;
    value: number;
  } | null;
  syncFunction?: (
    sync: boolean,
    toggleValue: boolean,
    name: string,
    value: string | number,
    formMethods: UseFormReturn<any>
  ) => void;
}

const ReusableInput = forwardRef<HTMLInputElement, ReusableInputProps>(
  (
    {
      label,
      placeholder,
      isRequired = false,
      infoText,
      value = '',
      error,
      onChange,
      onBlur,
      type = 'text',
      isDisabled = false,
      showAncillaryToggle = false,
      showToggle = false,
      toggleLabel = 'Differs from Plan Design',
      toggleName,
      toggleValue = false,
      onToggleChange,
      formMethods,
      formPath,
      sync = false,
      name,
      idMap,
      syncFunction,
    },
    ref
  ) => {
    // Format value appropriately
    const displayValue = value === null ? '' : value;

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      // Update the original input value
      onChange?.(newValue);

      // If sync is enabled and toggle is not active, sync across all plan designs
      if (syncFunction) {
        syncFunction(sync, toggleValue, name, newValue, formMethods);
      }
    };

    const handleToggleChange = (isChecked: boolean) => {
      if (onToggleChange) {
        onToggleChange(isChecked);

        // If toggle is turned on, clear the input value
        if (isChecked) {
          onChange?.('');
        }
      }
    };

    return (
      <FormControl isRequired={isRequired}>
        <VStack spacing={2} align="stretch" width="100%">
          <Box>
            <Box display="flex" alignItems="flex-start" mb={1}>
              {label && (
                <FormLabel color="#676767" mb={0}>
                  {label}
                </FormLabel>
              )}
              {infoText && (
                <CircularQuestionIcon tooltiptext={infoText} ml={-2} />
              )}
            </Box>
            <InputGroup>
              {error && (
                <InputLeftElement pointerEvents="none">
                  {FieldError()}
                </InputLeftElement>
              )}
              <Input
                pl={error ? '32px' : ''}
                ref={ref}
                placeholder={placeholder}
                value={toggleValue ? '' : displayValue}
                type={type}
                onChange={handleInputChange}
                onBlur={onBlur}
                isDisabled={isDisabled || toggleValue}
                isInvalid={error}
                errorBorderColor="red.500"
                borderColor={error ? 'red.500' : 'gray.300'}
              />
            </InputGroup>
          </Box>

          {/* Render toggle with accordion if showToggle is true */}

          {showAncillaryToggle && (
            <AncillaryToggleAccordion
              label={toggleLabel}
              isChecked={toggleValue}
              onChange={handleToggleChange}
              name={toggleName}
              formMethods={formMethods}
              formPath={formPath}
            />
          )}

          {showToggle && (
            <ToggleWithAccordion
              label={toggleLabel}
              isChecked={toggleValue}
              onChange={handleToggleChange}
              name={toggleName}
              formMethods={formMethods}
              formPath={formPath}
              idMap={idMap}
            />
          )}
        </VStack>
      </FormControl>
    );
  }
);

ReusableInput.displayName = 'ReusableInput';

export default ReusableInput;
