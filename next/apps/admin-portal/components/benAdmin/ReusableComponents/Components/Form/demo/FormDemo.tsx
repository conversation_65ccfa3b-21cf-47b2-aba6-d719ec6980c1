import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { clientConfig } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/Config/clientConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import { z } from 'zod';

import { SubCategoryType } from '../../../Models/types';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from '../components/hooks/formHelpers';

/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useFormDemo(org: Partial<OrganizationDetails>) {
  const { erisaMap, monthsMap, planClassMap, benefitPeriodsMap } =
    usePicklistMaps();

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Legal Name',
          'input',
          clientConfig.legal_entity_name,
          org?.organization?.legal_entity_name,
          {
            infoText: 'Please enter name of Organization',
            placeholder: 'Enter legal name',
            isRequired: true,
            validations: z
              .string()
              .max(255, 'Organization name must be at least 5 characters'),
          }
        ),
        defineFormField(
          'Employer Address',
          'input',
          clientConfig.address_line_1,
          org?.organization?.address?.address_line_1,
          {
            placeholder: 'Enter employer address',
            isRequired: true,
            infoText: 'Employer Address',
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Period',
          'dropdownSelect',
          clientConfig.benefit_period_ind,
          org?.plan?.plan_designs?.[0]?.plan_design_details?.[0]
            .benefit_period_ind,
          {
            optionsMap: benefitPeriodsMap,
            isRequired: true,
            infoText: 'Select one or more options',
          }
        ),
        defineFormField(
          'Plan Year/Renewal',
          'dropdownSelect',
          clientConfig.plan_year_renewal,
          org?.plan?.plan_year_renewal,
          {
            optionsMap: benefitPeriodsMap,
            infoText: 'Select one or more options',
            isRequired: true,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Renewal Month',
          'dropdownSelect',
          clientConfig.renewal_month,
          '',
          {
            optionsMap: monthsMap,
            infoText: 'Choose one option',
            isRequired: true,
          }
        ),
        defineFormField(
          'Annual Reporting Start Month',
          'dropdownSelect',
          clientConfig.annual_reporting_start_month,
          org?.plan?.annual_reporting_start_month,
          {
            optionsMap: monthsMap,
            allowSearch: true,
            placeholder: 'Enter annual reporting start month',
            infoText: 'Select from the dropdown options',
            isRequired: true,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Plan Class',
          'dropdownSelect',
          clientConfig.product_class_id,
          org?.plan?.product?.product_class_id,
          {
            optionsMap: planClassMap,
            infoText: 'Select the start date',
            placeholder: 'Enter plan class',
          }
        ),
        defineFormField(
          'ERISA Status',
          'dropdownSelect',
          clientConfig.erisa_ind,
          org?.plan?.erisa_ind,
          {
            optionsMap: erisaMap,
            isRequired: true,
            infoText: 'Select the start date',
            placeholder: 'Enter ERISA status',
          }
        ),
      ]),
      defineFormField(
        'Previous PBM',
        'input',
        clientConfig.previous_pbm,
        org?.plan?.previous_pbm,
        {
          isRequired: true,
          infoText: 'Select previous PBM',
          placeholder: 'Enter previous PBM',
        }
      ),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField('Primary Termination Reason', 'input', '', '', {
          optionsMap: planClassMap,
          infoText: 'Select the start date',
          placeholder: 'Enter plan class',
        }),
        defineFormField('Secondary Termination Reason', 'input', '', '', {
          optionsMap: erisaMap,
          isRequired: true,
          infoText: 'Select the start date',
          placeholder: 'Enter ERISA status',
        }),
      ]),
      defineFormField('Termination Details', 'input', '', '', {
        isRequired: true,
        infoText: 'Select previous PBM',
        placeholder: 'Enter previous PBM',
      }),
    ]),
  ];

  // Navigation handlers (for Back and Continue buttons)
  const handleContinue = () => {
    console.log('Continue button clicked');
  };

  const handleBack = () => {
    console.log('Back button clicked');
  };

  return {
    subCategories,
    handleContinue,
    handleBack,
  };
}
