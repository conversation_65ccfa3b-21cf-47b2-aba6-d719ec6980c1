import { Box, HStack, Text, VStack } from '@chakra-ui/react';
import { parseDateWithoutTimezone } from '@next/shared/hooks';
import { defaultInputValidation } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/MenuItems/PlanDesignTool/validations';
import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import {
  BaseFieldConfig,
  DatepickerFieldConfig,
  DropdownSelectFieldConfig,
  FieldConfig,
  InputFieldConfig,
  TemplateFieldConfig,
} from '../../../../Models/types';
import { dateStyles } from '../../../../Styles/styles';
import { formatDate } from '../../../Table/utils/tableUtils';
import { DateControl } from '../../../Template/DateControl';
import { AncillaryToggleAccordion } from '../AncillaryToggleAccordion';
import ReusableCheckboxGroup from '../CheckBox';
import DropdownSelect from '../DropdownSelect';
import ReusableInput from '../Input';
import ReusableRadioGroup from '../RadioGroup';
import {
  convertValueByZodSchema,
  getTypeFromZodSchema,
} from '../utils/valdations';

type FormFieldConfig = FieldConfig | TemplateFieldConfig;

function hasValueProperty(
  field: FormFieldConfig
): field is FormFieldConfig & { value: any } {
  return 'value' in field;
}

interface FormFieldRendererProps {
  field: FormFieldConfig;
  formMethods: UseFormReturn<any>;
  onChange?: (name: string, value: any) => void;
  readOnly?: boolean;
  inlineWidth?: string;
  renderError?: (error: any) => React.ReactNode;
  defaultErrorRenderer?: boolean;
}

// Define a type for the options map that can handle both string and PicklistItem values
type OptionsMapValue = string | { label: string; value: string };
type OptionsMap = { [key: string]: OptionsMapValue };

// Add helper function to convert options map to string map
const convertOptionsMapToStringMap = (
  optionsMap: OptionsMap | undefined
): { [key: string]: string } => {
  if (!optionsMap) return {};

  return Object.entries(optionsMap).reduce((acc, [key, value]) => {
    acc[key] =
      typeof value === 'string' ? value : value.label || value.value || '';
    return acc;
  }, {} as { [key: string]: string });
};

/**
 * Generates validation rules for react-hook-form based on field configuration
 * Enhanced to handle Zod validation schemas
 */
export function getValidationRules(
  field: BaseFieldConfig
): Record<string, any> {
  const rules: Record<string, any> = {};

  // Skip validation for dropdown fields
  if (field.type === 'dropdownSelect') {
    return rules;
  }

  // Basic required validation
  if (field.isRequired) {
    rules.required = 'This field is required';
  }

  // Determine if we need to add validation
  let validationSchema = field.validations;

  // If no validation is provided and field type is input, add default string validation
  if (!validationSchema && field.type === 'input') {
    validationSchema = defaultInputValidation;
  }

  // Add Zod validation if provided or defaulted
  if (validationSchema) {
    rules.validate = {
      zodValidation: (value: any) => {
        // Convert the value based on the Zod schema type first
        const convertedValue = convertValueByZodSchema(value, validationSchema);

        // Run the Zod validation on the converted value
        const result = validationSchema?.safeParse(convertedValue);

        // Return true if validation passed, or error message if it failed
        return (
          result?.success || result?.error.errors[0]?.message || 'Invalid value'
        );
      },
    };
  }

  return rules;
}

/**
 * FormFieldRenderer Component
 *
 * Renders a form field based on its type, supporting integration with react-hook-form.
 * It handles editable and read-only modes as well as inline group fields.
 * Enhanced with Zod validation and type conversion.
 */
const FormFieldRenderer: React.FC<FormFieldRendererProps> = ({
  field,
  formMethods,
  onChange,
  readOnly = false,
  inlineWidth,
  renderError,
  defaultErrorRenderer = true,
}) => {
  const { control, trigger, setValue } = formMethods;

  // no op function
  const noop = () => void 0;

  /**
   * Handles field value changes with proper type conversion
   */
  const handleFieldChange = (name: string, value: any) => {
    let convertedValue = value;

    // If the field has a Zod validation schema, convert based on schema type
    if ('validations' in field && field.validations) {
      // Get the expected type from the schema
      const fieldType = getTypeFromZodSchema(field.validations);

      // Apply type conversion based on the expected type
      if (fieldType === 'number') {
        // For number fields, ensure proper conversion to number type
        convertedValue = value === '' ? null : Number(value);

        // Check if the conversion resulted in a valid number
        if (convertedValue !== null && isNaN(convertedValue)) {
          convertedValue = null;
        }
      } else if (fieldType === 'boolean') {
        // For boolean fields, convert string to boolean
        convertedValue = value === 'true' || value === true;
      }
      // Add more type conversions as needed
    }

    // Update the form value with the converted value
    // Using shouldDirty: true ensures the form recognizes the field has changed
    // Using shouldTouch: true marks the field as touched
    setValue(name, convertedValue, {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });

    if (onChange) {
      onChange(name, convertedValue);
    }

    // Trigger validation after value change
    setTimeout(() => {
      void trigger(name);
    }, 50);
  };

  const defaultRenderError = (error: any) => {
    if (!error || !defaultErrorRenderer) return null;
    return (
      <Text color="red.500" fontSize="sm">
        {error.message}
      </Text>
    );
  };

  const errorRenderer = renderError || defaultRenderError;

  // Determine field type based on Zod schema for display/input purposes
  const getFieldType = (fieldConfig: BaseFieldConfig): string | undefined => {
    if (!('validations' in fieldConfig) || !fieldConfig.validations) {
      return undefined;
    }

    return getTypeFromZodSchema(fieldConfig.validations);
  };

  // Read-only mode: render field value without input controls.
  if (readOnly) {
    // 1) Special handling for checkboxGroup in read-only mode:
    if (field.type === 'checkboxGroup') {
      return (
        <Box pointerEvents="none">
          <ReusableCheckboxGroup
            label={field.label}
            optionsMap={field.optionsMap || {}}
            selectedValues={Array.isArray(field.value) ? field.value : []}
            isRequired={(field as BaseFieldConfig).isRequired}
            infoText={(field as BaseFieldConfig).infoText}
            onChange={noop}
          />
        </Box>
      );
    }

    // 2) Inline groups in read-only mode are assumed to be handled elsewhere
    if (field.type === 'inlineGroup') {
      return null;
    }

    // 3) If the field has a 'value' property, we handle it here
    if ('value' in field) {
      const fieldValue = field.value ?? '';

      // 3a) If the value is already a valid React element, render it directly.
      if (React.isValidElement(fieldValue)) {
        return <Box>{fieldValue}</Box>;
      }

      // 3b) If it's a dropdownSelect with an optionsMap, do a lookup
      if (
        field.type === 'dropdownSelect' &&
        'optionsMap' in field &&
        field.optionsMap &&
        (field as DropdownSelectFieldConfig).optionsMap
      ) {
        const optionsMap = (field as DropdownSelectFieldConfig)
          .optionsMap as OptionsMap;
        const value =
          (typeof fieldValue === 'string' || typeof fieldValue === 'number') &&
          fieldValue !== ''
            ? typeof optionsMap[fieldValue] === 'string'
              ? optionsMap[fieldValue]
              : (optionsMap[fieldValue] as { label: string; value: string })
                  ?.label || '-'
            : '-';
        return (
          <Text fontSize="inherit" whiteSpace="pre-wrap">
            {value}
          </Text>
        );
      }

      // 3c) If the value is an array (e.g. multiple selections), join them
      if (Array.isArray(fieldValue)) {
        const optionsMap =
          field.type === 'dropdownSelect'
            ? ((field as DropdownSelectFieldConfig).optionsMap as
                | OptionsMap
                | undefined)
            : undefined;
        const value =
          fieldValue.length > 0
            ? fieldValue
                .map((val) => {
                  if (!optionsMap) return val;
                  const optionValue = optionsMap[val];
                  return typeof optionValue === 'string'
                    ? optionValue
                    : optionValue?.label || val;
                })
                .join(', ')
            : '-';
        return (
          <Text fontSize="inherit" whiteSpace="pre-wrap">
            {value}
          </Text>
        );
      }

      // 3d) Otherwise, treat it as a string
      let value =
        fieldValue === '' || fieldValue === null || fieldValue === undefined
          ? '-'
          : String(fieldValue);

      if (
        typeof fieldValue === 'string' &&
        (fieldValue.includes('undefined') || fieldValue.includes('null'))
      ) {
        value = fieldValue.replaceAll('undefined', '-').replaceAll('null', '-');
      }

      return (
        <Text fontSize="inherit" whiteSpace="pre-wrap">
          {value}
        </Text>
      );
    }

    // 4) If we have no value at all, return a dash
    return <Text fontSize="inherit">-</Text>;
  }

  // Handle inline group field type separately.
  if (field.type === 'inlineGroup') {
    const inlineFieldsCount = field.fields.length;
    const calculatedWidth =
      inlineWidth ||
      (inlineFieldsCount === 2
        ? '50%'
        : inlineFieldsCount >= 3
        ? '33.33%'
        : '100%');

    return (
      <HStack spacing={4} align="start" w="100%">
        {field.fields.map((innerField, innerIndex) =>
          'name' in innerField ? (
            <Box
              key={`${innerField.name}-${innerIndex}`}
              flex="1"
              width={calculatedWidth}
            >
              <FormFieldRenderer
                field={innerField}
                formMethods={formMethods}
                onChange={onChange}
                readOnly={readOnly}
                inlineWidth={calculatedWidth}
                renderError={renderError}
                defaultErrorRenderer={defaultErrorRenderer}
              />
            </Box>
          ) : null
        )}
      </HStack>
    );
  }

  // Render based on field type for editable fields.
  switch (field.type) {
    case 'input':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={getValidationRules(field as BaseFieldConfig)}
          defaultValue={hasValueProperty(field) ? field.value ?? '' : ''}
          render={({
            field: { onChange: formOnChange, value, ref, onBlur, name },
            fieldState: { error },
          }) => {
            // Determine if this is a number field based on Zod schema
            const fieldType = getFieldType(field as BaseFieldConfig);
            const isNumberField = fieldType === 'number';

            // For number fields, ensure empty string is displayed instead of null
            const displayValue =
              isNumberField && (value === null || value === undefined)
                ? ''
                : Array.isArray(value)
                ? value.join(', ')
                : value === null || value === undefined
                ? ''
                : value;

            // Get toggle value if toggle is enabled
            let toggleValue = false;
            const toggleName =
              (field as InputFieldConfig).toggleName || `${field.name}_differs`;

            if (
              (field as InputFieldConfig).showToggle ||
              (field as InputFieldConfig).showAncillaryToggle
            ) {
              const toggleFieldValue = formMethods.getValues(toggleName);
              toggleValue =
                toggleFieldValue === null ? false : toggleFieldValue;
            }

            return (
              <>
                <ReusableInput
                  label={field.label}
                  placeholder={field.placeholder || ''}
                  value={displayValue}
                  isRequired={(field as BaseFieldConfig).isRequired}
                  infoText={(field as BaseFieldConfig).infoText}
                  onChange={(inputValue) => {
                    formOnChange(inputValue);
                    handleFieldChange(field.name, inputValue);
                    if (
                      'onChange' in field &&
                      typeof field.onChange === 'function'
                    ) {
                      field.onChange(inputValue);
                    }
                  }}
                  onBlur={onBlur}
                  ref={ref}
                  type={isNumberField ? 'number' : 'text'}
                  isDisabled={(field as InputFieldConfig).isDisabled}
                  formMethods={formMethods}
                  name={name}
                  showAncillaryToggle={
                    (field as InputFieldConfig).showAncillaryToggle
                  }
                  showToggle={(field as InputFieldConfig).showToggle}
                  toggleLabel={(field as InputFieldConfig).toggleLabel}
                  toggleName={toggleName}
                  toggleValue={toggleValue}
                  idMap={field.idMap}
                  syncFunction={field.syncFunction}
                  onToggleChange={(isChecked) => {
                    // Update toggle state in form
                    setValue(toggleName, isChecked, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });

                    // If toggle is turned on, set the value to undefined
                    if (isChecked) {
                      formOnChange(undefined);
                      handleFieldChange(field.name, undefined);
                    } else {
                      // If toggle is turned off, reset to the original value
                      const originalValue = hasValueProperty(field)
                        ? field.value
                        : '';
                      formOnChange(originalValue);
                      handleFieldChange(field.name, originalValue);
                    }
                  }}
                  formPath={(field as InputFieldConfig).formPath}
                  sync={(field as InputFieldConfig).sync}
                  error={!!error}
                />
                {errorRenderer(error)}
              </>
            );
          }}
        />
      );

    case 'dropdownSelect': {
      const dropdownField = field as DropdownSelectFieldConfig;
      const showToggle = dropdownField.showToggle === true;
      const showAncillaryToggle = dropdownField.showAncillaryToggle === true;
      const toggleLabel =
        dropdownField.toggleLabel || 'Differs from Plan Design';
      const toggleName = dropdownField.toggleName || `${field.name}_differs`;

      // Convert optionsMap to string map
      const stringOptionsMap = convertOptionsMapToStringMap(
        dropdownField.optionsMap as OptionsMap
      );

      return (
        <VStack spacing={2} align="stretch" width="100%">
          <Controller
            name={field.name}
            control={control}
            rules={getValidationRules(field as BaseFieldConfig)}
            defaultValue={hasValueProperty(field) ? field.value ?? '' : ''}
            render={({
              field: { onChange: formOnChange, value, onBlur, name },
              fieldState: { error },
            }) => {
              let toggleValue = false;

              if (showToggle || showAncillaryToggle) {
                const toggleFieldValue = formMethods.getValues(toggleName);
                toggleValue =
                  toggleFieldValue === null ? false : toggleFieldValue;
              }

              return (
                <>
                  <DropdownSelect
                    name={name}
                    label={field.label}
                    optionsMap={stringOptionsMap}
                    placeholder={field.placeholder}
                    isRequired={dropdownField.isRequired}
                    infoText={dropdownField.infoText}
                    isDisabled={dropdownField.isDisabled}
                    formMethods={formMethods}
                    formPath={dropdownField.formPath}
                    sync={dropdownField.sync}
                    value={
                      toggleValue
                        ? null
                        : Array.isArray(value)
                        ? value[0] === '' ||
                          value[0] === null ||
                          value[0] === undefined
                          ? null
                          : value[0]
                        : value === '' || value === null || value === undefined
                        ? null
                        : String(value)
                    }
                    onChange={(name, key) => {
                      if (key === '' || key === null || key === undefined) {
                        formOnChange(null);
                        handleFieldChange(field.name, null);

                        if (dropdownField.onChange) {
                          dropdownField.onChange(null);
                        }
                        return;
                      }

                      const originalKey = key;
                      formOnChange(originalKey);
                      handleFieldChange(field.name, originalKey);

                      if (dropdownField.onChange) {
                        dropdownField.onChange(originalKey);
                      }
                    }}
                    onBlur={onBlur}
                    error={!!error}
                    showAncillaryToggle={showAncillaryToggle}
                    showToggle={showToggle}
                    toggleLabel={toggleLabel}
                    toggleName={toggleName}
                    toggleValue={toggleValue}
                    idMap={dropdownField.idMap}
                    syncFunction={dropdownField.syncFunction}
                    onToggleChange={(isChecked) => {
                      setValue(toggleName, isChecked, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true,
                      });

                      if (isChecked) {
                        formOnChange(null);
                        handleFieldChange(field.name, null);
                      } else {
                        const originalValue = hasValueProperty(field)
                          ? field.value === ''
                            ? null
                            : field.value
                          : null;
                        formOnChange(originalValue);
                        handleFieldChange(field.name, originalValue);
                      }
                    }}
                  />
                  {errorRenderer(error)}
                </>
              );
            }}
          />
        </VStack>
      );
    }

    case 'radioGroup':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={getValidationRules(field as BaseFieldConfig)}
          defaultValue={hasValueProperty(field) ? field.value ?? '' : ''}
          render={({
            field: { onChange: formOnChange, value, onBlur, name },
            fieldState: { error },
          }) => {
            // Determine if this radio group should store numeric values
            const fieldType = getFieldType(field as BaseFieldConfig);
            const isNumericRadio = fieldType === 'number';

            return (
              <>
                <ReusableRadioGroup
                  label={field.label}
                  optionsMap={field.optionsMap || {}}
                  isRequired={(field as BaseFieldConfig).isRequired}
                  infoText={(field as BaseFieldConfig).infoText}
                  onChange={(val) => {
                    // Convert to number if this is a numeric radio
                    const convertedVal =
                      isNumericRadio && val !== '' ? Number(val) : val;

                    // Update form value
                    formOnChange(convertedVal);

                    // Handle converted value
                    handleFieldChange(field.name, convertedVal);
                  }}
                  onBlur={onBlur}
                  value={
                    value === null || value === undefined ? '' : String(value)
                  }
                  layout={(field as any).layout || 'column'}
                />
                {errorRenderer(error)}
              </>
            );
          }}
        />
      );

    case 'checkboxGroup':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={getValidationRules(field as BaseFieldConfig)}
          defaultValue={hasValueProperty(field) ? field.value ?? [] : []}
          render={({
            field: { onChange: formOnChange, value, onBlur, name },
            fieldState: { error },
          }) => {
            // Determine if this checkbox group should store numeric values
            const fieldType = getFieldType(field as BaseFieldConfig);
            const isNumericCheckbox = fieldType === 'number';

            return (
              <>
                <ReusableCheckboxGroup
                  label={field.label}
                  optionsMap={field.optionsMap || {}}
                  selectedValues={
                    value === null || value === undefined
                      ? []
                      : (value as string[])
                  }
                  isRequired={(field as BaseFieldConfig).isRequired}
                  infoText={(field as BaseFieldConfig).infoText}
                  onChange={(val) => {
                    // Convert values to numbers if this is a numeric checkbox group
                    const convertedVal = isNumericCheckbox
                      ? val.map((v) => (v !== '' ? Number(v) : v))
                      : val;

                    // Update form value
                    formOnChange(convertedVal);

                    // Handle converted value
                    handleFieldChange(field.name, convertedVal);
                  }}
                  onBlur={onBlur}
                />
                {errorRenderer(error)}
              </>
            );
          }}
        />
      );

    case 'datepicker': {
      const dateField = field as DatepickerFieldConfig;
      const showAncillaryToggle = dateField.showAncillaryToggle === true;
      const toggleLabel = dateField.toggleLabel || ' Differs from Plan Design';
      const toggleName = dateField.toggleName || `${field.name}_differs`;

      return (
        <Controller
          name={field.name}
          control={control}
          rules={getValidationRules(field as BaseFieldConfig)}
          defaultValue={hasValueProperty(field) ? field.value ?? '' : ''}
          render={({
            field: { onChange: formOnChange, value, onBlur, name },
            fieldState: { error },
          }) => {
            const dateValue =
              (value === null || value === undefined ? '' : value) ||
              (hasValueProperty(field) ? field.value : undefined);

            let toggleValue = false;

            if (showAncillaryToggle) {
              const toggleFieldValue = formMethods.getValues(toggleName);
              toggleValue =
                toggleFieldValue === null ? false : toggleFieldValue;
            }

            return (
              <>
                <DateControl
                  name={name}
                  label={field.label}
                  placeholder={field.placeholder || `Select ${field.label}`}
                  isRequired={(field as BaseFieldConfig).isRequired}
                  infoText={(field as BaseFieldConfig).infoText}
                  value={
                    toggleValue
                      ? undefined
                      : dateValue
                      ? parseDateWithoutTimezone(String(dateValue))
                      : undefined
                  }
                  error={!!error}
                  onChange={(date) => {
                    const formattedDate = formatDate(date, true);
                    formOnChange(formattedDate);
                    handleFieldChange(field.name, formattedDate);
                    if (
                      'onChange' in field &&
                      typeof field.onChange === 'function' &&
                      formattedDate
                    ) {
                      field.onChange(formattedDate);
                    }
                  }}
                  onBlur={onBlur}
                  dateProps={{
                    propsConfigs: {
                      ...dateStyles,
                      inputProps: { size: 'sm', borderRadius: 4 },
                    },
                  }}
                  isDisabled={
                    (field as DatepickerFieldConfig).isDisabled || toggleValue
                  }
                />
                {errorRenderer(error)}

                {showAncillaryToggle && (
                  <AncillaryToggleAccordion
                    label={toggleLabel}
                    isChecked={toggleValue}
                    onChange={(isChecked) => {
                      // Update toggle state in form
                      setValue(toggleName, isChecked, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true,
                      });

                      // If toggle is turned on, set the value to undefined
                      if (isChecked) {
                        formOnChange(undefined);
                        handleFieldChange(field.name, undefined);
                      } else {
                        // If toggle is turned off, reset to the original value
                        const originalValue = hasValueProperty(field)
                          ? field.value
                          : '';
                        formOnChange(originalValue);
                        handleFieldChange(field.name, originalValue);
                      }
                    }}
                    name={toggleName}
                    formMethods={formMethods}
                    formPath={field.formPath}
                  />
                )}
              </>
            );
          }}
        />
      );
    }
    default:
      return null;
  }
};

export default FormFieldRenderer;
