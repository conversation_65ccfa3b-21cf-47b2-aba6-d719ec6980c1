import { Icon } from '@chakra-ui/react';
import { PiWarningFill, PiWarningOctagonFill } from 'react-icons/pi';

export const FieldWarning = () => (
  <Icon
    as={PiWarningFill}
    color="orange.400"
    boxSize="20px"
    _hover={{ bg: 'orange.100', borderRadius: 'md' }}
  />
);

export const FieldError = () => (
  <Icon
    as={PiWarningOctagonFill}
    color="red.500"
    boxSize="20px"
    _hover={{ bg: 'red.100', borderRadius: 'md' }}
  />
);
