import {
  AddOnProductResponse as BenAdminAddOnProductResponse,
  ChangeRequest as BenAdminChangeRequests,
  ContactDetails as BenAdminContactDetails,
  Organization as BenAdminOrganization,
  OrganizationDetails as BenAdminOrganizationDetails,
  PlanContainer as BenAdminP<PERSON>,
  PlanDesign as BenAdminPlanDesign,
  PlanDesignAncillary as BenAdminPlanAncillary,
  PlanDocument as BenAdminPlanDocuments,
  ValidationResponse as BenAdminValidationResponse,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';

/***********************************************
 * 1) Reusable Paginated Response
 ***********************************************/
export interface PaginatedResponse<T> {
  data: T[];
  limit: number;
  offset: number;
  totalCount: number;
}

/***********************************************
 * 2) API Data Mapping
 ***********************************************/
export interface ApiDataMap {
  document: BenAdminPlanDocuments[];
  plan: BenAdminPlan;
  contacts: BenAdminContactDetails;
  changerequest: BenAdminChangeRequests | BenAdminChangeRequests[];
  organization: BenAdminOrganization;
  organizationDetails: BenAdminOrganizationDetails;
  fileDownload: any;
  publish: undefined;
  picklist: any;
  addOnProduct: BenAdminAddOnProductResponse;
  planDesignAncillary: BenAdminPlanAncillary;
  planDesign: BenAdminPlanDesign[];
  validate: BenAdminValidationResponse;
  validateByPage: BenAdminValidationResponse;
}
