'use client';

import { Box, Flex } from '@chakra-ui/react';
import { ChangeRequestIntakeContent } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/IntakeComponents/ChangeRequestIntakeContent';
import { ValidationProvider } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useInitialChangeRequest } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useInitialChangeRequest';
import { useValidate } from 'apps/admin-portal/components/benAdmin/organization/hooks/usePageValidation';
import { LoadingSpinner } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/LoadingSpinner';
import React from 'react';

const ChangeRequestIntake: React.FC = () => {
  const { initialChangeRequest, changeRequestId } = useInitialChangeRequest();

  /**
   * VALIDATION FETCH STRATEGY:
   * - Only fetch validation data once on initial page load
   * - Cache the data for 5 minutes to avoid unnecessary refetches
   * - Individual components get validation data from context via useValidateByPageFromContext
   * - Manual refetch only happens when continue handlers explicitly call refetch()
   */
  const { validationData, isError, isLoading, refetch } = useValidate(
    changeRequestId,
    undefined, // No specific page filter - get all validation data
    {
      fetchOnMount: true, // Fetch once on initial mount
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
      cacheTime: 10 * 60 * 1000, // Keep in memory for 10 minutes
    }
  );

  // Check if initialChangeRequest is valid (non-null and non-empty).
  const isDataMissing =
    !initialChangeRequest ||
    (typeof initialChangeRequest === 'object' &&
      Object.keys(initialChangeRequest).length === 0);

  if (isDataMissing) {
    return (
      <Box w="100%" h="78.4vh" overflow="hidden">
        <Flex h="100%" align="center" justify="center">
          <LoadingSpinner isLoading={isDataMissing} />
        </Flex>
      </Box>
    );
  }

  return (
    <ValidationProvider
      validationData={validationData}
      isLoading={isLoading}
      isError={isError}
      refetch={refetch}
    >
      <ChangeRequestIntakeContent initialChangeRequest={initialChangeRequest} />
    </ValidationProvider>
  );
};

export default ChangeRequestIntake;
