/**
 * OrganizationViewPage.tsx
 *
 * This page is responsible for:
 * - Fetching all necessary API data for the Organization View Page.
 * - Passing the retrieved data to the `OrganizationView` component.
 *
 * If you need to add more data for this page:
 * 1. Define the new API call inside `useOrganizationApi.ts`.
 * 2. Use the new data inside this component.
 */

'use client';
import { ChevronRightIcon } from '@chakra-ui/icons';
import {
  Alert,
  AlertIcon,
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useOrganizationHandlers } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHandlers';
import { useOrganizationApi } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationViewApis';
import { OrganizationView } from 'apps/admin-portal/components/benAdmin/organization/OrganizationView';
import { LoadingSpinner } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/LoadingSpinner';
import { useParams } from 'next/navigation';
import { useMemo } from 'react';

const OrganizationViewPage = () => {
  const params = useParams();
  const orgId = params?.orgId as string;
  sessionStorage.removeItem('selectedChangeRequest');

  /**
   *   Fetching all necessary data for this page via `useOrganizationApi`
   * - This custom hook (`useOrganizationApi.ts`) is responsible for making API calls.
   * - Any new API data should be **added inside the hook** before being used here.
   */
  const {
    organization, // Organization-level data
    plan, // Plan-level data (fetched using org ID)
    features,
    picklists,
    error, // Any error encountered during API calls
    isLoading, // Loading state indicator
  } = useOrganizationApi(orgId);

  const organizationDetails = useMemo(() => {
    const mergedData = {
      organization,
      plan, // Use the modified plan with mock data
      features,
      picklists,
      ...organization,
    };

    return mergedData as OrganizationDetails;
  }, [organization, plan, picklists, features]);

  const { formMethods } = useOrganizationHandlers({ organizationDetails });

  /**
   *   If there's an error while fetching any data, show an error alert.
   * - This prevents users from seeing an incomplete or broken UI.
   */
  if (error) {
    return (
      <Box mt={4} p={4}>
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          {error || 'An unexpected error occurred. Please try again later.'}
        </Alert>
      </Box>
    );
  }

  return (
    <>
      <LoadingSpinner isLoading={isLoading} />

      {!isLoading && (
        <>
          {/* Breadcrumb Navigation */}
          {/* Used for navigating back to other sections of the app */}
          <Breadcrumb separator={<ChevronRightIcon />}>
            <BreadcrumbItem>
              <BreadcrumbLink href="/home">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/ben-admin">Ben Admin</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink color="#2A69AC" as="b">
                {organizationDetails?.organization?.name || 'Organization'}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>

          {/*  Loading Indicator */}
          {/* Displayed while waiting for API calls to complete */}

          {/* Organization View Component */}
          <OrganizationView
            organizationDetails={organizationDetails}
            formMethods={formMethods}
          />
        </>
      )}
    </>
  );
};

export default OrganizationViewPage;
