'use client';
// TODO: We need to do something different here when we start while labeling for other brands.
import '@fontsource/roboto';
import '@fontsource/roboto/500.css';
import '../public/reportTheme/index.css';

import { UserProvider } from '@auth0/nextjs-auth0/client';
import { ChakraProvider, createStandaloneToast } from '@chakra-ui/react';
import { datadogRum } from '@datadog/browser-rum';
import { RegisterQualtrics } from '@next/admin/components';
import { theme } from '@next/admin/constants';
import { persistPaths } from '@next/shared/constants';
import { QlikProvider } from '@next/shared/contexts';
import {
  AdminUserExtendedProvider,
  FormFieldProvider,
  PermissionsProvider,
} from '@next/shared/contexts';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// import { BreadCrumbs } from '@next/shared/ui';
import axios from 'axios';
import { Suspense } from 'react';

import { PICKLISTS } from '../components/benAdmin/organization/maps/picklistConstants';
import { useGlobalPicklists } from '../components/benAdmin/organization/usePicklistsHook';
import LoginGuard from './components/LoginGuard';

const datadogRumInit = async () => {
  let environment = 'unknown';
  try {
    const response = await axios.get('/api/getConfig');
    environment = response.data.env;
  } catch (error) {
    console.error('error fetching config');
    console.error(error);
  }

  if (environment !== 'local') {
    datadogRum.init({
      applicationId: process.env.DATADOG_APPLICATION_ID || '',
      clientToken: process.env.DATADOG_CLIENT_TOKEN || '',
      site: 'datadoghq.com',
      service: 'admin-portal',
      env: environment,
      sessionSampleRate: 100,
      sessionReplaySampleRate: 100,
      trackUserInteractions: true,
      trackResources: true,
      trackLongTasks: true,
      defaultPrivacyLevel: 'mask',
    });
    datadogRum.startSessionReplayRecording();
  }
};

// If this code is running on the client side
if (typeof window !== 'undefined') {
  // Init user monitoring
  datadogRumInit();
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 9000,
      refetchOnMount: false,
    },
  },
});

const ALL_PICKLIST_NAMES = Object.values(PICKLISTS);

function PicklistPreloader() {
  useGlobalPicklists(ALL_PICKLIST_NAMES);
  return null;
}

export default function Providers({ children }: { children: any }) {
  const { ToastContainer } = createStandaloneToast({ theme: theme });

  return (
    <QueryClientProvider client={queryClient}>
      <PicklistPreloader />
      <UserProvider>
        <AdminUserExtendedProvider>
          <Suspense>
            <LoginGuard>
              <FormFieldProvider persistPaths={persistPaths}>
                <PermissionsProvider datadogRum={datadogRum}>
                  <QlikProvider>
                    <ChakraProvider theme={theme}>
                      <RegisterQualtrics>
                        {children}
                        <ToastContainer />
                      </RegisterQualtrics>
                    </ChakraProvider>
                  </QlikProvider>
                </PermissionsProvider>
              </FormFieldProvider>
            </LoginGuard>
          </Suspense>
        </AdminUserExtendedProvider>
      </UserProvider>
    </QueryClientProvider>
  );
}
