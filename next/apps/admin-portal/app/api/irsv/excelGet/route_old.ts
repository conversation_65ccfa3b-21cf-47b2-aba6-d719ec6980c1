import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

// Configure your Lambda function details
// LEAVING THIS HERE FOR FUTURE REFERENCE as a node batcher that can create files of any size.
const LAMBDA_ENDPOINT =
  'https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/ExcelBatchDownload';

// Configure batch size and concurrency
const BATCH_SIZE = 1000;
const MAX_CONCURRENT_REQUESTS = 5; // Control concurrency to avoid overwhelming the API

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Excel export process started');
    const startTime = Date.now();
    const errorSummary: string[] = [];

    // Parse request body
    const body = await request.json();
    const {
      file_id,
      fileName = `claims_${file_id}.xlsx`,
      maxRetries = 3,
      batchRetries = 2,
    } = body;

    console.log(`📁 Processing export for file_id: ${file_id}`);

    if (!file_id) {
      console.error('❌ Missing required parameter: file_id');
      return NextResponse.json(
        { error: 'file_id parameter is required' },
        { status: 400 }
      );
    }

    // Construct the correct endpoint URL with the file_id
    const endpoint = `${LAMBDA_ENDPOINT}/${file_id}`;
    console.log(`🔗 API endpoint: ${endpoint}`);

    // Fetch total count first with retries
    console.log('📊 Fetching initial data to determine total records...');
    let initialResponse;
    try {
      initialResponse = await callLambda(
        endpoint,
        {
          limit: BATCH_SIZE,
          offset: 0,
        },
        maxRetries
      );
    } catch (error: any) {
      console.error('❌ Critical failure: Cannot fetch initial data batch');
      throw new Error(
        `Failed to fetch initial data. Cannot proceed with export: ${error.message}`
      );
    }

    if (!initialResponse || !initialResponse.pagination) {
      throw new Error('Invalid response from API: missing pagination data');
    }

    const totalRecords = initialResponse.pagination.total;
    const totalPages = Math.ceil(totalRecords / BATCH_SIZE);

    console.log(`
      📈 Export summary:
      - Total records: ${totalRecords.toLocaleString()}
      - Total pages needed: ${totalPages}
      - Batch size: ${BATCH_SIZE}
      - Concurrency level: ${MAX_CONCURRENT_REQUESTS}
      - Max retries per call: ${maxRetries}
      - Max batch retries: ${batchRetries}
      - Estimated size: ~${((totalRecords * 2) / 1024).toFixed(
        2
      )} KB (2KB per record)
    `);

    // Start with data from the first batch
    let allData = initialResponse.data || [];
    let processedRecords = allData.length;
    let failedBatches = 0;

    console.log(`
      ✅ Batch 1/${totalPages} complete:
      - Records in this batch: ${allData.length}
      - Running total: ${processedRecords}/${totalRecords} (${Math.round(
      (processedRecords / totalRecords) * 100
    )}%)
    `);

    if (totalPages > 1) {
      // Calculate offsets for all batches after the first one
      const offsets = Array.from(
        { length: totalPages - 1 },
        (_, i) => (i + 1) * BATCH_SIZE
      );

      // Process batch groups with controlled concurrency
      const batchGroupStart = Date.now();
      console.log(
        `⚙️ Processing ${offsets.length} batches in parallel (max ${MAX_CONCURRENT_REQUESTS} concurrent requests)`
      );

      // Process batches in parallel with controlled concurrency and retries
      const results = await processBatchesInParallel(
        offsets,
        async (offset) => {
          const batchStartTime = Date.now();
          try {
            const pageNumber = offset / BATCH_SIZE + 1;
            console.log(
              `⏳ Starting batch ${pageNumber}/${totalPages} (offset: ${offset})`
            );

            const batchResponse = await callLambda(
              endpoint,
              {
                offset,
                limit: BATCH_SIZE,
              },
              maxRetries
            );

            if (
              !batchResponse ||
              !batchResponse.data ||
              !Array.isArray(batchResponse.data)
            ) {
              console.error(`❌ Invalid response for offset ${offset}`);
              throw new Error(
                `Invalid response format from Lambda at offset ${offset}`
              );
            }

            const batchRecords = batchResponse.data.length;
            const batchDuration = (Date.now() - batchStartTime) / 1000;

            console.log(`
              ✅ Batch ${pageNumber}/${totalPages} complete:
              - Records: ${batchRecords}
              - Processing time: ${batchDuration.toFixed(2)}s
              - Avg records/second: ${(batchRecords / batchDuration).toFixed(0)}
            `);

            return batchResponse.data;
          } catch (error: any) {
            console.error(
              `❌ Failed to process batch with offset ${offset} after all retries:`,
              error
            );
            failedBatches++;
            errorSummary.push(`Batch offset ${offset}: ${error.message}`);
            return []; // Return empty array after all retries failed
          }
        },
        MAX_CONCURRENT_REQUESTS,
        batchRetries
      );

      // Merge all successful batch results into the main data array
      // Fix: Only add non-empty batches to prevent duplicates
      results.forEach((batchData) => {
        if (batchData && batchData.length > 0) {
          // Only add non-empty batches
          allData = allData.concat(batchData);
          processedRecords += batchData.length;
        }
      });

      const batchGroupDuration = (Date.now() - batchGroupStart) / 1000;
      console.log(`
        🔄 Parallel processing complete:
        - Total processing time: ${batchGroupDuration.toFixed(2)}s 
        - Average batch time: ${(batchGroupDuration / offsets.length).toFixed(
          2
        )}s
        - Records collected: ${processedRecords.toLocaleString()}/${totalRecords.toLocaleString()}
        - Failed batches: ${failedBatches}
      `);
    }

    // Verify we have the expected amount of data
    const missingRecords = totalRecords - allData.length;
    if (missingRecords > 0) {
      const warningMessage = `
        ⚠️ Warning: Data discrepancy
        - Expected: ${totalRecords.toLocaleString()} records
        - Received: ${allData.length.toLocaleString()} records
        - Missing: ${missingRecords.toLocaleString()} records (${(
        (missingRecords / totalRecords) *
        100
      ).toFixed(2)}%)
      `;
      console.warn(warningMessage);

      // If more than 5% of data is missing, add warning header to the Excel file
      if (missingRecords / totalRecords > 0.05) {
        // We'll add a warning worksheet in the Excel file later
      }
    }

    console.log(
      `📊 Creating Excel workbook with ${allData.length.toLocaleString()} records...`
    );

    // Create Excel workbook
    const workbook = XLSX.utils.book_new();

    // Handle potential empty dataset
    if (allData.length === 0) {
      console.warn('⚠️ No data records found - creating empty worksheet');
      const worksheet = XLSX.utils.json_to_sheet([]);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Claims Data');
    } else {
      console.log('📝 Converting JSON data to worksheet format...');

      // If there were significant errors, add a warning worksheet
      if (failedBatches > 0) {
        // Create summary worksheet with information about the export
        const summaryData = [
          { A: 'Export Summary' },
          { A: 'Date:', B: new Date().toISOString() },
          { A: 'File ID:', B: file_id },
          { A: 'Total Records Expected:', B: totalRecords },
          { A: 'Records Retrieved:', B: allData.length },
          { A: 'Missing Records:', B: missingRecords },
          {
            A: 'Missing Percentage:',
            B: `${((missingRecords / totalRecords) * 100).toFixed(2)}%`,
          },
          { A: 'Failed Batches:', B: failedBatches },
          { A: '' },
          { A: 'Error Summary:' },
        ];

        // Add each error to the summary
        errorSummary.forEach((error, index) => {
          summaryData.push({ A: `Error ${index + 1}:`, B: error });
        });

        const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(
          workbook,
          summaryWorksheet,
          'Export Summary'
        );
      }

      // Create main data worksheet
      const worksheet = XLSX.utils.json_to_sheet(allData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Claims Data');
    }

    // Generate Excel buffer
    console.log('💾 Generating Excel buffer...');
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    const successRate = (
      ((totalRecords - missingRecords) / totalRecords) *
      100
    ).toFixed(2);

    console.log(`
      🎉 Export complete:
      - Total processing time: ${duration.toFixed(2)}s
      - Records exported: ${allData.length.toLocaleString()}
      - Data completeness: ${successRate}%
      - Excel file size: ~${(excelBuffer.length / (1024 * 1024)).toFixed(2)} MB
      - Avg processing speed: ${(allData.length / duration).toFixed(
        0
      )} records/second
    `);

    // Return the Excel file with appropriate headers
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    });
  } catch (error: any) {
    console.error('❌ Export error:', error);
    return NextResponse.json(
      {
        error: 'Failed to export data',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to call Lambda with retries
async function callLambda(endpoint: string, params: any, maxRetries = 3) {
  let retries = 0;
  let lastError: any = null;

  while (retries <= maxRetries) {
    try {
      if (retries > 0) {
        // Exponential backoff with jitter for retries
        const backoffTime = Math.min(
          1000 * Math.pow(2, retries) + Math.random() * 1000,
          30000
        );
        console.log(
          `🔄 Retry attempt ${retries}/${maxRetries} for params ${JSON.stringify(
            params
          )}. Waiting ${(backoffTime / 1000).toFixed(1)}s...`
        );
        await new Promise((resolve) => setTimeout(resolve, backoffTime));
      }

      const response = await axios.get(endpoint, {
        params: params,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 60000,
      });

      // Handle both formats - direct JSON response or Lambda proxy integration format
      let result;
      if (response.data.body) {
        result =
          typeof response.data.body === 'string'
            ? JSON.parse(response.data.body)
            : response.data.body;
      } else {
        result = response.data;
      }

      if (retries > 0) {
        console.log(
          `✅ Succeeded after ${retries} retries for params ${JSON.stringify(
            params
          )}`
        );
      }

      return result;
    } catch (error: any) {
      lastError = error;
      retries++;

      // Log retry attempt
      console.warn(
        `⚠️ Lambda call failed (attempt ${retries}/${
          maxRetries + 1
        }) for params ${JSON.stringify(params)}:`,
        error.message
      );

      if (error.response) {
        console.warn(
          `Status: ${error.response.status}, Data:`,
          error.response.data
        );
      }

      // If we've exhausted all retries, throw the error
      if (retries > maxRetries) {
        console.error(
          `❌ All retry attempts failed for params ${JSON.stringify(params)}`
        );
        throw new Error(
          `Failed to call Lambda after ${maxRetries + 1} attempts: ${
            error.message
          }`
        );
      }
    }
  }

  // This should never happen due to the throw in the loop, but TypeScript needs it
  throw lastError;
}

// Process batches in parallel with controlled concurrency and retries
async function processBatchesInParallel(
  items: number[],
  processor: (item: number) => Promise<any[]>,
  concurrency: number,
  maxBatchRetries = 2
): Promise<any[][]> {
  const results: any[][] = [];
  let itemsProcessed = 0;
  const totalItems = items.length;
  const failedItems: number[] = [];

  // Process items in batches with controlled concurrency
  for (let i = 0; i < totalItems; i += concurrency) {
    const batch = items.slice(i, i + concurrency);
    console.log(
      `🔄 Processing batch group ${i / concurrency + 1}/${Math.ceil(
        totalItems / concurrency
      )}: ${batch.join(', ')}`
    );

    // Process this batch concurrently
    const batchPromises = batch.map((item) => processor(item));
    const batchResults = await Promise.allSettled(batchPromises);

    // Handle results and collect any failures
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        console.error(
          `❌ Batch with offset ${batch[index]} failed: ${result.reason}`
        );
        failedItems.push(batch[index]);
        // Push empty array as placeholder to maintain order
        results.push([]);
      }
    });

    itemsProcessed += batch.length - failedItems.length;
    console.log(
      `📊 Batch group complete: ${itemsProcessed}/${totalItems} items processed, ${failedItems.length} failures`
    );
  }

  // Retry failed items if there are any
  if (failedItems.length > 0) {
    console.log(
      `🔄 Attempting to retry ${failedItems.length} failed batches...`
    );

    let retriedSuccessfully = 0;

    // Process each failed item with retries
    for (const failedItem of failedItems) {
      let retryAttempt = 0;
      let succeeded = false;

      while (retryAttempt < maxBatchRetries && !succeeded) {
        retryAttempt++;
        const backoffTime = Math.min(
          2000 * Math.pow(2, retryAttempt) + Math.random() * 2000,
          30000
        );

        console.log(
          `🔄 Retry ${retryAttempt}/${maxBatchRetries} for batch with offset ${failedItem}. Waiting ${(
            backoffTime / 1000
          ).toFixed(1)}s...`
        );
        await new Promise((resolve) => setTimeout(resolve, backoffTime));

        try {
          const retryResult = await processor(failedItem);

          // Find the index of the failed item in the original items array
          const originalIndex = items.indexOf(failedItem);
          if (originalIndex !== -1) {
            // Replace the empty array with the successful result
            results[originalIndex] = retryResult;
            succeeded = true;
            retriedSuccessfully++;
            console.log(
              `✅ Successfully recovered batch with offset ${failedItem} on retry ${retryAttempt}`
            );
          }
        } catch (error) {
          console.error(
            `❌ Retry ${retryAttempt}/${maxBatchRetries} failed for batch with offset ${failedItem}:`,
            error
          );
        }
      }

      if (!succeeded) {
        console.error(
          `❌ All retry attempts failed for batch with offset ${failedItem}`
        );
      }
    }

    console.log(`
      🔄 Retry process complete:
      - Failed batches: ${failedItems.length}
      - Successfully recovered: ${retriedSuccessfully}
      - Permanent failures: ${failedItems.length - retriedSuccessfully}
    `);
  }

  return results;
}
