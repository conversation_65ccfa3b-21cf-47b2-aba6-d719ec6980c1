import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Excel export process started');
    const startTime = Date.now();

    // Parse request body
    const body = await request.json();
    const {
      data,
      fileName = 'export_data.xlsx',
      worksheetName = 'Data',
    } = body;

    console.log(`📁 Processing export with fileName: ${fileName}`);

    // Validate that data is provided and is an array
    if (!data) {
      console.error('❌ Missing required parameter: data');
      return NextResponse.json(
        { error: 'data parameter is required' },
        { status: 400 }
      );
    }

    if (!Array.isArray(data)) {
      console.error('❌ Invalid data format: data must be an array');
      return NextResponse.json(
        { error: 'data must be an array of objects' },
        { status: 400 }
      );
    }

    const totalRecords = data.length;

    console.log(`
      📈 Export summary:
      - Total records: ${totalRecords.toLocaleString()}
      - Estimated size: ~${((totalRecords * 2) / 1024).toFixed(
        2
      )} KB (2KB per record)
    `);

    console.log(
      `📊 Creating Excel workbook with ${data.length.toLocaleString()} records...`
    );

    // Create Excel workbook
    const workbook = XLSX.utils.book_new();

    // Handle potential empty dataset
    if (data.length === 0) {
      console.warn('⚠️ No data records found - creating empty worksheet');
      const worksheet = XLSX.utils.json_to_sheet([]);
      XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
    } else {
      console.log('📝 Converting JSON data to worksheet format...');

      // Create main data worksheet
      const worksheet = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
    }

    // Generate Excel buffer
    console.log('💾 Generating Excel buffer...');
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`
      🎉 Export complete:
      - Total processing time: ${duration.toFixed(2)}s
      - Records exported: ${data.length.toLocaleString()}
      - Excel file size: ~${(excelBuffer.length / (1024 * 1024)).toFixed(2)} MB
      - Avg processing speed: ${(data.length / duration).toFixed(
        0
      )} records/second
    `);

    // Return the Excel file with appropriate headers
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    });
  } catch (error: any) {
    console.error('❌ Export error:', error);
    return NextResponse.json(
      {
        error: 'Failed to export data',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
