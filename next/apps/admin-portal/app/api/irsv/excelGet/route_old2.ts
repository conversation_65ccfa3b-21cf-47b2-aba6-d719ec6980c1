import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

// Configure your Lambda function details
const LAMBDA_ENDPOINT =
  'https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/ExcelAggDownload';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Excel export process started');
    const startTime = Date.now();

    // Parse request body
    const body = await request.json();
    const {
      file_id,
      fileName = `claims_${file_id}.xlsx`,
      maxRetries = 3,
    } = body;

    console.log(`📁 Processing export for file_id: ${file_id}`);

    if (!file_id) {
      console.error('❌ Missing required parameter: file_id');
      return NextResponse.json(
        { error: 'file_id parameter is required' },
        { status: 400 }
      );
    }

    // Construct the endpoint URL with the file_id
    const endpoint = `${LAMBDA_ENDPOINT}/${file_id}`;
    console.log(`🔗 API endpoint: ${endpoint}`);

    // Make single call to Lambda to get all data
    console.log('📊 Fetching all data from Lambda...');
    const response = await callLambdaWithRetries(endpoint, maxRetries);

    if (!response || !response.data || !Array.isArray(response.data)) {
      throw new Error('Invalid response format from Lambda');
    }

    const allData = response.data;
    const totalRecords = allData.length;

    console.log(`
      📈 Export summary:
      - Total records: ${totalRecords.toLocaleString()}
      - Estimated size: ~${((totalRecords * 2) / 1024).toFixed(
        2
      )} KB (2KB per record)
    `);

    console.log(
      `📊 Creating Excel workbook with ${allData.length.toLocaleString()} records...`
    );

    // Create Excel workbook
    const workbook = XLSX.utils.book_new();

    // Handle potential empty dataset
    if (allData.length === 0) {
      console.warn('⚠️ No data records found - creating empty worksheet');
      const worksheet = XLSX.utils.json_to_sheet([]);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Claims Data');
    } else {
      console.log('📝 Converting JSON data to worksheet format...');

      // Create main data worksheet
      const worksheet = XLSX.utils.json_to_sheet(allData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Claims Data');
    }

    // Generate Excel buffer
    console.log('💾 Generating Excel buffer...');
    const excelBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`
      🎉 Export complete:
      - Total processing time: ${duration.toFixed(2)}s
      - Records exported: ${allData.length.toLocaleString()}
      - Excel file size: ~${(excelBuffer.length / (1024 * 1024)).toFixed(2)} MB
      - Avg processing speed: ${(allData.length / duration).toFixed(
        0
      )} records/second
    `);

    // Return the Excel file with appropriate headers
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    });
  } catch (error: any) {
    console.error('❌ Export error:', error);
    return NextResponse.json(
      {
        error: 'Failed to export data',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to call Lambda with retries
async function callLambdaWithRetries(endpoint: string, maxRetries = 3) {
  let retries = 0;
  let lastError: any = null;

  while (retries <= maxRetries) {
    try {
      if (retries > 0) {
        // Exponential backoff with jitter for retries
        const backoffTime = Math.min(
          1000 * Math.pow(2, retries) + Math.random() * 1000,
          30000
        );
        console.log(
          `🔄 Retry attempt ${retries}/${maxRetries}. Waiting ${(
            backoffTime / 1000
          ).toFixed(1)}s...`
        );
        await new Promise((resolve) => setTimeout(resolve, backoffTime));
      }

      console.log(
        `📡 Making API call to Lambda (attempt ${retries + 1}/${
          maxRetries + 1
        })...`
      );

      const response = await axios.get(endpoint, {
        // headers: {
        //   'Content-Type': 'application/json',
        // },
        timeout: 300000, // 5 minute timeout for large datasets
      });

      // Handle both formats - direct JSON response or Lambda proxy integration format
      let result;
      if (response.data.body) {
        result =
          typeof response.data.body === 'string'
            ? JSON.parse(response.data.body)
            : response.data.body;
      } else {
        result = response.data;
      }

      if (retries > 0) {
        console.log(`✅ Succeeded after ${retries} retries`);
      } else {
        console.log('✅ Lambda call successful on first attempt');
      }

      return result;
    } catch (error: any) {
      lastError = error;
      retries++;

      // Log retry attempt
      console.warn(
        `⚠️ Lambda call failed (attempt ${retries}/${maxRetries + 1}):`,
        error.message
      );

      if (error.response) {
        console.warn(
          `Status: ${error.response.status}, Data:`,
          error.response.data
        );
      }

      // If we've exhausted all retries, throw the error
      if (retries > maxRetries) {
        console.error(
          `❌ All retry attempts failed after ${maxRetries + 1} attempts`
        );
        throw new Error(
          `Failed to call Lambda after ${maxRetries + 1} attempts: ${
            error.message
          }`
        );
      }
    }
  }

  // This should never happen due to the throw in the loop, but TypeScript needs it
  throw lastError;
}
