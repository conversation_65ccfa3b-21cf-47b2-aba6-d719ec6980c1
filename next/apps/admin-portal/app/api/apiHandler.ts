//apiHandler

import { getAccessToken, withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextRequest, NextResponse } from 'next/server';

import { apiConfig } from './apiMaps';

/**
 * Utility to fetch data based on the method and keys
 */
const fetchData = async (
  key: string,
  method: string,
  req: NextRequest,
  params: any
) => {
  const res = new NextResponse();
  const { accessToken } = await getAccessToken(req, res);

  const urlConfig = apiConfig[key];
  if (!urlConfig) {
    console.error(`[fetchData] No configuration found for key: ${key}`);
    throw new Error(`No configuration found for key: ${key}`);
  }

  const { searchParams } = req.nextUrl;
  const contentType = req.headers.get('content-type');

  // URL will now be resolved at runtime asynchronously
  let url =
    typeof urlConfig.url === 'function'
      ? await urlConfig.url(params)
      : urlConfig.url;

  console.log(`[fetchData] Resolved URL: ${url}`);
  console.log(`[fetchData] Environment variables:`, {
    NEXT_PUBLIC_BEN_ADMIN_URL: process.env.NEXT_PUBLIC_BEN_ADMIN_URL,
  });

  const headers: Record<string, string> = {
    ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
    'Content-Type': contentType ?? 'application/json',
  };

  if (searchParams) {
    url += `?${searchParams?.toString()}`;
  }

  // send correct data format based on content-type here
  let body;
  if (method !== 'GET') {
    if (req.headers.get('content-type')?.includes('multipart/form-data')) {
      body = await req.blob();
    } else {
      const jsonData = await req.json();
      body = JSON.stringify(jsonData);
    }
  }

  const fetchOptions = {
    method,
    headers,
    ...(body && { body }),
  };

  const response = await fetch(url!, fetchOptions);

  // Handle both successful and error responses by parsing the body
  let responseData;

  // For file downloads since they're binary, not json
  if (
    response.headers.get('content-type')?.includes('application/octet-stream')
  ) {
    responseData = await response.blob();
  } else {
    try {
      responseData = await response.json();
    } catch (parseError) {
      // If JSON parsing fails, fall back to text
      responseData = await response.text();
    }
  }

  // If the response is not OK, throw an error with the actual response data
  if (!response.ok) {
    console.error(
      `[fetchData] Request failed with status ${response.status}`,
      responseData
    );
    const error = new Error(`Request failed with status ${response.status}`);
    // Attach the response data to the error so it can be accessed
    (error as any).responseData = responseData;
    (error as any).status = response.status;
    throw error;
  }

  return responseData;
};

/**
 * Flattens nested params objects
 */
const getFlattenedParams = (params: any): any => {
  while (params && params.params) {
    params = params.params;
  }
  return params;
};

/**
 * Generic handler that processes all methods dynamically
 */
export const apiHandler = withApiAuthRequired(
  async (req: NextRequest, params: any) => {
    const flattenedParams = getFlattenedParams(params);
    const method = req.method?.toUpperCase();
    const key = req.headers.get('X-Request-Key');

    try {
      if (!method || !key) {
        throw new Error('Invalid request: missing method or key');
      }
      const result = await fetchData(key, method, req, flattenedParams);
      // A file cannot be JSON, needs to be a blob. Send that according to type
      if (result.type === 'application/octet-stream') {
        return new NextResponse(result, { status: 200, statusText: 'OK' });
      }
      return NextResponse.json(result);
    } catch (error: any) {
      // If the error has responseData (from a failed API call), return that instead of a generic error
      if (error.responseData) {
        // Special handling for validation endpoints - convert 400 to 200 since validation errors are expected results
        const isValidationEndpoint =
          key === 'validate' || key === 'validateByPage';
        const statusCode =
          isValidationEndpoint && error.status === 400
            ? 200
            : error.status || 500;

        return NextResponse.json(error.responseData, { status: statusCode });
      }

      // For other types of errors, return the generic error message
      return NextResponse.json(
        { error: error.message },
        { status: error.status || 500 }
      );
    }
  }
);

/**
 * Function to create handlers for each HTTP method
 */
export const createHandler = (method: string) => {
  return (req: NextRequest, params: any) => {
    if (req.method !== method) {
      return NextResponse.json(
        { error: `Method ${req.method} not allowed` },
        { status: 405 }
      );
    }
    return apiHandler(req, params);
  };
};
