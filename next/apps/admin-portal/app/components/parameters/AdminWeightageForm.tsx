import {
  Box,
  Button,
  FormControl,
  HStack,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Text,
  useToast,
  VStack,
} from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

type WeightType = 'cost' | 'manual';

interface WeightingEntry {
  weightType: WeightType;
  brandWeight?: number;
  genericWeight?: number;
}

interface WeightingState {
  [key: string]: WeightingEntry;
}

const defaultWeighting: WeightingState = {
  formularyExclusions: { weightType: 'cost' },
  part1GLP1WeightLoss: { weightType: 'cost' },
  part1GLP1Diabetes: { weightType: 'cost' },
  part1HDCR: { weightType: 'cost' },
  part1PA: { weightType: 'cost' },
};

const sectionLabels: Record<string, string> = {
  formularyExclusions: 'Formulary Exclusions',
  part1GLP1WeightLoss: 'Part 1 GLP1 Weight Loss',
  part1GLP1Diabetes: 'Part 1 GLP1 Diabetes',
  part1HDCR: 'Part 1 HDCR',
  part1PA: 'Part 1 PA',
};

interface Props {
  initialWeighting?: WeightingState;
  onSave: (weighting: WeightingState) => Promise<void>;
}

export default function AdminWeightingForm({
  initialWeighting,
  onSave,
}: Props) {
  const [form, setForm] = useState<WeightingState>(defaultWeighting);
  const [isSaving, setIsSaving] = useState(false);
  const toast = useToast();

  useEffect(() => {
    if (initialWeighting) {
      setForm((prev) => ({
        ...prev,
        ...initialWeighting,
      }));
    }
  }, [initialWeighting]);

  const handleChange = (
    key: string,
    field: keyof WeightingEntry,
    value: string | number
  ) => {
    setForm((prev) => {
      const current = prev[key];

      if (field === 'weightType' && value === 'manual') {
        return {
          ...prev,
          [key]: {
            weightType: 'manual',
            brandWeight: current.brandWeight ?? 70,
            genericWeight: current.genericWeight ?? 30,
          },
        };
      }

      return {
        ...prev,
        [key]: {
          ...current,
          [field]: value,
        },
      };
    });
  };

  const isValid = (entry: WeightingEntry) => {
    if (entry.weightType === 'manual') {
      const total = (entry.brandWeight || 0) + (entry.genericWeight || 0);
      return total === 100;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const invalidKey = Object.keys(form).find((key) => !isValid(form[key]));
    if (invalidKey) {
      toast({
        title: 'Validation Error',
        description: `"${sectionLabels[invalidKey]}" weights must add up to 100%.`,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
      return;
    }

    setIsSaving(true);
    try {
      await onSave(form);
      toast({
        title: 'Saved Successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Save Failed',
        description: err instanceof Error ? err.message : 'Unexpected error',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderSection = (key: string, label: string) => {
    const entry = form[key];
    const isManual = entry.weightType === 'manual';
    const total = (entry.brandWeight || 0) + (entry.genericWeight || 0);
    const isInvalid = isManual && total !== 100;

    return (
      <Box key={key} borderWidth="1px" borderRadius="md" p={4}>
        <Text fontWeight="semibold" mb={3}>
          {label}
        </Text>

        <RadioGroup
          value={entry.weightType}
          onChange={(val) => handleChange(key, 'weightType', val)}
        >
          <Stack direction="column" spacing={2}>
            <Radio value="cost">Cost Basis Weighting</Radio>
            <Radio value="manual">Manual Entry Weighting</Radio>
          </Stack>
        </RadioGroup>

        {isManual && (
          <FormControl isInvalid={isInvalid} mt={4}>
            <HStack spacing={4}>
              <Box flex="1">
                <Text fontSize="sm" mb={1}>
                  Brand Weighting (%)
                </Text>
                <Input
                  type="number"
                  value={entry.brandWeight ?? ''}
                  onChange={(e) =>
                    handleChange(key, 'brandWeight', Number(e.target.value))
                  }
                />
              </Box>
              <Box flex="1">
                <Text fontSize="sm" mb={1}>
                  Generic Weighting (%)
                </Text>
                <Input
                  type="number"
                  value={entry.genericWeight ?? ''}
                  onChange={(e) =>
                    handleChange(key, 'genericWeight', Number(e.target.value))
                  }
                />
              </Box>
            </HStack>

            {isInvalid && (
              <Box
                mt={3}
                bg="orange.50"
                border="1px"
                borderColor="orange.300"
                borderRadius="md"
                p={3}
              >
                <Text fontSize="sm" color="orange.800" fontWeight="medium">
                  ⚠️ Brand and Generic weights must add up to 100%
                </Text>
              </Box>
            )}

            {!isInvalid && (
              <Text mt={2} fontSize="sm" color="green.500">
                Total: {total.toFixed(2)}%
              </Text>
            )}
          </FormControl>
        )}
      </Box>
    );
  };

  return (
    <form onSubmit={handleSubmit}>
      <VStack align="stretch" spacing={6}>
        {Object.entries(sectionLabels).map(([key, label]) =>
          renderSection(key, label)
        )}

        <Button
          type="submit"
          colorScheme="blue"
          isLoading={isSaving}
          loadingText="Saving..."
          size="sm"
          alignSelf="flex-end"
        >
          Save Weighting Configuration
        </Button>
      </VStack>
    </form>
  );
}
