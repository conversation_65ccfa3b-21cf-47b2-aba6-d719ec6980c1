import { Box, Heading, HStack } from '@chakra-ui/react';
import AdminWeightingForm from 'apps/admin-portal/app/components/parameters/AdminWeightageForm';

interface Props {
  opportunity: any;
}

const AdminTab = ({ opportunity }: Props) => {
  const handleWeitageSave = async (weitagesInfo: any) => {
    try {
      const fullGeneralInformation = {
        ...(opportunity.opportunityMetadata?.generalInformation || {}),
        weighting: weitagesInfo,
      };

      const response = await fetch(
        `https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/opportunities/${opportunity.opportunityId}/general-information`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(fullGeneralInformation),
        }
      );

      if (!response.ok) {
        throw new Error('Failed to save weighting');
      }

      console.log('✅ Weighting saved successfully');
    } catch (err) {
      console.error('❌ Save error', err);
    }
  };

  return (
    <Box mx="auto" p={6}>
      <HStack mb={6}>
        <Heading size="md">Weighting Configuration</Heading>
      </HStack>

      <AdminWeightingForm
        initialWeighting={
          opportunity.opportunityMetadata?.generalInformation?.weighting
        }
        onSave={handleWeitageSave}
      />
    </Box>
  );
};

export default AdminTab;
