// File: src/app/opportunities/[opportunityId]/components/tabs/ParametersTab.tsx
'use client';

import { Box, Card, CardHeader, Heading, VStack } from '@chakra-ui/react';
// import React from 'react';
// import GeneralInformationForm from '@/components/parameters/GeneralInformationForm';
import GeneralInformationForm from 'apps/admin-portal/app/components/parameters/GeneralInformationForm';

// import { Opportunity } from '@/types/opportunity';

// interface any {
//   opportunity: Opportunity;
// }

export default function GeneralInformationTab({ opportunity }: any) {
  const handleSaveGeneralInfo = async (generalInfo: any) => {
    try {
      const previousWeighting =
        opportunity.opportunityMetadata?.generalInformation?.weighting;

      const mergedGeneralInfo = {
        ...generalInfo,
        weighting: previousWeighting ?? {}, // preserve weighting
      };
      const response = await fetch(
        `https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/opportunities/${opportunity.opportunityId}/general-information`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mergedGeneralInfo),
        }
      );

      if (!response.ok) {
        throw new Error('Failed to save general information');
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <VStack spacing={6} align="stretch">
      <Card>
        <CardHeader>
          <Heading size="md">General Information</Heading>
        </CardHeader>
        <Box p={6}>
          <GeneralInformationForm
            opportunityId={opportunity.opportunityId}
            onSave={handleSaveGeneralInfo}
            initialGeneralInfo={
              opportunity.opportunityMetadata?.generalInformation
            }
          />
        </Box>
      </Card>
    </VStack>
  );
}
