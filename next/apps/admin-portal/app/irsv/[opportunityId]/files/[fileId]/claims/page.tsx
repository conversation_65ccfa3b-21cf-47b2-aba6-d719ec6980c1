// File: src/app/opportunities/[opportunityId]/files/[fileId]/claims/page.tsx

'use client';

import {
  Badge,
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  CardHeader,
  Collapse,
  Container,
  Divider,
  Flex,
  Heading,
  HStack,
  Icon,
  IconButton,
  Spinner,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useDisclosure,
  useToast,
  VStack,
} from '@chakra-ui/react';
import ClaimDetailPanel from 'apps/admin-portal/app/components/claims/ClaimDetailPanel';
import { ClaimsFilter } from 'apps/admin-portal/app/components/claims/ClaimsFilter';
import { ClaimsTable } from 'apps/admin-portal/app/components/claims/ClaimsTable';
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Download,
  FileText,
  Filter,
  RefreshCw,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

// import ClaimDetailPanel from '@/components/claims/ClaimDetailPanel';
// import { ClaimsFilter } from '@/components/claims/ClaimsFilter';
// Import components
// import { ClaimsTable } from '@/components/claims/ClaimsTable';
// Import types
export interface FileRecord {
  fileId: string;
  opportunityId: string;
  productId: string;
  originalFilename: string;
  uploadDate: Date;
  s3Location: string;
  status: FileStatus;
  fileSize: number;
  originalHeaders: string[];
  rowCount: number;
  processingStage: any;
  createdBy?: string;
  createdAt?: Date;
  updatedBy?: string;
  updatedAt?: Date;
}
export enum FileStatus {
  PENDING = 'PENDING', // File is awaiting action
  PROCESSING = 'PROCESSING', // File is being processed
  MAPPED = 'MAPPED', // File has been successfully mapped
  ERROR = 'ERROR', // An error occurred during processing
  PROCESSING_CLAIMS = 'PROCESSING_CLAIMS', // File is being processed to create claims
  PROCESSED = 'PROCESSED', // Claims processing is complete
  ENRICHED = 'ENRICHED', // File has been enriched with additional data
}
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return 'N/A';

  try {
    const d = typeof date === 'string' ? new Date(date) : date;

    // Check for invalid date
    if (isNaN(d.getTime())) {
      return 'Invalid Date';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };

    return new Intl.DateTimeFormat('en-US', options).format(d);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
}
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}
// import { FileRecord, FileStatus } from '@/types/file';
// import { formatDate, formatFileSize } from '@/utils/format';

import ExclusionsTab from './components/ExclusionsTab'; // Import the new component

interface ClaimRecord {
  recordId: string;
  rowNumber: number;
  mappedFields: Record<string, any>;
  unmappedFields: Record<string, any>;
  validationStatus: string;
  processingStatus: string;
  createdAt: string;
}

export default function ClaimsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const toast = useToast();

  // Get active tab from URL or default to "data"
  const activeTab = searchParams?.get('tab') || 'data';

  // State management
  const [file, setFile] = useState<FileRecord | null>(null);
  const [claims, setClaims] = useState<ClaimRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState({
    validationStatus: '',
    processingStatus: '',
    searchTerm: '',
  });
  const [selectedClaim, setSelectedClaim] = useState<ClaimRecord | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  // UI state management
  const { isOpen: isFilterOpen, onToggle: onToggleFilter } = useDisclosure();
  const { isOpen: isFileDetailsOpen, onToggle: onToggleFileDetails } =
    useDisclosure({ defaultIsOpen: true });
  const {
    isOpen: isDetailPanelOpen,
    onOpen: onOpenDetailPanel,
    onClose: onCloseDetailPanel,
  } = useDisclosure();

  // Fetch file details and claims data
  useEffect(() => {
    if (!params?.fileId || !params?.opportunityId) {
      setError('Invalid file or opportunity ID');
      setLoading(false);
      return;
    }

    Promise.all([fetchFileDetails(), fetchClaims()]).catch((error) => {
      setError(error instanceof Error ? error.message : 'Failed to load data');
      setLoading(false);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params?.fileId, params?.opportunityId]);

  const fetchFileDetails = async () => {
    const response = await fetch(
      `https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/files/${params?.fileId}`
    );
    if (!response.ok) throw new Error('Failed to fetch file details');
    const data = await response.json();
    setFile(data);
  };

  const fetchClaims = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: '25', // Reduced from default for better performance
        validationStatus: filters.validationStatus,
        processingStatus: filters.processingStatus,
        search: filters.searchTerm,
      });

      const response = await fetch(
        `https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/files/${
          params?.fileId
        }/claims?${queryParams.toString()}`
      );

      if (!response.ok) throw new Error('Failed to fetch claims');

      const data = await response.json();
      setClaims(data.claims);
      setTotalPages(data.pagination.totalPages);
      setTotalRecords(data.pagination.totalRecords);
    } catch (error) {
      console.error('Error fetching claims:', error);
      toast({
        title: 'Error',
        description: 'Failed to load claims data',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle page changes
  useEffect(() => {
    if (params?.fileId) {
      fetchClaims();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, params?.fileId]);

  // Handle tab change
  const handleTabChange = (tabValue: string) => {
    // Update URL with new tab parameter
    const newParams = new URLSearchParams(searchParams?.toString());
    newParams.set('tab', tabValue);
    router.push(`${window.location.pathname}?${newParams.toString()}`);
  };

  const handleExport = async () => {
    try {
      setIsExporting(true); // Start loading state
      const response = await fetch(
        `https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/ExcelAggDownload/${params?.fileId}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      let data = await response.json();
      data = data.data;

      const response2 = await fetch('/api/irsv/excelGet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data }),
      });

      if (!response2.ok) throw new Error('Failed to initiate export');

      const blob = await response2.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `claims_${params?.fileId}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove(); // Clean up the DOM element

      toast({
        title: 'Export Successful',
        description: 'Claims data has been exported',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description:
          error instanceof Error ? error.message : 'Failed to export claims',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsExporting(false); // End loading state
    }
  };

  const handleRefresh = () => {
    fetchClaims();
  };

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
    fetchClaims();
  };

  const handleViewClaim = (claim: ClaimRecord) => {
    setSelectedClaim(claim);
    onOpenDetailPanel();
  };

  const getStatusColor = (status: FileStatus): string => {
    switch (status) {
      case FileStatus.PROCESSED:
        return 'purple';
      case FileStatus.ENRICHED:
        return 'teal';
      case FileStatus.ERROR:
        return 'red';
      default:
        return 'blue';
    }
  };

  if (loading && !claims.length) {
    return (
      <Container maxW="container.xl" centerContent py={10}>
        <VStack spacing={4}>
          <Heading size="md">Loading Claims Data</Heading>
          <Text>Please wait while we load the claims data...</Text>
        </VStack>
      </Container>
    );
  }

  if (error || !file) {
    return (
      <Container maxW="container.xl" py={10}>
        <VStack spacing={4} align="start">
          <Heading size="md">Error</Heading>
          <Text color="red.500">{error || 'File not found'}</Text>
          <Button
            leftIcon={<ChevronLeft size={16} />}
            onClick={() => router.back()}
            size="sm"
            colorScheme="blue"
          >
            Back
          </Button>
        </VStack>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={6}>
      {/* Breadcrumb Navigation */}
      <Breadcrumb spacing="8px" separator={<ChevronRight size={16} />} mb={6}>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/irsv">
            Opportunities
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href={`/irsv/${params?.opportunityId}`}>
            Opportunity
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink
            as={Link}
            href={`/irsv/${params?.opportunityId}?tab=files`}
          >
            Files
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <Text>Claims</Text>
        </BreadcrumbItem>
      </Breadcrumb>

      {/* File Details Card */}
      <Card mb={4} variant="outline">
        <CardHeader p={4}>
          <Flex justify="space-between" align="center">
            <HStack>
              <Icon as={FileText} color="blue.500" boxSize="20px" />
              <Heading size="md">{file.originalFilename}</Heading>
              <Badge colorScheme={getStatusColor(file.status)} ml={2}>
                {file.status}
              </Badge>
            </HStack>
            <Button
              size="sm"
              variant="ghost"
              rightIcon={
                isFileDetailsOpen ? (
                  <ChevronUp size={16} />
                ) : (
                  <ChevronDown size={16} />
                )
              }
              onClick={onToggleFileDetails}
            >
              {isFileDetailsOpen ? 'Hide Details' : 'Show Details'}
            </Button>
          </Flex>
        </CardHeader>
        <Collapse in={isFileDetailsOpen}>
          <CardBody pt={0} pb={4} px={4}>
            <Divider mb={4} />
            <Flex
              direction={{ base: 'column', md: 'row' }}
              gap={4}
              flexWrap="wrap"
            >
              <Box minW="200px">
                <Text fontSize="sm" fontWeight="medium" color="gray.500">
                  File Information
                </Text>
                <Text fontSize="sm" mt={1}>
                  Uploaded: {formatDate(file.uploadDate)}
                </Text>
                <Text fontSize="sm">Size: {formatFileSize(file.fileSize)}</Text>
                <Text fontSize="sm">Total Claims: {file.rowCount}</Text>
              </Box>
            </Flex>
          </CardBody>
        </Collapse>
      </Card>

      {/* Actions & Filters - Only show for Claims Data tab */}
      {activeTab === 'data' && (
        <Flex mb={4} wrap="wrap" justify="space-between" align="center" gap={2}>
          <HStack>
            <Button
              leftIcon={<Filter size={16} />}
              onClick={onToggleFilter}
              size="sm"
              colorScheme={isFilterOpen ? 'blue' : 'gray'}
              variant={isFilterOpen ? 'solid' : 'outline'}
            >
              Filter
            </Button>

            <Button
              leftIcon={
                isExporting ? <Spinner size="sm" /> : <Download size={16} />
              }
              onClick={handleExport}
              size="sm"
              colorScheme="green"
              variant="outline"
              isDisabled={isExporting}
            >
              {isExporting ? 'Exporting...' : 'Export'}
            </Button>
          </HStack>

          <HStack>
            <Text fontSize="sm" color="gray.600">
              Showing {claims.length} of {totalRecords.toLocaleString()} claims
            </Text>
            <IconButton
              aria-label="Refresh data"
              icon={<RefreshCw size={16} />}
              onClick={handleRefresh}
              size="sm"
              variant="ghost"
            />
          </HStack>
        </Flex>
      )}

      {/* Filters Panel - Only show for Claims Data tab */}
      {activeTab === 'data' && (
        <Collapse in={isFilterOpen} animateOpacity>
          <Card mb={4} variant="outline">
            <CardBody p={4}>
              <ClaimsFilter
                filters={filters}
                onChange={handleFilterChange}
                totalResults={totalRecords}
              />
            </CardBody>
          </Card>
        </Collapse>
      )}

      {/* Tabs Container */}
      <Tabs
        variant="enclosed"
        colorScheme="blue"
        index={activeTab === 'data' ? 0 : activeTab === 'exclusions' ? 1 : 0}
        onChange={(index) =>
          handleTabChange(index === 0 ? 'data' : 'exclusions')
        }
      >
        <TabList>
          <Tab>Claims Data</Tab>
          <Tab>Clinical Savings</Tab>
        </TabList>

        <TabPanels>
          {/* Claims Data Tab */}
          <TabPanel px={0} py={4}>
            <ClaimsTable
              claims={claims}
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              isLoading={loading}
              onViewDetails={handleViewClaim}
            />
          </TabPanel>

          {/* Exclusions Tab */}
          <TabPanel px={0} py={4}>
            <ExclusionsTab fileId={params?.fileId as string} />
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Claim Detail Panel (Slide-out) */}
      {selectedClaim && (
        <ClaimDetailPanel
          isOpen={isDetailPanelOpen}
          onClose={onCloseDetailPanel}
          claim={selectedClaim}
        />
      )}
    </Container>
  );
}
