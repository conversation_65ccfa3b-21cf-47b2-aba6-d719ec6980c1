// File: src/app/opportunities/[opportunityId]/page.tsx

'use client';

import {
  <PERSON>ert,
  AlertIcon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Container,
  HStack,
  Spinner,
  VStack,
} from '@chakra-ui/react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// import { Opportunity } from '@/types/opportunity';
import OpportunityTabs from './components/OpportunityTabs';
import AdminTab from './components/tabs/AdminTab';
import FilesTab from './components/tabs/FilesTab';
import GeneralInformationTab from './components/tabs/GeneralInformationTab';
import OverviewTab from './components/tabs/OverviewTab';

export default function OpportunityDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [opportunity, setOpportunity] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!params?.opportunityId) {
      setError('Invalid opportunity ID');
      setIsLoading(false);
      return;
    }

    fetchOpportunityData(params.opportunityId as string);
  }, [params?.opportunityId]);

  const fetchOpportunityData = async (id: string) => {
    try {
      const response = await fetch(
        `https://5mtzqjvo9j.execute-api.us-east-1.amazonaws.com/dev/api/opportunities/${id}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Opportunity not found');
        }
        const errorData = await response.json();
        throw new Error(
          errorData.error || 'Failed to fetch opportunity details'
        );
      }

      const data = await response.json();
      setOpportunity(data);
    } catch (err) {
      console.error('Error fetching opportunity:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to load opportunity'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Container maxW="container.xl" centerContent py={10}>
        <Spinner size="xl" />
      </Container>
    );
  }

  if (error || !opportunity) {
    return (
      <Container maxW="container.xl" py={10}>
        <Alert status="error">
          <AlertIcon />
          {error || 'Opportunity data not available'}
        </Alert>
        <Button
          leftIcon={<ChevronLeft />}
          mt={4}
          onClick={() => router.push('/irsv')}
        >
          Back to Opportunities
        </Button>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <HStack justify="space-between">
          <Breadcrumb spacing="8px" separator={<ChevronRight size={16} />}>
            <BreadcrumbItem>
              <BreadcrumbLink as={Link} href="/irsv">
                Opportunities
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>{opportunity.employer}</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>

          <Button
            leftIcon={<ChevronLeft />}
            variant="ghost"
            onClick={() => router.push('/irsv')}
          >
            Back
          </Button>
        </HStack>

        <OpportunityTabs>
          <OverviewTab opportunity={opportunity} />
          <FilesTab opportunityId={opportunity.opportunityId} />
          <GeneralInformationTab opportunity={opportunity} />
          <AdminTab opportunity={opportunity} />
        </OpportunityTabs>
      </VStack>
    </Container>
  );
}
