import {
  Badge,
  Box,
  Flex,
  Heading,
  Icon,
  Text,
  useColorModeValue,
  VStack,
} from '@chakra-ui/react';
import { FiDownload } from 'react-icons/fi';

const PharmacyClaimExportTasks = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  return (
    <Flex direction="column" minH="100vh" bg="white" p={6}>
      <Box
        maxW="2xl"
        mx="auto"
        w="full"
        bg={bgColor}
        borderRadius="lg"
        boxShadow="sm"
        border="1px"
        borderColor={borderColor}
        p={8}
        mt={4}
      >
        <VStack spacing={6} textAlign="center">
          <VStack spacing={3}>
            <Icon
              as={FiDownload}
              w={12}
              h={12}
              color="green.500"
              bg="green.50"
              p={3}
              borderRadius="full"
            />
            <Heading size="lg" color="gray.800">
              Pharmacy Claim Export Tasks
            </Heading>
            <Text fontSize="md" color={textColor} maxW="lg">
              Schedule and manage pharmacy claim export operations and track
              task progress.
            </Text>
          </VStack>

          <Badge
            colorScheme="orange"
            fontSize="sm"
            px={3}
            py={1}
            borderRadius="full"
          >
            Coming Soon
          </Badge>

          <Text fontSize="sm" color={textColor} mt={4}>
            This feature is currently under development.
          </Text>
        </VStack>
      </Box>
    </Flex>
  );
};

export default PharmacyClaimExportTasks;
