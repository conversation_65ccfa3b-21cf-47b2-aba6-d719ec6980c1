import { Box, Button, Flex, Heading, Text } from '@chakra-ui/react';
import {
  AlertBox,
  CustomReactTable,
  GenericModal,
  Pagination,
} from '@next/admin/components';
import { buttonStyling, STATUS } from '@next/admin/constants';
import { useJavaApi } from '@next/shared/api';
import { paginateData } from '@next/shared/helpers';
import {
  downloadPDF,
  downloadXLSX,
  useCustomToast,
  useDownloadFile,
  useMembershipColumns,
  usePollRequest,
  useQueryParams,
} from '@next/shared/hooks';
import { BackButton, DataTable as Table } from '@next/shared/ui';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { FaFilePdf } from 'react-icons/fa';
import { RiFileExcel2Fill } from 'react-icons/ri';

import BatchProgress from '../../../../components/batchProgress/BatchProgress';

const tableContainer = {
  width: '100%',
  height: '100%',
  overflow: 'auto',
};

const MembershipBilligDetail = () => {
  const handleFileDownload = useDownloadFile();

  const router = useRouter();

  const showToast = useCustomToast();
  const {
    membershipBillingPeriodNo,
    page = '1',
    size = '10',
  } = useQueryParams(['membershipBillingPeriodNo', 'page', 'size']);
  const [billingPeriodNo, setBillingPeriodNo] = useState<string | undefined>(
    () => {
      // Try initializing state with membershipBillingPeriodNo if available on first render
      return membershipBillingPeriodNo ?? undefined;
    }
  );
  const { batchProgressInfo, getRecursiveProgressStatus } = usePollRequest({
    pollRequestAPI: 'pollRequestActive',
    requestType: 'MEM',
    key: billingPeriodNo,
  });

  const [calculateInProgress, setcalculateInProgress] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const {
    memberShipBillingDetail,
    methodApi,
    getApi,
    membershipBillingOrganizationDetails,
  } = useJavaApi(['memberShipBillingDetail'], {
    membershipBillingPeriodNo: String(billingPeriodNo),
  });

  useEffect(() => {
    // Only update state if membershipBillingPeriodNo is defined
    // and state has not been set before or differs from current value
    if (
      membershipBillingPeriodNo &&
      membershipBillingPeriodNo !== billingPeriodNo
    ) {
      setBillingPeriodNo(membershipBillingPeriodNo);
    }
  }, [membershipBillingPeriodNo, billingPeriodNo]);

  function transformString(str: string) {
    const stringWithUnderscores = str.replace(/\s/g, '_');

    const stringWithoutHyphens = stringWithUnderscores.replace(/-/g, '');

    return stringWithoutHyphens;
  }

  const handleCalculate = (apiRoute: any) => {
    setcalculateInProgress(true);
    methodApi(apiRoute, {
      method: 'POST',
      data: billingPeriodNo,
      onSuccess: async () => {
        await getRecursiveProgressStatus({
          showProgressMessage: true,
          showSuccessMessage: true,
          key: billingPeriodNo ?? null,
          requestType: 'MEM',
        }).then(() => {
          getApi('memberShipBillingDetail', {
            membershipBillingPeriodNo: String(billingPeriodNo),
          }).then(() => {
            setcalculateInProgress(false);
          });
        });
      },
      onError: (err) => {
        showToast({
          title: err?.data?.messages?.[0]?.message
            ? err.data.messages[0].message
            : err.data.message,
          status: 'error',
        });
        setcalculateInProgress(false);
      },
    });
  };

  const handleClickViewOrganization = (id: any) => {
    getApi(
      'membershipBillingOrganizationDetails',
      {
        membershipBillingPeriodNo: String(billingPeriodNo),
        orgNo: id,
      },
      {
        onSuccess: (res) => {
          setIsModalOpen(true);
        },
        onError: (err) => {
          console.log(err);
        },
      }
    );
  };

  const orgData = useMemo(
    () =>
      membershipBillingOrganizationDetails?.payload?.memberSummaryDtoList || [],
    [membershipBillingOrganizationDetails]
  );

  const calculateButtonState = useMemo(
    () =>
      memberShipBillingDetail?.membershipBillingSummaryDto?.length > 0 ||
      memberShipBillingDetail?.processStatus === STATUS.FINALIZED ||
      memberShipBillingDetail?.processStatus === STATUS.INVOICED ||
      calculateInProgress,
    [
      calculateInProgress,
      memberShipBillingDetail?.membershipBillingSummaryDto?.length,
      memberShipBillingDetail?.processStatus,
    ]
  );

  const { column, columnOrgSummary } = useMembershipColumns({
    handleAction: handleClickViewOrganization,
    orgData: membershipBillingOrganizationDetails,
    memberShipBillingDetail:
      memberShipBillingDetail?.membershipBillingSummaryDto || [],
  });

  const handleFinalize = () => {
    methodApi('finalizeMemberShipBillingDetail', {
      data: billingPeriodNo,
      onSuccess: (res) => {
        showToast({
          title: 'Finalization In Progress. Please wait...',
          status: 'success',
        });
        router.refresh();
      },
      onError: (err) => {
        showToast({
          title: err.data.message,
          status: 'error',
        });
      },
    });
  };

  const disableFinalizeButton =
    !memberShipBillingDetail?.calculated ||
    memberShipBillingDetail?.processStatus === 'FINALIZED' ||
    memberShipBillingDetail?.processStatus === 'INVOICED';

  const handleDownload = async (billingPeriodNo: any) => {
    let errorShown = false;

    methodApi('exportFileDownload', {
      method: 'GET',
      restParams: { billingPeriodNo },

      onSuccess: (dataSuccess: string) => {
        const vendorName = transformString(
          memberShipBillingDetail?.vendorName as string
        );
        const startDate = transformString(
          memberShipBillingDetail?.membershipBillingDateFrom
        );
        const endDate = transformString(
          memberShipBillingDetail?.membershipBillingDateTo
        );

        handleFileDownload(
          dataSuccess,
          'application/text',
          `Membership_Billing_${vendorName}_${startDate}_${endDate}.csv`
        );
      },

      onError: (e) => {
        if (!errorShown) {
          errorShown = true;
          showToast({
            title: 'ERROR',
            description: e.data.message,
            status: 'error',
          });
        }
      },
    });
  };

  const dateFormat = (date: string): string => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      timeZone: 'UTC',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  return (
    <Box>
      <Flex alignItems={'center'} gap={0} marginBottom={'10px'}>
        <BackButton link="" label="" labelFontSize="18px" />
        <Heading
          style={{
            color: '#4c6293',
            fontWeight: 600,
            fontSize: 18,
          }}
        >
          {' '}
          {memberShipBillingDetail
            ? `${
                memberShipBillingDetail.vendorName
              } Membership Summary for ${dateFormat(
                memberShipBillingDetail.membershipBillingDateFrom
              )} - ${dateFormat(
                memberShipBillingDetail.membershipBillingDateTo
              )}`
            : 'Membership Summary'}
        </Heading>
      </Flex>
      <Box>
        <Flex
          alignItems="center"
          whiteSpace="nowrap"
          justifyContent={'space-between'}
          mb={6}
        >
          <Flex alignItems="center" whiteSpace="nowrap" gap={8}>
            <Flex>
              <Text
                mr={2}
                style={{
                  color: '#4c6293',
                  fontWeight: 600,
                  fontSize: 16,
                }}
              >
                Period Start Date:
              </Text>

              {dateFormat(memberShipBillingDetail?.membershipBillingDateFrom)}
            </Flex>

            <Flex>
              <Text
                mr={2}
                style={{
                  color: '#4c6293',
                  fontWeight: 600,
                  fontSize: 16,
                }}
              >
                Period End Date:
              </Text>

              {dateFormat(memberShipBillingDetail?.membershipBillingDateTo)}
            </Flex>
            <Button
              style={{
                ...buttonStyling,
                cursor: calculateButtonState ? 'not-allowed' : 'pointer',
              }}
              isDisabled={calculateButtonState || batchProgressInfo}
              onClick={() => handleCalculate('calculateFeesByOrganization')}
            >
              Calculate
            </Button>
          </Flex>
          <Text
            style={{
              color: '#4c6293',
              fontWeight: 600,
              fontSize: 16,
              //   marginBottom: 10,
              textAlign: 'right',
              //   marginLeft: '40rem',
              textTransform: 'capitalize',
            }}
          >
            Status: {memberShipBillingDetail?.processStatus?.toLowerCase()}
          </Text>
        </Flex>
        <Box
          sx={{
            '& > div:first-child': {
              paddingBottom: '28px',
              paddingTop: '12px',
            },
          }}
        >
          {batchProgressInfo && (
            <BatchProgress
              progressInfo={batchProgressInfo}
              onCompletion={() => console.log('Batch processing complete')}
              error={null}
            />
          )}
        </Box>

        <Flex width="100%" justifyContent="center">
          <Text fontSize="md" as="i" fontWeight="bold" color="#4c6293">
            Counts By Organization
          </Text>
        </Flex>
        <Box sx={tableContainer}>
          <Table
            minWidth={'900px'}
            columns={column}
            data={
              paginateData(
                parseInt(page),
                parseInt(size),
                memberShipBillingDetail?.membershipBillingSummaryDto
              ) || []
            }
            headerBackground="#ececec"
            fontSize="sm"
            rowClick={() => undefined}
          />
        </Box>
        <Pagination
          totalCount={
            memberShipBillingDetail?.membershipBillingSummaryDto?.length
          }
        />
        <Flex justifyContent="space-between" p={2}>
          <AlertBox
            alertButtonText={'Recompute'}
            headerText="Confirmation"
            approveButtonHandler={() =>
              handleCalculate('reCalculateFeesByOrganization')
            }
            bodyText={['Would you like to Recompute?']}
            style={buttonStyling}
            isButtonDisabled={
              memberShipBillingDetail?.membershipBillingSummaryDto?.length ===
                0 ||
              memberShipBillingDetail?.processStatus === STATUS.FINALIZED ||
              memberShipBillingDetail?.processStatus === STATUS.INVOICED
            }
            approveButtonText="OK"
            rejectButtonText="Cancel"
          />
          <AlertBox
            alertButtonText={'Finalize'}
            headerText="Confirmation"
            approveButtonHandler={handleFinalize}
            isButtonDisabled={disableFinalizeButton}
            style={buttonStyling}
            bodyText={[
              'Would you like to Finalize the Membership Billing Period?',
            ]}
            approveButtonText="OK"
            rejectButtonText="Cancel"
          />
        </Flex>
        {memberShipBillingDetail?.exportFileExists && (
          <Button
            variant="link"
            m={2}
            onClick={() => handleDownload(billingPeriodNo)}
          >
            Membership Summary Export
          </Button>
        )}
        <GenericModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={`${membershipBillingOrganizationDetails?.payload?.organizationName} Summary`}
          headerSize={16}
          size="lg"
        >
          <Flex
            width="100%"
            justifyContent="center"
            style={{
              color: '#4c6293',
              fontWeight: 600,
              fontSize: 14,
            }}
          >
            {`${membershipBillingOrganizationDetails?.payload?.organizationName}: ${membershipBillingOrganizationDetails?.payload?.membershipBillingPeriodDateFrom} - ${membershipBillingOrganizationDetails?.payload?.membershipBillingPeriodDateTo}`}
          </Flex>
          <Box boxShadow="lg">
            <CustomReactTable
              columns={columnOrgSummary}
              data={orgData}
              footer={true}
              footerStyle={{
                textAlign: 'start',
                paddingLeft: '1.5rem',
              }}
              sortFilter={['carrier', 'groupName', 'groupNumber']}
            />
          </Box>
          <Flex gap={2} mt={4}>
            <FaFilePdf
              size="22px"
              color="#F40F02"
              cursor={'pointer'}
              onClick={() =>
                downloadPDF(membershipBillingOrganizationDetails, orgData)
              }
            />
            <RiFileExcel2Fill
              size="25px"
              color="#1D6F42"
              cursor={'pointer'}
              onClick={() =>
                downloadXLSX(membershipBillingOrganizationDetails, orgData)
              }
            />
          </Flex>
        </GenericModal>
      </Box>
    </Box>
  );
};

export default MembershipBilligDetail;
