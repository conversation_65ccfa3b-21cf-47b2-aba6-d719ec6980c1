//************************DO NOT EDIT WITHOUT NOTIFYING Sam or Ken  **********************************
import { getAccessToken, withApiAuthRequired } from '@auth0/nextjs-auth0';
import { getLogger, getUser } from '@next/shared/logger';
import { ProviderSetup } from '@next/shared/tracer';
import { context, trace } from '@opentelemetry/api';
import axios, { Method } from 'axios';
import * as formidable from 'formidable';
import * as fs from 'fs';
import { NextApiRequest, NextApiResponse } from 'next';
import fetch, { Blob, FormData } from 'node-fetch';
export type RequestBody = {
  sendType?:
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'
    | 'blob';
  method: Method;
  body?: Record<string, any>;
  route: string;
};

const logger = getLogger();

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withApiAuthRequired(async function call(
  req: NextApiRequest,
  res: NextApiResponse
) {
  ProviderSetup();
  const span = trace.getSpan(context.active());

  try {
    const { accessToken } = await getAccessToken(req, res);
    console.log(accessToken);
    if (!accessToken) {
      logger.error('no access token');
      res.status(401).end(JSON.stringify({ message: 'no access token' }));
      return false;
    }

    const username = getUser(accessToken);
    // logger = getLogger(username);
    span?.setAttribute('app.user', username);

    const reqData: any = await getFormData(req);
    const formattedData = formatData(reqData);
    span?.setAttribute('http.target_route', formattedData.fields.route);
    span?.setAttribute('http.target_method', formattedData.fields.method);

    const response: any = await axiosRequest(accessToken, formattedData);

    if (response.internalError) {
      res.status(response.status).send(response.statusText);
      return;
    }
    if (formattedData?.fields?.returnHeaders)
      res.setHeader(
        'content-dispositon',
        response?.headers?.['content-disposition']
      );
    if (response?.data || (response instanceof Object && 'data' in response)) {
      res.send(response.data);
    } else {
      res.send(response);
    }
    // res.send(response.data || response);
    span?.end();
  } catch (error) {
    logger.error(error);
    res.status(401).end(JSON.stringify({ error: 'authentication error' }));
    span?.end();
  }
});

const formatData = (data: any) => {
  if (typeof data.fields.method === 'string') return data;
  const newData = data;
  const formattedData: Record<string, string> = {};
  const keys = Object.keys(newData.fields);
  for (let i = 0; i < keys.length; i++) {
    const val = newData.fields[keys[i]][0];
    formattedData[keys[i]] = val;
  }
  newData.fields = formattedData;
  return newData;
};

const getFormData = async (req: any) => {
  const data = await new Promise((resolve, reject) => {
    const form = new formidable.Formidable();

    form.parse(req, (err, fields, files) => {
      if (err) reject({ err });
      resolve({ err, fields, files });
    });
  });
  return data;
};

const axiosRequest = async (accessToken: string, requestData: any) => {
  const {
    route: url,
    method = 'GET',
    body,
    sendType = 'application/json',
    ...rest
  } = requestData.fields;
  const span = trace.getSpan(context.active());
  const traceContext = span?.spanContext();
  let sendBody = body;
  const headers: Record<string, any> = {
    Application: 'authorization',
    Authorization: 'Bearer ' + accessToken,
    traceparent: `00-${traceContext?.traceId}-${traceContext?.spanId}-01`,
  };
  if (sendType === 'blob') {
    headers.responseType = 'arraybuffer';
  }
  if (sendType === 'multipart/form-data') {
    sendBody = { ...rest };
    const form = new FormData();
    const keys = Object.keys(sendBody);
    const fileKeys = Object.keys(requestData.files);

    for (let i = 0; i < keys.length; i++) {
      form.append(keys[i], sendBody[keys[i]]);
    }

    if (fileKeys && fileKeys.length) {
      for (let i = 0; i < fileKeys.length; i++) {
        const buffer = fs.readFileSync(
          requestData.files[fileKeys[i]][0].filepath
        );
        const blob = new Blob([buffer]);
        form.append(
          fileKeys[i],
          blob,
          requestData.files[fileKeys[0]][0]?.originalFilename
        );
      }
    }
    const body = form;

    try {
      const response = await fetch(
        `${process.env.SERVICE_URL}/admin-portal/v1/${url}`,
        {
          method: method,
          body,
          headers: headers,
        }
      );
      const text = await response.text(); // Parse it as text
      const data = JSON.parse(text);
      return (await data) || response;
    } catch (e: any) {
      logger.error(e);
      e.response.internalError = true;
      if (e.response) {
        return e.response;
      }
      return e;
    }
  } else {
    headers['Content-Type'] = sendType;
    headers[
      'traceparent'
    ] = `00-${traceContext?.traceId}-${traceContext?.spanId}-01`;
  }

  try {
    const response = await axios.request({
      url: `${process.env.SERVICE_URL}/admin-portal/v1/${url}`,
      method,
      data: sendBody,
      headers,
      responseType: headers.responseType,
    });
    return response;
  } catch (e: any) {
    logger.error(e);
    e.response.internalError = true;
    if (e.response.data) {
      return e.response.data;
    }
    if (e.response) {
      return e.response;
    }
    return e;
  }
};
