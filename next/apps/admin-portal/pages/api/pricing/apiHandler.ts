import { getAccessToken, withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';

interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
}

const ENV_BASE_URLS = {
  dev: 'https://edpm.dev.rxbenefits.cloud/v1',
  qa: 'https://edpm.qa.rxbenefits.cloud/v1',
};

const getBaseUrl = (env: string) => {
  switch (env) {
    case 'dev':
      return ENV_BASE_URLS.dev;

    case 'qa':
      return ENV_BASE_URLS.qa;

    default:
      return ENV_BASE_URLS.qa;
  }
};

const getEnvironment = (host: string) => {
  // Determine environment from hostname
  let env = 'qa';

  if (host.includes('prod') || host.includes('www')) {
    env = 'prod';
  } else if (host.includes('stage')) {
    env = 'stage';
  } else if (host.includes('dev') || host.includes('localhost')) {
    env = 'dev';
  } else if (host.includes('demo')) {
    env = 'demo';
  }
  return env;
};

const callApi = async (
  req: NextApiRequest,
  res: NextApiResponse,
  endpoint: string,
  options: RequestOptions = {}
) => {
  const { accessToken } = await getAccessToken(req, res);
  const headers: Record<string, any> = {
    Application: 'authorization',
    Authorization: 'Bearer ' + accessToken,
  };

  const host = req.headers.host || '';
  const environment = getEnvironment(host);

  const baseUrl = getBaseUrl(environment);

  const response = await fetch(`${baseUrl}/${endpoint}`, {
    ...options,
    headers: {
      ...(options.headers || {}),
      ...headers,
      'Content-Type': 'application/json',
    },
  });

  const contentType = response.headers.get('content-type');

  if (!response.ok) {
    const errorBody = await response.json();
    const error = new Error(errorBody.message || `Error ${response.status}`);
    (error as any).status = response.status; // Add status to error
    (error as any).details = errorBody.details; // Add details to error
    throw error;
  }

  return contentType?.includes('application/json')
    ? await response.json()
    : await response.blob();
};

export const createApiHandler = (handlers: {
  [method: string]: {
    getEndpoint: (req: NextApiRequest) => string;
  };
}) => {
  return withApiAuthRequired(
    async (req: NextApiRequest, res: NextApiResponse) => {
      const methodHandler = handlers[req.method ?? ''];

      if (!methodHandler) {
        return res
          .status(405)
          .json({ error: `Method ${req.method} not allowed` });
      }

      try {
        const endpoint = methodHandler.getEndpoint(req);
        const hasBody =
          ['POST', 'PUT', 'PATCH'].includes(req.method ?? '') && req.body;
        const result = await callApi(req, res, endpoint, {
          method: req.method,
          ...(hasBody ? { body: JSON.stringify(req.body) } : {}),
        });

        res.status(200).json(result);
      } catch (error: any) {
        res
          .status(error.status || 500)
          .json({ message: error.message, details: error.details });
      }
    }
  );
};
