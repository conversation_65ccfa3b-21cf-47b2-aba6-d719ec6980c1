import { useUser } from '@auth0/nextjs-auth0/client';
import {
  Box,
  ButtonGroup,
  Container,
  Divider,
  IconButton,
  Image,
  Link,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { STATIC_MY_RXB_LOGO_URL } from '@next/shared/constants';
import { AdminUserContext } from '@next/shared/contexts';
import router from 'next/router';
import { useContext, useEffect } from 'react';
import { FaFacebook, FaLinkedin, FaTwitter } from 'react-icons/fa';

import { ChatBotScript } from './ChatBotScript';
import TimeoutModal from './TimeoutModal';

export const Footer = () => {
  const { userState, missingCommPrefIndicator } = useContext(AdminUserContext);
  const { user, isLoading } = useUser();
  const memberID = userState?.metaData?.data?.[0].alternateId;
  const firstName = userState?.metaData?.data?.[0].firstName;
  const lastName = userState?.metaData?.data?.[0].lastName;
  const memberDOB = userState?.metaData?.data?.[0].birthdate;
  const email = userState?.email;
  const { onOpen, isOpen, onClose } = useDisclosure();

  useEffect(() => {
    let inactivityTimer: any;

    const handleInactivity = () => {
      inactivityTimer = setTimeout(() => {
        onOpen();
      }, 13 * 60 * 1000);
    };

    handleInactivity();

    const resetInactivityTimer = () => {
      clearTimeout(inactivityTimer);
      handleInactivity();
    };

    window.addEventListener('mousemove', resetInactivityTimer);
    window.addEventListener('keydown', resetInactivityTimer);

    return () => {
      window.removeEventListener('mousemove', resetInactivityTimer);
      window.removeEventListener('keydown', resetInactivityTimer);
      clearTimeout(inactivityTimer);
    };
  }, [onOpen]);

  if (
    (!isLoading && !user?.email_verified) ||
    (!isLoading && missingCommPrefIndicator) ||
    (!isLoading && router.pathname.includes('confirm-privacy-policy')) ||
    (!isLoading && router.pathname.includes('sso-error'))
  ) {
    return;
  }

  return (
    <Box py={4} borderTop="3px solid" borderColor="blue.400">
      <Container py={4} maxW="1600px">
        <Stack spacing={{ base: '4', md: '5' }}>
          <Stack
            justify="space-between"
            direction={['column', 'column', 'column', 'row']}
            align="center"
            py={2}
          >
            <Link href="/home">
              <Image
                src={STATIC_MY_RXB_LOGO_URL}
                alt="Company Logo"
                h="2.5em"
              />
            </Link>
            <ButtonGroup variant="ghost" color="blue.700">
              <IconButton
                as="a"
                target="_blank"
                href="https://www.facebook.com/RxBenefits/"
                aria-label="Facebook"
                icon={<FaFacebook fontSize="1.25rem" />}
              />
              <IconButton
                as="a"
                target="_blank"
                href="https://www.linkedin.com/company/rxbenefits-inc-"
                aria-label="LinkedIn"
                icon={<FaLinkedin fontSize="1.25rem" />}
              />
              <IconButton
                as="a"
                target="_blank"
                href="https://twitter.com/RxBenefits?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor"
                aria-label="Twitter"
                icon={<FaTwitter fontSize="1.25rem" />}
              />
            </ButtonGroup>
          </Stack>
          <Divider borderColor="gray.600" />
          <Text
            fontSize={['2xs', '2xs', 'xs']}
            color="subtle"
            textAlign={['center', 'left']}
          >
            Copyright &copy; {new Date().getFullYear()} RxBenefits, Inc. All
            rights reserved.{' '}
            <Link
              textDecoration="underline"
              href="https://www.rxbenefits.com/privacy-policy/terms-of-use/"
              target="_blank"
            >
              Terms of Use
            </Link>
            <Link
              p={2}
              textDecoration="underline"
              href="https://www.rxbenefits.com/privacy-policy/"
              target="_blank"
            >
              Privacy Policy
            </Link>
          </Text>
        </Stack>
      </Container>
      <TimeoutModal isOpen={isOpen} onClose={onClose} />
      {firstName !== undefined && memberID !== undefined && (
        <ChatBotScript
          name={`${firstName} ${lastName}`}
          memberID={memberID}
          memberDOB={memberDOB}
          email={email}
        />
      )}
    </Box>
  );
};
