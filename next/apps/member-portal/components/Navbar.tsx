import { useUser } from '@auth0/nextjs-auth0/client';
import {
  Avatar,
  Badge,
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Image,
  Link,
  Menu,
  MenuButton,
  MenuDivider,
  MenuItem,
  MenuList,
  Stack,
  Text,
  useColorModeValue,
  useDisclosure,
} from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { STATIC_MY_RXB_LOGO_URL } from '@next/shared/constants';
import { AdminUserContext } from '@next/shared/contexts';
import { usePathname } from 'next/navigation';
import router from 'next/router';
import { ReactNode, useContext, useEffect } from 'react';
import { FaBars, FaWindowClose } from 'react-icons/fa';

import { useMemberData } from '../hooks/useMemberData';
import { useImpersonationStore } from '../stores/useImpersonationStore';
import { useOnboardingStore } from '../stores/useOnboardingStore';
import { logImpersonationEnd } from '../utils/logImpersonationEnd';
import RXBuddy from './RXBuddy';
import NavBarSkeleton from './Skeletons/navbar';

const Links = [
  { title: 'Home', url: '/home' },
  { title: 'Claims', url: '/claims' },
  { title: 'Prior Authorizations', url: '/prior-auth' },
];

const NavLink = ({
  url,
  children,
  currentRoute,
}: {
  url: string;
  children: ReactNode;
  currentRoute: string;
}) => (
  <Link
    px={4}
    py={1}
    rounded="3xl"
    _hover={{
      textDecoration: 'none',
      bg: useColorModeValue('gray.200', 'gray.700'),
    }}
    backgroundColor={url === currentRoute ? 'gray.200' : ''}
    fontWeight="bold"
    href={url}
    color="blue.700"
  >
    {children}
  </Link>
);

export default function Navbar() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const pathname = usePathname();
  const { user, isLoading } = useUser();
  const { missingCommPrefIndicator } = useContext(AdminUserContext);
  const { isAdmin } = useMemberData();
  const { clearImpersonation } = useImpersonationStore();

  const {
    firstName,
    lastName,
    isDependent,
    employeeNo,
    organizationNo,
    dependentNo,
    // impMember,
  } = useMemberData();

  const { getIsProtect, getApi } = useApi();
  const { firstLogin } = useOnboardingStore();

  useEffect(() => {
    if (organizationNo) {
      getApi('getIsProtect', { orgId: organizationNo });
    }
  }, [organizationNo, getApi]);

  if (
    (!isLoading && !user?.email_verified) ||
    (!isLoading && router.pathname.includes('onboarding')) ||
    (!isLoading && router.pathname.includes('confirm-privacy-policy')) ||
    (!isLoading && router.pathname.includes('sso-error')) ||
    (!isLoading &&
      user &&
      user?.['https://rxbenefits.com/isAdmin'] &&
      router.pathname.includes('/admin'))
  ) {
    return null;
  }

  return (
    <>
      <Box
        px={4}
        position="fixed"
        w="100%"
        bg="white"
        zIndex={9}
        mt={isAdmin ? ['5.5em', '5.5em', '2.5em'] : ''}
      >
        <Flex h={20} alignItems={'center'} justifyContent={'space-between'}>
          <IconButton
            size={['sm', 'md']}
            icon={isOpen ? <FaWindowClose /> : <FaBars />}
            aria-label={'Open Menu'}
            display={{ md: 'none' }}
            background="none"
            _hover={{ background: 'none' }}
            onClick={isOpen ? onClose : onOpen}
          />
          <HStack spacing={8} alignItems={'center'}>
            <Link href="/home">
              <Image
                src={STATIC_MY_RXB_LOGO_URL}
                alt="My RxBenefits Logo"
                maxH={['2em', '2.5em']}
              />
            </Link>
            <HStack
              as={'nav'}
              spacing={4}
              display={{ base: 'none', md: 'flex' }}
            >
              {Links.map((link) => {
                if (
                  link.url === '/prior-auth' &&
                  !getIsProtect?.protectClient
                ) {
                  return null;
                }
                return (
                  <NavLink
                    key={link.url}
                    url={link.url}
                    currentRoute={pathname}
                  >
                    {link.title}
                  </NavLink>
                );
              })}
            </HStack>
          </HStack>
          <Flex alignItems={'center'}>
            <Menu>
              {firstName === undefined && lastName === undefined ? (
                <NavBarSkeleton />
              ) : (
                <>
                  {router.pathname === '/home' && (
                    <RXBuddy
                      isProtect={getIsProtect?.protectClient}
                      organizationNo={organizationNo}
                      employeeNo={employeeNo}
                      dependentNo={dependentNo}
                      newLogin={firstLogin}
                    />
                  )}
                  <MenuButton
                    as={Button}
                    rounded={'full'}
                    variant={'link'}
                    cursor={'pointer'}
                    minW={0}
                    display="flex"
                    data-dd-action-name="Profile"
                  >
                    <Box position="relative">
                      <Avatar
                        size={['sm', 'md']}
                        name={
                          (firstName && lastName) !== null
                            ? `${firstName} ${lastName}`
                            : undefined
                        }
                        bg="#36c48b"
                        color="white"
                        fontWeight="bold"
                      />
                      {missingCommPrefIndicator && (
                        <Badge
                          colorScheme="red"
                          variant="solid"
                          borderRadius="full"
                          boxSize="8px"
                          position="absolute"
                          width={'10px'}
                          height={['12px', '12px', '12px', '12px', '10px']}
                          top="4px"
                          right="0"
                          border="2px solid white"
                        />
                      )}
                    </Box>
                  </MenuButton>
                  <Box
                    fontSize="sm"
                    pl={2}
                    display={['none', 'none', 'none', 'block']}
                  >
                    <Text data-dd-privacy="allow">{`${firstName} ${lastName}`}</Text>
                    <Text color="gray.500" data-dd-privacy="allow">
                      {isDependent ? 'Dependent' : 'Primary Insured'}
                    </Text>
                  </Box>
                  <MenuList>
                    <MenuItem
                      as={Link}
                      href="/account"
                      _hover={{ textDecoration: 'none' }}
                      className="dd-privacy-allow"
                      position="relative"
                    >
                      <Box display="inline-block">
                        Account Settings
                        {missingCommPrefIndicator && (
                          <Badge
                            colorScheme="red"
                            variant="solid"
                            borderRadius="full"
                            boxSize="8px"
                            position="absolute"
                            top="6px"
                            right="80px"
                          />
                        )}
                      </Box>
                    </MenuItem>
                    <MenuDivider />
                    <MenuItem
                      onClick={async () => {
                        if (isAdmin) {
                          await logImpersonationEnd(user?.email || 'unknown');
                          clearImpersonation();
                        }
                        router.push('/api/auth/logout');
                      }}
                      _hover={{ textDecoration: 'none' }}
                      className="dd-privacy-allow"
                    >
                      Log Out
                    </MenuItem>
                  </MenuList>
                </>
              )}
            </Menu>
          </Flex>
        </Flex>

        {isOpen ? (
          <Box pb={4} display={{ md: 'none' }}>
            <Stack as={'nav'} spacing={4}>
              {Links.map((link) => (
                <NavLink url={link.url} currentRoute={pathname} key={link.url}>
                  {link.title}
                </NavLink>
              ))}
            </Stack>
          </Box>
        ) : null}
      </Box>
    </>
  );
}
