'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
const LoginGuard = ({
  children,
  datadogRum,
}: {
  children: any;
  datadogRum: any;
}) => {
  const { user, isLoading } = useUser();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const safePages = ['/login', '/sso-error'];
  const isSafePage = safePages.includes(pathname);

  useEffect(() => {
    const org = searchParams?.get('org');
    if (!user && !isLoading && !isSafePage) {
      router.push(`/api/auth/login${org ? `?org=${org}` : ''}`);
    }
  }, [router, user, isLoading, pathname, searchParams, isSafePage]);

  useEffect(() => {
    if (!datadogRum || !user?.email) return;
    datadogRum?.setUser({
      id: user.email,
      email: user.email,
    });
  }, [datadogRum, user]);

  if (isLoading) return null;
  if (!user && !isSafePage) return null;
  return children;
};
export default LoginGuard;
