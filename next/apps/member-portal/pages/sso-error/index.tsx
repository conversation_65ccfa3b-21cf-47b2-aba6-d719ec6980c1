import { WarningTwoIcon } from '@chakra-ui/icons';
import { Box, ChakraProvider, Heading, Image, Text } from '@chakra-ui/react';
import { theme } from '@next/member/constants';
import { ILLUMINATE_RXB_LOGO } from '@next/shared/constants';
import { useEffect } from 'react';

const SsoError = () => {
  useEffect(() => {
    console.warn('SSO login failure encountered');
  }, []);

  return (
    <>
      <Box bg="white" minH="100vh">
        {/* Header */}
        <Box bg="#11181C" px={4} py={4} width="100%" textAlign="center">
          <Image
            src={ILLUMINATE_RXB_LOGO}
            alt="Illuminate Rx Logo"
            height="60px"
            mx="auto"
          />
        </Box>

        {/* Main Content */}
        <Box
          pt="78px"
          px="24px"
          textAlign="center"
          color="gray.600"
          display="flex"
          flexDirection="column"
          alignItems="center"
        >
          <WarningTwoIcon boxSize={20} color="#FFB71A" mb={6} mt={6} />

          <Heading as="h2" fontSize="20px" mb={4} padding="6px">
            Something went wrong
          </Heading>

          <Text fontSize="16px" maxW="500px" padding="6px">
            We’re unable to log you in to Illuminate Rx at this time.
            <br />
            Please try again later, use the chatbot in your member portal, or
            call{' '}
            <Text as="span" color="gray.700">
              (866) 683-5224
            </Text>{' '}
            if you need immediate assistance.
          </Text>
        </Box>
      </Box>
    </>
  );
};

export default function SsoErrorPageWrapper() {
  return (
    <ChakraProvider theme={theme}>
      <SsoError />
    </ChakraProvider>
  );
}
