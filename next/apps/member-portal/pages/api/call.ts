//************************DO NOT EDIT WITHOUT NOTIFYING Sam or Ken  **********************************
import { getAccessToken, withA<PERSON><PERSON>uthRequired } from '@auth0/nextjs-auth0';
import { getLogger, getUser } from '@next/shared/logger';
import { ProviderSetup } from '@next/shared/tracer';
import { context, trace } from '@opentelemetry/api';
import axios, { AxiosResponse } from 'axios';
import { NextApiRequest, NextApiResponse } from 'next';

let logger = getLogger();
const METHODS = {
  POST: axios.post,
  GET: axios.get,
  PATCH: axios.patch,
  DELETE: axios.delete,
  PUT: axios.put,
};
export type RequestBody = {
  sendType?:
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'
    | 'blob';
  method: keyof typeof METHODS;
  body?: Record<string, any>;
  route?: string;
};
export default withApiAuthRequired(async function call(
  req: NextApiRequest,
  res: NextApiResponse
) {
  ProviderSetup();
  try {
    const span = trace.getActiveSpan();
    const { accessToken } = await getAccessToken(req, res);
    if (!accessToken) {
      logger.error('no access token');
      res.status(401).end(JSON.stringify({ message: 'no access token' }));
      return;
    }
    console.log(accessToken);
    console.log('Method:', req.method);
    console.log('URL:', req.url);
    console.log('Headers:', req.headers);
    // console.log(JSON.stringify(req));
    const username = getUser(accessToken);
    logger = getLogger(username);
    span?.setAttribute('app.user', username);

    const formattedBody = { ...req.body };
    span?.setAttribute('http.target_route', formattedBody.route);
    span?.setAttribute('http.target_method', formattedBody.method);
    if (!req.body.method || req.body.method === 'GET') {
      formattedBody.method = 'GET' as keyof typeof METHODS;
      const returnVal = await axiosCall(accessToken, formattedBody);
      if (isAxiosResponse(returnVal)) {
        res.send(returnVal.data);
      } else {
        logger.error(
          `Unexpected return type from axiosCall: ${typeof returnVal}`
        );
        res.status(500).end(JSON.stringify({ error: 'server error' }));
      }
    } else {
      const returnVal = await axiosPost(accessToken, formattedBody);
      if (isAxiosResponse(returnVal)) {
        res.send(returnVal.data);
      } else if (typeof returnVal === 'number') {
        res.status(returnVal).end(); // Handle status codes
      } else {
        logger.error(
          `Unexpected error from axiosPost: ${JSON.stringify(returnVal.data)}`
        );
        res.status(500).end(JSON.stringify({ error: 'server error' }));
      }
    }
  } catch (error) {
    logger.error(error);
    res.status(500).end(JSON.stringify({ error: 'server error' }));
  }
});

// Type guard to check if the return value is an AxiosResponse
function isAxiosResponse(value: any): value is AxiosResponse {
  return value && typeof value === 'object' && 'data' in value;
}

// The axiosCall function (handles all HTTP methods)
const axiosCall = async (
  accessToken: string,
  data: RequestBody
): Promise<AxiosResponse<any> | { data: any }> => {
  const span = trace.getSpan(context.active());
  const traceContext = span?.spanContext();
  const sendType = data.sendType || 'application/json';
  try {
    const response = await METHODS[data.method](
      `${process.env.SERVICE_URL}/admin-portal/v1/${data.route}`,
      {
        headers: {
          'Content-Type': sendType,
          Application: 'authorization',
          Authorization: 'Bearer ' + accessToken,
          traceparent: `00-${traceContext?.traceId}-${traceContext?.spanId}-01`,
        },
      }
    );
    return response;
  } catch (error: any) {
    logger.error(
      `An error occurred calling ${process.env.SERVICE_URL}/admin-portal/v1/${data.route}`
    );
    logger.error(error);
    return { data: error };
  }
};

const axiosPost = async (
  accessToken: string,
  data: Record<string, string>
): Promise<AxiosResponse<any> | number | { data: any }> => {
  const span = trace.getSpan(context.active());
  const traceContext = span?.spanContext();
  const sendType = data.sendType || 'application/json';
  const requestBody = data.body;
  logger.debug({
    'Content-Type': sendType,
    Application: 'authorization',
    type: 'POST',
    // Authorization: `Bearer ${accessToken}`,
    url: `${process.env.SERVICE_URL}/admin-portal/v1/${data.route}`,
  });
  const headers: Record<string, any> = {
    headers: {
      'Content-Type': sendType,
      Application: 'authorization',
      Authorization: 'Bearer ' + accessToken,
      traceparent: `00-${traceContext?.traceId}-${traceContext?.spanId}-01`,
    },
  };
  if (sendType === 'blob') {
    headers.responseType = 'arraybuffer';
  }
  try {
    if (data.method.toLowerCase() === 'delete') {
      const config = {
        headers: headers.headers,
        data: requestBody,
      };
      const response = await axios.delete(
        `${process.env.SERVICE_URL}/admin-portal/v1/${data.route}`,
        config
      );

      return response.status;
    }

    const response = await axios.post(
      `${process.env.SERVICE_URL}/admin-portal/v1/${data.route}`,
      requestBody,
      headers
    );
    return response;
  } catch (error: any) {
    logger.error(
      `An error occurred calling ${process.env.SERVICE_URL}/admin-portal/v1/${data.route}`
    );
    logger.error(error);
    return { data: error };
  }
};
