import { UserProvider } from '@auth0/nextjs-auth0/client';
import { Box, ChakraProvider, Container } from '@chakra-ui/react';
import { datadogRum } from '@datadog/browser-rum';
import { theme } from '@next/member/constants';
import { STATIC_GRADIENT_HERO_URL } from '@next/shared/constants';
import { AdminUserExtendedProvider } from '@next/shared/contexts';
import axios from 'axios';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Footer } from '../components/Footer';
import ImpersonationBanner from '../components/ImpersonationBanner';
import LoginGuard from '../components/LoginGuard';
import Navbar from '../components/Navbar';
import { RegisterQualtrix } from '../components/RegisterQualtrix';

const datadogRumInit = async () => {
  let environment = 'unknown';
  try {
    const response = await axios.get('/api/getConfig');
    environment = response.data.env;
  } catch (error) {
    console.error('error fetching config');
    console.error(error);
  }

  if (environment !== 'local') {
    datadogRum.init({
      applicationId: process.env.DATADOG_APPLICATION_ID || '',
      clientToken: process.env.DATADOG_CLIENT_TOKEN || '',
      site: 'datadoghq.com',
      service: 'member-portal',
      env: environment,
      sessionSampleRate: 100,
      sessionReplaySampleRate: 100,
      trackUserInteractions: true,
      trackResources: true,
      trackLongTasks: true,
      defaultPrivacyLevel: 'mask',
    });
    datadogRum.startSessionReplayRecording();
  }
};
// If this code is running on the client side
if (typeof window !== 'undefined') {
  // Init user monitoring
  datadogRumInit();
}

export default function App({
  Component,
  pageProps,
  router,
}: {
  Component: any;
  pageProps: any;
  router: any;
}) {
  //   const { query } = useRouter();
  //   const { orgId, memberId } = query;
  const searchParams = useSearchParams();
  const orgId = searchParams?.get('orgId');
  const memberId = searchParams?.get('memberId');

  const [history, setHistory] = useState<string[]>([]);
  const [logged, setLogged] = useState<string>('');

  useEffect(() => {
    const newHistory = [...history];
    const isOld = newHistory.length;
    newHistory.push(router.pathname);
    setHistory(newHistory);
    if (!isOld && router.pathname === '/') {
      return;
    }
    setLogged(sessionStorage.getItem('logged') || '');
    if (
      !sessionStorage.getItem('logged') &&
      router.pathname !== '/CheckUserLogin' &&
      router.pathname !== '/sso-error'
    ) {
      navigator.sendBeacon('api/auth/logout');
      window.location.replace('/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.pathname]);

  if (
    !logged &&
    router.pathname !== '/CheckUserLogin' &&
    router.pathname !== '/' &&
    router.pathname !== '/sso-error'
  ) {
    return null;
  }

  return (
    <UserProvider>
      <LoginGuard datadogRum={datadogRum}>
        <AdminUserExtendedProvider>
          <ChakraProvider theme={theme}>
            <RegisterQualtrix>
              <ImpersonationBanner />
              <Navbar />
              <Box
                bgSize="100% 55%"
                bgAttachment="fixed"
                bgRepeat="no-repeat"
                bgImage={STATIC_GRADIENT_HERO_URL}
                pt="5em"
              >
                <Container maxW="1600px" py={12} px={[4, 4, 4, 6]}>
                  <Component {...pageProps} orgId={orgId} memberId={memberId} />
                </Container>
              </Box>
              <Footer />
            </RegisterQualtrix>
          </ChakraProvider>
        </AdminUserExtendedProvider>
      </LoginGuard>
    </UserProvider>
  );
}
