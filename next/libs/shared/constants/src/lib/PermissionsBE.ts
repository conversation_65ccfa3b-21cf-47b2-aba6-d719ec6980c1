import { PERM_NICE_NAME } from './Permissions';
// DO NOT Import to FE (This contains permissions names which should not be exposed)
// If a new permission is created map the permission key to PERM_NICE_NAME then add here.

export enum PERMISSIONS_ENUM {
  'authorization-view-users' = 'authorization-view-users',
  'authorization-add-users' = 'authorization-add-users',
  'portal-view-organizations' = 'portal-view-organizations',
  'portal-view-financial-invoice' = 'portal-view-financial-invoice',
  'portal-view-financial-claim-summary' = 'portal-view-financial-claim-summary',
  'portal-view-financial-claim-detail' = 'portal-view-financial-claim-detail',
  'portal-view-financial-invoice-claim-export' = 'portal-view-financial-invoice-claim-export',
  'portal-view-organization-users' = 'portal-view-organization-users',
  'portal-edit-organizations' = 'portal-edit-organizations',
  'portal-view-members' = 'portal-view-members',
  'portal-edit-members' = 'portal-edit-members',
  'portal-view-files' = 'portal-view-files',
  'portal-upload-files' = 'portal-upload-files',
  'portal-delete-files' = 'portal-delete-files',
  'reporting-view-reports' = 'reporting-view-reports',
  'authorization-developer' = 'authorization-developer',
  'protect-view-cds' = 'protect-view-cds',
  'portal-edit-eligibility' = 'portal-edit-eligibility',
  '340B-view-records' = '340B-view-records',
  '340B-create-records' = '340B-create-records',
  'fms-general' = 'fms-general',
  'fms-view-commission-statements' = 'fms-view-commission-statements',
  'fms-broker-commissions' = 'fms-broker-commissions',
  'fms-view-invoicing' = 'fms-view-invoicing',
  'fms-edit-invoicing' = 'fms-edit-invoicing',
  'fms-edit-plan-fees' = 'fms-edit-plan-fees',
  'portal-rxpa' = 'portal-rxpa',
  'localscripts-view-records' = 'localscripts-view-records',
  'authorization-request-new-users' = 'authorization-request-new-users',
  'bop-view' = 'bop-view',
  'fms-commission-statements-view-only' = 'fms-commission-statements-view-only',
  'vendor-view' = 'vendor-view',
  'vendor-edit' = 'vendor-edit',
  'illuminate-view' = 'illuminate-view',
  'illuminate-edit' = 'illuminate-edit',
  'ch-manage-import-tasks' = 'ch-manage-import-tasks',
  'ch-manage-export-tasks' = 'ch-manage-export-tasks',
  'ch-view-exports' = 'ch-view-exports',
  'ch-manage-export' = 'ch-manage-export',
  'ch-manage-reconciliation' = 'ch-manage-reconciliation',
  'ch-support-functions' = 'ch-support-functions',
  '' = '',
}

export const FESafePerms = {
  [PERMISSIONS_ENUM['authorization-add-users']]: PERM_NICE_NAME['addUsers'],
  [PERMISSIONS_ENUM['authorization-view-users']]: PERM_NICE_NAME['viewUsers'],
  [PERMISSIONS_ENUM['portal-delete-files']]: PERM_NICE_NAME['deleteFiles'],
  [PERMISSIONS_ENUM['portal-upload-files']]: PERM_NICE_NAME['uploadFiles'],
  [PERMISSIONS_ENUM['portal-view-files']]: PERM_NICE_NAME['viewFile'],
  [PERMISSIONS_ENUM['portal-view-financial-claim-detail']]:
    PERM_NICE_NAME['claimDetail'],
  [PERMISSIONS_ENUM['portal-view-financial-claim-summary']]:
    PERM_NICE_NAME['claimSummary'],
  [PERMISSIONS_ENUM['portal-view-financial-invoice']]:
    PERM_NICE_NAME['financialInvoice'],
  [PERMISSIONS_ENUM['portal-view-financial-invoice-claim-export']]:
    PERM_NICE_NAME['invoiceClaimExport'],
  [PERMISSIONS_ENUM['portal-view-organizations']]:
    PERM_NICE_NAME['viewOrganization'],
  [PERMISSIONS_ENUM['authorization-developer']]: PERM_NICE_NAME['developer'],
  [PERMISSIONS_ENUM['fms-broker-commissions']]:
    PERM_NICE_NAME['brokerCommissions'],
  [PERMISSIONS_ENUM['fms-edit-invoicing']]: PERM_NICE_NAME['editInvoicing'],
  [PERMISSIONS_ENUM['fms-edit-plan-fees']]: PERM_NICE_NAME['planFees'],
  [PERMISSIONS_ENUM['fms-general']]: PERM_NICE_NAME['general'],
  [PERMISSIONS_ENUM['fms-view-commission-statements']]:
    PERM_NICE_NAME['commissionStatement'],
  [PERMISSIONS_ENUM['fms-view-invoicing']]: PERM_NICE_NAME['viewInvoicing'],
  [PERMISSIONS_ENUM['portal-edit-eligibility']]:
    PERM_NICE_NAME['editEligibility'],
  [PERMISSIONS_ENUM['portal-view-organization-users']]:
    PERM_NICE_NAME['viewOrganizationUsers'],
  [PERMISSIONS_ENUM['protect-view-cds']]: PERM_NICE_NAME['viewCds'],
  [PERMISSIONS_ENUM['reporting-view-reports']]: PERM_NICE_NAME['viewReports'],
  [PERMISSIONS_ENUM['portal-view-members']]: PERM_NICE_NAME['viewMembers'],
  [PERMISSIONS_ENUM['portal-edit-members']]: PERM_NICE_NAME['editMembers'],
  [PERMISSIONS_ENUM['340B-view-records']]: PERM_NICE_NAME['viewRecord'],
  [PERMISSIONS_ENUM['340B-create-records']]: PERM_NICE_NAME['createRecord'],
  [PERMISSIONS_ENUM['portal-rxpa']]: PERM_NICE_NAME['viewRxPa'],
  [PERMISSIONS_ENUM['localscripts-view-records']]:
    PERM_NICE_NAME['viewLocalscripts'],
  [PERMISSIONS_ENUM['authorization-request-new-users']]:
    PERM_NICE_NAME['newUserRequest'],
  [PERMISSIONS_ENUM['bop-view']]: PERM_NICE_NAME['viewBop'],
  [PERMISSIONS_ENUM['fms-commission-statements-view-only']]:
    PERM_NICE_NAME['fmsCommissionStatementViewOnly'],
  [PERMISSIONS_ENUM['vendor-view']]: PERM_NICE_NAME['viewVendors'],
  [PERMISSIONS_ENUM['vendor-edit']]: PERM_NICE_NAME['editVendors'],
  [PERMISSIONS_ENUM['illuminate-view']]: PERM_NICE_NAME['illuminateView'],
  [PERMISSIONS_ENUM['illuminate-edit']]: PERM_NICE_NAME['illuminateEdit'],
  [PERMISSIONS_ENUM['ch-manage-import-tasks']]:
    PERM_NICE_NAME['chManageImportTasks'],
  [PERMISSIONS_ENUM['ch-manage-export-tasks']]:
    PERM_NICE_NAME['chManageExportTasks'],
  [PERMISSIONS_ENUM['ch-view-exports']]: PERM_NICE_NAME['chViewExports'],
  [PERMISSIONS_ENUM['ch-manage-export']]: PERM_NICE_NAME['chManageExport'],
  [PERMISSIONS_ENUM['ch-manage-reconciliation']]:
    PERM_NICE_NAME['chManageReconciliation'],
  [PERMISSIONS_ENUM['ch-support-functions']]:
    PERM_NICE_NAME['chSupportFunctions'],
};
