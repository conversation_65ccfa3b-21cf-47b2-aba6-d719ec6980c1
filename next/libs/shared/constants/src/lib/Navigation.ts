import { PERMISSIONS_ENUM } from './PermissionsBE';
export const NAV_LINKS = [
  {
    name: 'Organizations',
    slug: 'clients-members',
    subLinks: [
      {
        name: 'Organizations',
        slug: 'organizations',
        perm: [PERMISSIONS_ENUM['portal-view-organizations']],
      },
      {
        name: 'Members',
        slug: 'members',
        perm: [
          PERMISSIONS_ENUM['portal-view-members'],
          PERMISSIONS_ENUM['portal-edit-members'],
        ],
      },
      //Do not delete the commented code, it is hidden temporarily.
      // {
      //   name: 'Member Change History',
      //   slug: 'member-change-history',
      //   perm: [PERMISSIONS_ENUM['portal-edit-eligibility']],
      // },
      {
        name: 'Files',
        slug: 'files',
        perm: [
          PERMISSIONS_ENUM['portal-view-files'],
          PERMISSIONS_ENUM['portal-upload-files'],
          PERMISSIONS_ENUM['portal-delete-files'],
        ],
      },
    ],
  },
  {
    name: 'Reports',
    slug: 'reports', //reports-dashboard/report',
    perm: [PERMISSIONS_ENUM['reporting-view-reports']],
    hasIndex: true,
  },
  {
    name: 'RxPharmacy Assurance',
    slug: 'pharmacy-assurance',
    perm: [PERMISSIONS_ENUM['portal-rxpa']],
  },
  {
    name: 'Financials',
    slug: 'financials',
    subLinks: [
      {
        name: 'General',
        slug: 'general',
        perm: [PERMISSIONS_ENUM['fms-general']],
      },
      {
        name: 'Broker Commissions',
        slug: 'broker-commissions',
        perm: [PERMISSIONS_ENUM['fms-broker-commissions']],
      },
      {
        name: 'Commission Statements',
        slug: 'commission-statements',
        perm: [
          PERMISSIONS_ENUM['fms-broker-commissions'],
          PERMISSIONS_ENUM['fms-view-commission-statements'],
        ],
      },
      {
        name: 'Invoicing',
        slug: 'invoicing',
        perm: [
          PERMISSIONS_ENUM['fms-edit-invoicing'],
          PERMISSIONS_ENUM['fms-view-invoicing'],
        ],
      },
    ],
  },
  {
    name: 'Data Management',
    slug: 'integrations', // Keeping slug as 'integrations' for backward compatibility with existing routes
    subLinks: [
      {
        name: 'Eligibility Imports',
        slug: 'eligibility-imports',
        perm: [PERMISSIONS_ENUM['portal-edit-eligibility']],
      },
      {
        name: 'Eligibility Import Tasks',
        slug: `eligibility-import-task-summary?page=1&size=10&processStatus=2%2C9&reviewStatus=0&autoPopulate=completed_tasks_requiring_review&vendorNo=0`,
        perm: [PERMISSIONS_ENUM['portal-edit-eligibility']],
      },
      {
        name: 'Pharmacy Claim Import Tasks',
        slug: 'pharmacy-claim-import-tasks',
        perm: [PERMISSIONS_ENUM['ch-manage-import-tasks']],
      },
      {
        name: 'Pharmacy Claim Export Tasks',
        slug: 'pharmacy-claim-export-tasks',
        perm: [PERMISSIONS_ENUM['ch-manage-export-tasks']],
      },
      {
        name: 'Pharmacy Claim Exports',
        slug: 'pharmacy-claim-exports',
        perm: [
          PERMISSIONS_ENUM['ch-view-exports'],
          PERMISSIONS_ENUM['ch-manage-export'],
        ],
      },
      {
        name: 'Claim Reconciliation',
        slug: 'claim-reconciliation',
        perm: [PERMISSIONS_ENUM['ch-manage-reconciliation']],
      },
    ],
  },
  {
    name: 'Protect CDS',
    slug: 'protect',
    // setting slug(s) for subLinks that don't have a page yet to "/" for the time being
    subLinks: [
      {
        name: 'Configuration',
        slug: 'config/conditions',
        perm: [PERMISSIONS_ENUM['protect-view-cds']],
      },
      {
        name: 'Intervention Queue',
        slug: 'cases/prospective',
        perm: [PERMISSIONS_ENUM['protect-view-cds']],
      },
    ],
  },
  {
    name: 'Ben Admin',
    slug: 'ben-admin', //reports-dashboard/report',
    perm: [PERMISSIONS_ENUM['bop-view']],
  },
  {
    name: 'Price Book',
    slug: 'price-book',
    subLinks: [
      {
        name: 'Parameter Management',
        slug: 'parameter-management',
        perm: [PERMISSIONS_ENUM['authorization-developer']],
      },
      {
        name: 'Upload Price Record',
        slug: 'upload-price-record',
        perm: [PERMISSIONS_ENUM['authorization-developer']],
      },
      {
        name: 'Price Record Search',
        slug: 'product-management',
        perm: [PERMISSIONS_ENUM['authorization-developer']],
      },
    ],
  },
  {
    name: 'IlluminateRx FSA',
    slug: 'irsv',
    perm: [
      PERMISSIONS_ENUM['illuminate-view'],
      PERMISSIONS_ENUM['illuminate-edit'],
      PERMISSIONS_ENUM['authorization-developer'],
    ],
  },
  {
    name: 'Admin Tools',
    slug: 'admin-tools',
    subLinks: [
      {
        name: 'User Management',
        slug: 'user-management',
        perm: [PERMISSIONS_ENUM['authorization-view-users']],
      },
      {
        name: 'Manage Broker Entities',
        slug: 'manage-broker-entities',
        perm: [PERMISSIONS_ENUM['fms-broker-commissions']],
      },
      {
        name: 'Commissions Access',
        slug: 'CommissionsAccess',
        perm: [PERMISSIONS_ENUM['fms-broker-commissions']],
      },
      {
        name: '340B',
        slug: '340b',
        perm: [
          PERMISSIONS_ENUM['340B-view-records'],
          PERMISSIONS_ENUM['340B-create-records'],
        ],
      },
      {
        name: 'Localscripts',
        slug: 'localscripts',
        perm: [PERMISSIONS_ENUM['localscripts-view-records']],
      },
      {
        name: 'User Account Request',
        slug: 'new-user-request',
        perm: [PERMISSIONS_ENUM['authorization-request-new-users']],
      },
      {
        name: 'Vendors',
        slug: 'vendors',
        perm: [
          PERMISSIONS_ENUM['vendor-view'],
          PERMISSIONS_ENUM['vendor-edit'],
        ],
      },
    ],
  },
];
