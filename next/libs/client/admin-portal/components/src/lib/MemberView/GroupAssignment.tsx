'use client';
import { ChevronDownIcon, ChevronRightIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  createStandaloneToast,
  Divider,
  Flex,
  FormControl,
  FormHelperText,
  FormLabel,
  Input,
  InputGroup,
  Select,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { useApi } from '@next/shared/api';
import { formatDate } from '@next/shared/helpers';
import { useCustomToast } from '@next/shared/hooks';
import { DataTable as Table } from '@next/shared/ui';
import { AccordionWrapper } from '@next/shared/ui';
import { createColumnHelper } from '@tanstack/react-table';
import { theme } from 'libs/client/admin-portal/constants/src/lib/theme';
import moment from 'moment';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  AiOutlineExclamationCircle,
  AiOutlineQuestionCircle,
} from 'react-icons/ai';

import { GenericModal } from '../Global/GenericModal';
import { InputError } from '../Global/InputError';
interface GroupData {
  benefitGroupNo: number;
  benefitGroupId: string;
  name: string;
  processStatus: string;
  dependentAlternateGroupNumber: string | null;
  effectiveDate: string;
  expirationDate: string;
  assignmentDate: string;
  isAddDivision: boolean;
  groupDivision: string;
  divisionName: string;
  divisionId: string | number;
  changeDate: string;
}

interface BenefitGroup {
  assignmentDate: string;
  benefitGroupId: string;
  contractNumber: string;
  effectiveDate: string;
  employeeBenefitGroupNo: number;
  expirationDate: string | null;
  groupNumber: string;
  name: string;
  planName: string;
  recordType: number;
}
export type GroupAssignmentDataProps<Data extends Record<string, any>> = {
  data: Data[];
  groupData: Data[];
  apiInstance: Record<string, any>;
};

export function MembersViewGroupAssignment<Data extends GroupData>({
  data,
  groupData = [],
  apiInstance,
}: GroupAssignmentDataProps<Data>) {
  const [updatedDivisions, setUpdatedDivisions] = useState([]);

  const [saveModal, setSaveModal] = useState(false);

  let dataRendered: GroupData | null = null;
  if (Array.isArray(data) && data.length > 0) {
    [dataRendered] = data;
  }

  const params = useParams();
  const orgId = params?.organizationId;
  const empId = params?.employeeId;

  const { divisions = [] } = useApi(['divisions'], {
    orgId: orgId as string | number,
    empId: empId as string | number,
  });

  const showToast = useCustomToast();

  const {
    methodApi,
    getApi,
    employeeDetail = {},
    groupDivisions = [],
    organizationDetail = [],
    employeeViewGroupAssignment = [],
  } = apiInstance;

  const {
    register,
    setValue,
    handleSubmit,
    watch,
    reset,
    formState: { isSubmitting, dirtyFields, errors },
  } = useForm<GroupData>({
    defaultValues: {
      processStatus: '',
      benefitGroupId: '',
      effectiveDate: moment().format('YYYY-MM-DD'),
      assignmentDate: moment().format('YYYY-MM-DD'),
      isAddDivision: false,
    },
  });

  const {
    processStatus,
    isAddDivision,
    divisionId,
    divisionName,
    changeDate,
    groupDivision,
    effectiveDate,
  } = watch();

  useEffect(() => {
    if (employeeDetail && Array.isArray(groupDivisions)) {
      const defaultDivision = groupDivisions?.filter((item: any) => {
        return item?.divisionId === employeeDetail?.divisionId;
      });
      setValue('groupDivision', defaultDivision[0]?.divisionId);
    }
  }, [employeeDetail, setValue, groupDivisions]);

  const getUpateDivisions = useCallback(
    (divisions: any) => {
      let updatedDivisions;
      if (organizationDetail?.supportsDivisions === true) {
        updatedDivisions = (Array.isArray(divisions) ? divisions : []).map(
          (division: any) => {
            const updatedHistory = [
              {
                groupName: 'Division(s)',
                groupEffectiveDate: undefined,
                groupAssignmentDate: undefined,
              },
              ...division.divisionHistory.map((history: any) => ({
                ...history,
                groupAssignmentDate: history.divisionAssignmentDate,
                groupEffectiveDate: history.divisionEffectiveDate,
                groupName: `${history.divisionId}-${history.divisionName}`,
              })),
            ];

            return {
              ...division,
              groupName: `${division.groupId}-${division.groupName}`,
              divisionHistory: updatedHistory,
            };
          }
        );
      } else {
        updatedDivisions =
          employeeViewGroupAssignment?.length &&
          employeeViewGroupAssignment
            ?.filter((group: BenefitGroup) => {
              return group.recordType === 1;
            })
            .map((group: BenefitGroup) => ({
              groupName: `${group.benefitGroupId} - ${group.name}`,
              groupEffectiveDate: group.effectiveDate,
              groupAssignmentDate: group.assignmentDate,
            }));
      }
      return updatedDivisions;
    },
    [employeeViewGroupAssignment, organizationDetail?.supportsDivisions]
  );

  useEffect(() => {
    const updatedDivisions = getUpateDivisions(divisions);
    setUpdatedDivisions(updatedDivisions);
  }, [divisions, getUpateDivisions]);

  const fetchData = async () => {
    const currBenefitGroupId = watch('benefitGroupId');
    setValue('isAddDivision', false);
    setValue('groupDivision', '');
    try {
      const grpNo = groupDataView?.filter((each: GroupData) => {
        return each.benefitGroupId === currBenefitGroupId;
      })?.[0]?.benefitGroupNo;
      await getApi('groupDivisions', {
        orgId: orgId,
        groupNo: grpNo,
      });
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const benefitGroupId =
      dataRendered?.benefitGroupId || employeeDetail?.groupId;
    if (benefitGroupId && groupData.length > 0) {
      const groupId = dataRendered?.benefitGroupId || employeeDetail?.groupId;

      const data = groupData.filter(
        (item) => item.benefitGroupId === groupId
      )?.[0];

      reset({
        processStatus: String(data?.processStatus),
        benefitGroupId: data?.benefitGroupId,
        effectiveDate: dataRendered?.effectiveDate,
        assignmentDate: dataRendered?.assignmentDate,
      });
    }
  }, [dataRendered, groupData, reset, employeeDetail]);

  const groupDataView = useMemo(
    () =>
      groupData.filter(
        (item) =>
          processStatus === 'all' ||
          processStatus === String(item.processStatus)
      ),
    [processStatus, groupData]
  );

  useEffect(() => {
    if (processStatus !== 'all')
      setValue(
        'benefitGroupId',
        dataRendered?.benefitGroupId || employeeDetail?.groupId
      );

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupDataView]);

  const columnHelper = createColumnHelper<any>();

  const columns = [
    columnHelper.accessor('groupName', {
      cell: ({ row, getValue }) => {
        return row.original.groupName !== 'Division(s)' ? (
          <Box as="div" display="flex" paddingLeft={`${row.depth * 2}rem`}>
            {row.original.divisionHistory?.length ? (
              <button
                type="button"
                {...{
                  onClick: row.getToggleExpandedHandler(),
                  style: { cursor: 'pointer' },
                }}
              >
                {row.getIsExpanded() ? (
                  <ChevronDownIcon transform={'scale(1.5)'} />
                ) : (
                  <ChevronRightIcon transform={'scale(1.5)'} />
                )}
              </button>
            ) : (
              ''
            )}

            <Text
              textIndent="8px"
              fontSize="14px"
              color="#3182CE"
              fontWeight={row.depth > 0 ? 'normal' : '600'}
            >
              {getValue()}
            </Text>
          </Box>
        ) : (
          <Text fontWeight={'bold'}>{getValue()}</Text>
        );
      },
      header: 'Group',
      enableSorting: false,
    }),
    columnHelper.accessor('groupEffectiveDate', {
      cell: (info) =>
        info.row.original.groupName !== 'Division(s)'
          ? formatDate(info.getValue())
          : null,
      header: 'Effective Date',
      enableSorting: false,
    }),
    columnHelper.accessor('groupAssignmentDate', {
      cell: (info) =>
        info.row.original.groupName !== 'Division(s)'
          ? formatDate(info.getValue())
          : null,
      header: 'Assignment Date',
      enableSorting: false,
    }),
  ];
  const { toast } = createStandaloneToast();

  const isEmpTerminated = () => {
    if (!organizationDetail?.supportsDivisions) {
      if (
        employeeDetail?.employeeExpirationDate !== null &&
        employeeDetail?.employeeExpirationDate < moment().format('YYYY-MM-DD')
      ) {
        return true;
      } else {
        return false;
      }
    }
  };

  const onSubmitHandler = (values: any) => {
    const groupSingleData = groupData.filter(
      (item) => item.benefitGroupId === watch('benefitGroupId')
    );
    const currBenefitGroupId = watch('benefitGroupId');
    const grpNo = groupDataView?.filter((each: GroupData) => {
      return each.benefitGroupId === currBenefitGroupId;
    })?.[0]?.benefitGroupNo;

    if (isAddDivision || !dirtyFields.benefitGroupId) {
      const selecteddivision = Array.isArray(groupDivisions)
        ? groupDivisions.filter((item: any) => {
            return item?.divisionId === groupDivision;
          })
        : [];

      methodApi('addNewDivision', {
        method: 'POST',
        restParams: { orgId: orgId, empId: empId },
        body: {
          divisionEffectiveDate: changeDate,
          divisionId: divisionId ? divisionId : selecteddivision[0]?.divisionId,
          divisionName: divisionName
            ? divisionName
            : selecteddivision[0]?.divisionName,
        },
        onSuccess: () => {
          showToast({
            title: 'Updated Division Successfully',
            status: 'success',
          });

          setSaveModal(false);
          setValue('isAddDivision', false);
          reset({
            ...watch(),
            divisionId: '',
            divisionName: '',
            changeDate: '',
          });

          getApi(
            'divisions',
            {
              orgId: orgId,
              empId: empId,
            },
            {
              onSuccess: (resp: any) => {
                const updatedDivisions1 = getUpateDivisions(resp);
                setUpdatedDivisions(updatedDivisions1);
              },
            }
          );
          getApi('employeeDetail', {
            orgId: orgId,
            empId: empId,
          });
          getApi('groupDivisions', {
            orgId: orgId,
            groupNo: grpNo,
          });
        },
        onError: (resp: any) => {
          setSaveModal(false);
          toast({
            title: resp?.data?.error,
            description: 'Update Employee - Failed',
            status: 'error',
            duration: 3000,
          });
        },
      });
    } else {
      methodApi('updateGroupAssignment', {
        method: 'PATCH',
        restParams: { orgId: orgId, empId: empId },
        body: {
          benefitGroupId: groupSingleData[0].benefitGroupNo,
          changeDate: organizationDetail?.supportsDivisions
            ? changeDate
            : effectiveDate,
          division: {
            divisionId: values.groupDivision,
            divisionName: groupDivisions?.find(
              (each: any) => each.divisionId === values.groupDivision
            )?.divisionName,
          },
        },
        onSuccess: () => {
          showToast({
            title: 'Updated Successfully',
            status: 'success',
          });
          reset({}, { keepValues: true });
          setSaveModal(false);
          getApi(
            'divisions',
            {
              orgId: orgId,
              empId: empId,
            },
            {
              onSuccess: (resp: any) => {
                const updatedDivisions1 = getUpateDivisions(resp);
                setUpdatedDivisions(updatedDivisions1);
              },
            }
          );
          getApi('employeeViewGroupAssignment', {
            orgId: orgId,
            empId: empId,
          });
          getApi('employeeDetail', {
            orgId: orgId,
            empId: empId,
          });
          getApi('employeeViewGroup', {
            orgId: orgId,
            empId: empId,
          });
          setValue('changeDate', '');
        },
        onError: (err: any) => {
          setSaveModal(false);
          toast({
            title: 'Update Employee',
            description:
              err.detail.split('Error: ')[1] || 'Update Employee - Failed',
            status: 'error',
            duration: 3000,
          });
        },
      });
    }
  };

  return (
    <AccordionWrapper title="Group Assignment">
      <Divider
        width={'100%'}
        borderWidth="1px"
        borderColor={theme.colors.brand.lightgrey}
      />
      <form>
        <Flex gap={8} p={4}>
          <FormControl flex={4}>
            <FormLabel margin={'2px'}>
              <Flex gap={2}>
                <FormHelperText color="red">*</FormHelperText>
                Select Status
              </Flex>
            </FormLabel>
            <Select
              placeholder="Select..."
              {...register('processStatus')}
              isDisabled={isEmpTerminated()}
            >
              <option value="1">Active</option>
              <option value="all">All</option>
              <option value="9">Termed</option>
            </Select>
          </FormControl>

          <FormControl flex={4}>
            <Flex gap={2}>
              <FormHelperText color="red">*</FormHelperText>
              <FormLabel margin={'2px'}>Select Group</FormLabel>
              <Tooltip
                label="Plan name chosen by the member to identify the member’s coverage option"
                fontSize="md"
                placement="top"
                hasArrow
              >
                <Flex marginTop={'5px'}>
                  <AiOutlineQuestionCircle />
                </Flex>
              </Tooltip>
            </Flex>

            <Select
              {...register('benefitGroupId', {
                onChange: fetchData,
              })}
              isDisabled={isEmpTerminated()}
            >
              {groupDataView?.map((item: GroupData, index) => {
                return (
                  <option key={index} value={item.benefitGroupId}>
                    {item.benefitGroupId}- {item.name}
                  </option>
                );
              })}
            </Select>
          </FormControl>
        </Flex>

        <Flex gap={8} p={4}>
          <FormControl flex={4}>
            <FormLabel margin={'2px'}>
              <Flex gap={2}>
                <FormHelperText color="red">*</FormHelperText>
                Effective Date
              </Flex>
            </FormLabel>

            <InputGroup>
              <Input
                type="date"
                placeholder="Effective Date"
                {...register('effectiveDate')}
                isDisabled={
                  organizationDetail?.supportsDivisions || isEmpTerminated()
                }
                max="9999-12-31"
              />
            </InputGroup>
          </FormControl>

          <FormControl flex={4}>
            <FormLabel margin={'2px'}>Assignment Date</FormLabel>

            <InputGroup>
              <Input
                placeholder="Assignment Date"
                disabled
                type="date"
                {...register('assignmentDate')}
                variant={'filled'}
              />
            </InputGroup>
          </FormControl>
        </Flex>

        <Divider
          width={'100%'}
          borderWidth="1px"
          borderColor={theme.colors.brand.lightgrey}
          mb={'4'}
        />
        {organizationDetail?.supportsDivisions && (
          <>
            <Flex gap={2}>
              {!isAddDivision ? (
                <FormControl flex={8} isRequired>
                  <FormLabel>Division</FormLabel>
                  <Select
                    {...register('groupDivision', {
                      required: {
                        value: true,
                        message: 'Group Division is required',
                      },
                    })}
                  >
                    {(Array.isArray(groupDivisions) ? groupDivisions : []).map(
                      (item: any, index: number) => {
                        return (
                          <option key={index} value={item.divisionId}>
                            {item.divisionId} - {item.divisionName}
                          </option>
                        );
                      }
                    )}
                  </Select>
                </FormControl>
              ) : (
                <Flex gap={2}>
                  <FormControl isRequired flex={4}>
                    <FormLabel>Division ID</FormLabel>
                    <Input
                      {...register('divisionId', {
                        required: {
                          value: true,
                          message: 'Division ID is required',
                        },
                      })}
                    />
                  </FormControl>

                  <FormControl isRequired flex={4}>
                    <FormLabel>Division Name</FormLabel>
                    <Input
                      {...register('divisionName', {
                        required: {
                          value: true,
                          message: 'Division Name is required',
                        },
                      })}
                    />
                  </FormControl>
                </Flex>
              )}
              <FormControl flex={4} isReadOnly>
                <FormLabel>Effective Date</FormLabel>
                <Input
                  type="date"
                  value={employeeDetail?.divisionEffectiveDate}
                />
              </FormControl>
            </Flex>
            <Flex p={4}>
              {!isAddDivision && (
                <Button
                  variant={'ghost'}
                  color={'#3182CE'}
                  fontWeight={600}
                  onClick={() => setValue('isAddDivision', true)}
                  style={{ padding: 0 }}
                >
                  + Add New Division
                </Button>
              )}
              {isAddDivision && (
                <div>
                  Current Division:{' '}
                  {`${employeeDetail?.divisionId} - ${employeeDetail?.divisionName}`}
                </div>
              )}
            </Flex>

            <Divider
              width={'100%'}
              borderWidth="1px"
              borderColor={theme.colors.brand.lightgrey}
              mb={'4'}
            />
            <FormControl w={'fit-content'} isRequired>
              <FormLabel>Change Date</FormLabel>
              <Input
                type="date"
                {...register('changeDate', {
                  required: { value: true, message: 'this field is required' },
                })}
                width={200}
              />
              {errors.changeDate?.message && (
                <InputError message={errors.changeDate?.message} />
              )}
            </FormControl>
          </>
        )}

        <Divider
          width={'100%'}
          borderWidth="1px"
          borderColor={theme.colors.brand.lightgrey}
          mb={'4'}
        />

        <AccordionWrapper title="Group Assignment History">
          <Table
            data={updatedDivisions}
            columns={columns}
            headerBackground="#ececec"
            subRow="divisionHistory"
          />
        </AccordionWrapper>

        <Divider
          width={'100%'}
          borderWidth="1px"
          borderColor={theme.colors.brand.lightgrey}
          mb={'4'}
        />

        <GenericModal
          title=""
          onClose={() => setSaveModal(false)}
          isOpen={saveModal}
          size={'lg'}
        >
          <Flex ml={12} gap={2}>
            <Text fontSize={'2xl'} fontWeight={'extrabold'} mb={2}>
              <AiOutlineExclamationCircle color="#fca447" />
            </Text>
            <Text fontSize={'2xl'} fontWeight={'extrabold'} mb={2}>
              Change Group Assignment
            </Text>
          </Flex>
          <Box textAlign={'center'}>
            <Text fontSize={'lg'}>Change the Group assignment?</Text>
            <Text fontSize={'lg'} fontWeight={'bold'} color={'#f41212'}>
              NOTE: This may affect the Members <br />
              Coverage.
            </Text>
          </Box>
          <Flex justifyContent={'end'}>
            <Button m={2} onClick={() => setSaveModal(false)}>
              Cancel
            </Button>
            <Button
              m={2}
              variant="solid"
              colorScheme="brand"
              bg="brand.700"
              type="submit"
              onClick={handleSubmit(onSubmitHandler)}
            >
              Ok
            </Button>
          </Flex>
        </GenericModal>

        <Flex gap={3} justifyContent={'end'}>
          <Button
            type="button"
            onClick={() => {
              reset();
              fetchData();
            }}
            isDisabled={
              !dirtyFields.benefitGroupId &&
              !dirtyFields.groupDivision &&
              !dirtyFields.divisionId &&
              !isAddDivision
            }
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(
              (data) => {
                setSaveModal(true);
              },
              (errors) => {
                if (Object.keys(errors).length > 0) {
                  toast({
                    title: 'Validation Error',
                    description: 'Please fill in all required fields',
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                  });
                }
              }
            )}
            isLoading={isSubmitting}
            variant="solid"
            colorScheme="brand"
            bg="brand.700"
            isDisabled={
              !dirtyFields.benefitGroupId &&
              !dirtyFields.groupDivision &&
              !dirtyFields.divisionId
            }
          >
            Save
          </Button>
        </Flex>
      </form>
    </AccordionWrapper>
  );
}
