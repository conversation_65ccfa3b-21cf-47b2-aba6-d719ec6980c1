import {
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { GET_COVERAGE_TEMP_CARD_DATA } from '@next/admin/constants';
import { formatDate } from '@next/shared/helpers';
import { useCustomText, useCustomToast } from '@next/shared/hooks';
import { CoverageData } from '@next/shared/types';
import { AccordionWrapper } from '@next/shared/ui';
import { theme } from 'libs/client/admin-portal/constants/src/lib/theme';
import { useParams } from 'next/navigation';
import { AiOutlineQuestionCircle } from 'react-icons/ai';

import { AlertBox } from '../Global/AlertBox';
import { GenerateTempCard } from './Coverage/GenerateTempCard';

export type MemberViewCoverageProps<Data extends Record<string, any>> = {
  data: Data[];
  apiInstance: Record<string, any>;
  tempCardInfo: Record<string, any>;
};

export const MemberViewCoverage = <Data extends CoverageData>({
  data,
  apiInstance,
  tempCardInfo,
}: MemberViewCoverageProps<Data>) => {
  const showToast = useCustomToast();
  const params = useParams();
  const orgId = params?.['organizationId'];
  const empId = params?.['employeeId'];

  const createCustomText = useCustomText();
  const { methodApi, getApi, resetData } = apiInstance;

  const isFutureTerminated = (expirationDate: string) => {
    const currentDate = new Date();
    const isFutureTermination =
      expirationDate && new Date(expirationDate) > currentDate;

    return isFutureTermination;
  };

  if (tempCardInfo) {
    GET_COVERAGE_TEMP_CARD_DATA?.forEach((field: any) => {
      const { fieldName } = field;
      if (fieldName in tempCardInfo) {
        field.defaultValue = tempCardInfo[fieldName];
      }
    });
  }

  const GenericFormLabel = ({
    formLabel,
    toolTipLabel,
  }: {
    formLabel: string;
    toolTipLabel: string;
  }) => {
    return (
      <FormLabel margin={'2px'}>
        <Flex gap={2}>
          {formLabel}
          <Tooltip label={toolTipLabel} fontSize="md" placement="top" hasArrow>
            <Flex alignItems={'center'}>
              <AiOutlineQuestionCircle />
            </Flex>
          </Tooltip>
        </Flex>
      </FormLabel>
    );
  };

  const CustomField = ({
    formLabel,
    toolTipLabel,
    value,
  }: {
    formLabel: string;
    toolTipLabel: string;
    value: string;
  }) => {
    return (
      <FormControl flex={4}>
        <GenericFormLabel formLabel={formLabel} toolTipLabel={toolTipLabel} />
        <Input value={value} disabled />
      </FormControl>
    );
  };

  const newCardRequestHandler = (coverageNo: number | string) => {
    methodApi('employeeNewCardRequest', {
      method: 'POST',
      restParams: { orgId, empId, coverageNo },
      onSuccess: () => {
        resetData('employeeViewCoverages');
        showToast({
          title: 'Requested a new card',
          status: 'success',
        });
        getApi('employeeViewCoverages', { orgId, empId });
      },
      onError: () =>
        showToast({
          title: 'Failed to Request a new card',
          status: 'error',
        }),
    });
  };

  return (
    <AccordionWrapper title="Coverage">
      {data?.length > 0 ? (
        data?.map((item: CoverageData) => (
          <div key={item.coverageNo}>
            <Divider
              width={'100%'}
              borderWidth="1px"
              borderColor={theme.colors.brand.lightgrey}
            />
            <Flex
              gap={8}
              p={4}
              justifyContent={'space-between'}
              alignItems={'center'}
            >
              <h1 style={{ fontWeight: 'bold', margin: '5px' }}>
                {item?.name}
              </h1>
              <Flex gap={2}>
                <h2>Alternate ID</h2>
                <Tooltip
                  label="ID configured as employee's alternate ID: Employee Client ID, Generated ID, or SSN"
                  fontSize="md"
                  placement="top"
                  hasArrow
                >
                  <Flex alignItems={'center'}>
                    <AiOutlineQuestionCircle />:
                  </Flex>
                </Tooltip>
                {item?.alternateID}
              </Flex>
            </Flex>

            {(!item?.employeeExpirationDate ||
              isFutureTerminated(item?.employeeExpirationDate)) && (
              <Flex gap={2}>
                {item?.supportsTempCard &&
                  (item?.expirationDate === null ||
                    isFutureTerminated(item?.expirationDate)) && (
                    <GenerateTempCard tempCardInfo={tempCardInfo} />
                  )}
                {item?.supportsNewCardRequest &&
                  (item?.expirationDate === null ||
                    isFutureTerminated(item?.expirationDate)) && (
                    <AlertBox
                      isButtonDisabled={item?.newCard}
                      alertButtonText={'Request New Card'}
                      approveButtonHandler={() =>
                        newCardRequestHandler(item?.coverageNo)
                      }
                      headerText={'Request New Card'}
                      bodyText={[
                        'Are you sure you want to request a new card?',
                        'NOTE: This action cannot be undone.',
                      ]}
                      approveButtonText={'OK'}
                      rejectButtonText={'Cancel'}
                      buttonTextColor="brand.700"
                      buttonBorderColor="brand.700"
                    />
                  )}
                {item?.supportsNewCardRequest &&
                  item?.newCard &&
                  (item?.expirationDate === null ||
                    isFutureTerminated(item?.expirationDate)) &&
                  createCustomText({
                    processStatus: 2,
                    text: 'New Card Requested',
                  })}
              </Flex>
            )}
            <Flex gap={8} p={4}>
              <CustomField
                formLabel=" HDHP?"
                toolTipLabel="High-deductible health plan"
                value={item?.hdhpIndicator ? 'Yes' : 'No'}
              />

              <CustomField
                formLabel="Coverage Period"
                toolTipLabel="Beginning (From) and end (To) dates for employee’s coverage. To date is not populated if no termination date is present."
                value={`From ${formatDate(item?.effectiveDate)}${
                  item?.expirationDate
                    ? ` to ${formatDate(item.expirationDate)}`
                    : ''
                }`}
              />
            </Flex>

            <Flex gap={8} p={4}>
              <CustomField
                formLabel="Vendor Group Number"
                toolTipLabel="Group number assigned to employee based on group assignment"
                value={item?.groupNumber}
              />
              <CustomField
                formLabel="Group"
                toolTipLabel="Current employee group assignment"
                value={`${item?.groupId} - ${item?.groupName}`}
              />
            </Flex>
            <Flex gap={8} p={4}>
              {item?.contractNumber !== null && (
                <CustomField
                  formLabel="Contract Number"
                  toolTipLabel="Contract Number"
                  value={item?.contractNumber ?? ''}
                />
              )}
            </Flex>
          </div>
        ))
      ) : (
        <Text mt={'2'}>There are no coverages assigned to this member.</Text>
      )}
    </AccordionWrapper>
  );
};
